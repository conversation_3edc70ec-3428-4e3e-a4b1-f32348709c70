<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的产品详情对话框演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        .demo-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* 详情对话框样式 */
        .detail-container {
            max-height: 70vh;
            overflow-y: auto;
        }

        .detail-card {
            margin-bottom: 20px;
        }

        .detail-card:last-child {
            margin-bottom: 0;
        }

        .card-header {
            display: flex;
            align-items: center;
        }

        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-left: 8px;
        }

        .card-icon {
            font-size: 18px;
            color: #409EFF;
        }

        .category-summary {
            margin-left: auto;
        }

        .detail-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .detail-label {
            font-weight: 500;
            color: #606266;
            min-width: 120px;
            margin-right: 10px;
        }

        .detail-value {
            color: #303133;
            flex: 1;
        }

        .detail-value.highlight {
            font-weight: bold;
            color: #409EFF;
        }

        .status-item {
            margin-bottom: 15px;
        }

        .status-label {
            font-weight: 500;
            color: #606266;
            margin-right: 10px;
            display: inline-block;
            min-width: 140px;
        }

        .status-tip {
            margin-top: 5px;
            padding: 8px 12px;
            background-color: #f4f4f5;
            border-radius: 4px;
            font-size: 12px;
            color: #909399;
            line-height: 1.4;
        }

        .status-tip i {
            margin-right: 5px;
            color: #409EFF;
        }

        .remark-content {
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            line-height: 1.6;
            color: #606266;
        }

        /* 最终结果卡片样式 */
        .final-result-card {
            border: 2px solid #E6F7FF;
            background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
        }

        .final-status-container {
            padding: 10px 0;
        }

        .final-status-item {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.8);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .final-status-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
        }

        .final-status-header i {
            font-size: 20px;
            color: #409EFF;
            margin-right: 8px;
        }

        .final-status-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
        }

        .final-status-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .final-status-tag {
            font-size: 16px !important;
            padding: 12px 24px !important;
            font-weight: bold;
            border-radius: 20px !important;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .final-status-tip {
            margin-top: 10px;
            padding: 8px 12px;
            background-color: rgba(64, 158, 255, 0.1);
            border-radius: 4px;
            font-size: 12px;
            color: #606266;
            line-height: 1.4;
            max-width: 200px;
            text-align: left;
        }

        .final-status-tip i {
            margin-right: 5px;
            color: #409EFF;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <h1>优化后的产品详情对话框演示</h1>
            <p>点击产品名称查看按分类组织的详细信息</p>
            
            <el-table :data="tableData" border style="width: 100%">
                <el-table-column prop="productCode" label="产品代码" width="120"></el-table-column>
                <el-table-column label="产品名称" width="150" align="center">
                    <template slot-scope="scope">
                        <el-button
                            type="text"
                            @click="handleViewDetail(scope.row)"
                            style="color: #409EFF; font-weight: bold;"
                        >
                            {{ scope.row.productName }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="manufacturer" label="生产企业" width="150"></el-table-column>
                <el-table-column prop="spec" label="规格"></el-table-column>
            </el-table>

            <!-- 产品详情查看对话框 -->
            <el-dialog 
                title="产品详细信息" 
                :visible.sync="detailOpen" 
                width="85%" 
                append-to-body
                :close-on-click-modal="false"
            >
                <div class="detail-container">
                    <!-- 基本信息 -->
                    <el-card class="detail-card" shadow="never">
                        <div slot="header" class="card-header">
                            <i class="el-icon-info card-icon"></i>
                            <span class="card-title">基本信息</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="detail-item">
                                    <span class="detail-label">产品名称：</span>
                                    <span class="detail-value highlight">{{ detailData.productName }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="detail-item">
                                    <span class="detail-label">产品代码：</span>
                                    <span class="detail-value">{{ detailData.productCode }}</span>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="detail-item">
                                    <span class="detail-label">生产企业：</span>
                                    <span class="detail-value">{{ detailData.manufacturer }}</span>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 配置工艺确认 -->
                    <el-card class="detail-card" shadow="never">
                        <div slot="header" class="card-header">
                            <i class="el-icon-setting card-icon"></i>
                            <span class="card-title">配置工艺确认</span>
                            <div class="category-summary">
                                <el-tag size="mini" type="info">配方相关检查项</el-tag>
                            </div>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="status-item">
                                    <span class="status-label">配方稳定性报告：</span>
                                    <el-tag type="success" size="small">存在</el-tag>
                                    <div class="status-tip">
                                        <i class="el-icon-info"></i>
                                        配方稳定性测试已通过，符合质量要求
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="status-item">
                                    <span class="status-label">配制可行性评估：</span>
                                    <el-tag type="success" size="small">存在</el-tag>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 生产出库信息 -->
                    <el-card class="detail-card final-result-card" shadow="never">
                        <div slot="header" class="card-header">
                            <i class="el-icon-truck card-icon"></i>
                            <span class="card-title">生产出库信息</span>
                            <div class="category-summary">
                                <el-tag size="mini" type="warning">最终结果</el-tag>
                            </div>
                        </div>
                        <div class="final-status-container">
                            <el-row :gutter="30">
                                <el-col :span="12">
                                    <div class="final-status-item">
                                        <div class="final-status-header">
                                            <i class="el-icon-goods"></i>
                                            <span class="final-status-title">生产状态</span>
                                        </div>
                                        <div class="final-status-content">
                                            <el-tag type="success" size="large" class="final-status-tag">
                                                ✓ 可生产
                                            </el-tag>
                                            <div class="final-status-tip">
                                                <i class="el-icon-info"></i>
                                                所有前置条件已满足，可以开始批量生产
                                            </div>
                                        </div>
                                    </div>
                                </el-col>
                                <el-col :span="12">
                                    <div class="final-status-item">
                                        <div class="final-status-header">
                                            <i class="el-icon-box"></i>
                                            <span class="final-status-title">出库状态</span>
                                        </div>
                                        <div class="final-status-content">
                                            <el-tag type="success" size="large" class="final-status-tag">
                                                ✓ 可出库
                                            </el-tag>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-card>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="detailOpen = false">关 闭</el-button>
                </div>
            </el-dialog>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    detailOpen: false,
                    detailData: {},
                    tableData: [
                        {
                            productCode: 'ABC123',
                            productName: '高效洗发水',
                            manufacturer: '美丽化妆品有限公司',
                            spec: '500ml'
                        }
                    ]
                }
            },
            methods: {
                handleViewDetail(row) {
                    this.detailData = { ...row };
                    this.detailOpen = true;
                }
            }
        })
    </script>
</body>
</html>
