<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="产品代码" prop="productCode">
        <el-input
          v-model="queryParams.productCode"
          placeholder="请输入产品代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="实验室编号" label-width="100px" prop="laboratoryCode">
        <el-select
          v-model="queryParams.laboratoryCode"
          placeholder="请选择实验室编号"
          clearable
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="生产企业" prop="manufacturer">
        <el-select
          v-model="queryParams.manufacturer"
          placeholder="请选择生产企业"
          clearable
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入产品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预计生产日期" label-width="110px" prop="plannedProductionDate">
        <el-date-picker
          clearable
          v-model="queryParams.plannedProductionDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择预计生产日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品交期" prop="deliveryDate">
        <el-date-picker
          clearable
          v-model="queryParams.deliveryDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择产品交期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="产品类型" prop="productType">
        <el-select
          v-model="queryParams.productType"
          placeholder="请选择产品类型"
          clearable
        >
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
      <el-form-item label="备案号" prop="registrationCompletionHoverTip">
        <el-input
          v-model="queryParams.registrationCompletionHoverTip"
          placeholder="请输入备案号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="是否可生产" prop="canProduce">
        <el-input
          v-model="queryParams.canProduce"
          placeholder="请输入是否可生产"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="是否可出库" prop="canDeliver">
        <el-input
          v-model="queryParams.canDeliver"
          placeholder="请输入是否可出库"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['qc:audit:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['qc:audit:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['qc:audit:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['qc:audit:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="auditList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键ID" align="center" prop="id" /> -->
      <el-table-column label="产品ID" align="center" prop="productId" />
      <el-table-column label="产品代码" align="center" prop="productCode" />
      <el-table-column
        label="实验室编号"
        align="center"
        prop="laboratoryCode"
      />
      <el-table-column label="生产企业" align="center" prop="manufacturer" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="规格" align="center" prop="spec" />
      <el-table-column
        label="预计生产日期"
        align="center"
        prop="plannedProductionDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.plannedProductionDate, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单数量" align="center" prop="orderQuantity" />
      <el-table-column
        label="产品交期"
        align="center"
        prop="deliveryDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.deliveryDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="产品类型" align="center" prop="productType" />
      <el-table-column
        label="配方稳定性报告状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.formulaStabilityReportHoverTip"
            :content="scope.row.formulaStabilityReportHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.formulaStabilityReport == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.formulaStabilityReport == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.formulaStabilityReport == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.formulaStabilityReport == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="配制可行性评估状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.formulaFeasibilityAssessmentHoverTip"
            :content="scope.row.formulaFeasibilityAssessmentHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.formulaFeasibilityAssessment == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.formulaFeasibilityAssessment == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.formulaFeasibilityAssessment == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.formulaFeasibilityAssessment == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="标准配制工艺单状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.standardFormulaProcess == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.standardFormulaProcess == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="生产模具治具确认状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.moldToolConfirmationHoverTip"
            :content="scope.row.moldToolConfirmationHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.moldToolConfirmation == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.moldToolConfirmation == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.moldToolConfirmation == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.moldToolConfirmation == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="灌包可行性评估状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.fillingPackagingFeasibilityHoverTip"
            :content="scope.row.fillingPackagingFeasibilityHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.fillingPackagingFeasibility == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.fillingPackagingFeasibility == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.fillingPackagingFeasibility == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.fillingPackagingFeasibility == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="灌装/包装SOP状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.fillingPackagingSop == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.fillingPackagingSop == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="成品检验标准状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.finishedProductStandard == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.finishedProductStandard == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="质量协议状态"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.qualityAgreementHoverTip"
            :content="scope.row.qualityAgreementHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.qualityAgreement == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.qualityAgreement == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.qualityAgreement == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.qualityAgreement == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="包材标准状态"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.packagingMaterialStandard == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.packagingMaterialStandard == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="料体标样状态"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.liquidSample == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.liquidSample == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="包材标准样状态"
        align="center"
        width="130"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.packagingMaterialSample == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.packagingMaterialSample == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="成品标样状态"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.finishedProductSample == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.finishedProductSample == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="过度包装确认状态"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.excessivePackagingConfirmationHoverTip"
            :content="scope.row.excessivePackagingConfirmationHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.excessivePackagingConfirmation == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.excessivePackagingConfirmation == 1 ? '存在' : '不存在' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.excessivePackagingConfirmation == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.excessivePackagingConfirmation == 1 ? '存在' : '不存在' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="注册备案完成状态"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.registrationCompletionHoverTip"
            :content="'备案号：' + scope.row.registrationCompletionHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.registrationCompletion == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.registrationCompletion == 1 ? '已完成' : '未完成' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.registrationCompletion == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.registrationCompletion == 1 ? '已完成' : '未完成' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="配方工艺一致性状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.formulaProcessConsistencyHoverTip"
            :content="scope.row.formulaProcessConsistencyHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.formulaProcessConsistency == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.formulaProcessConsistency == 1 ? '一致' : '不一致' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.formulaProcessConsistency == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.formulaProcessConsistency == 1 ? '一致' : '不一致' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="文案资料一致性状态"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.documentationConsistencyHoverTip"
            :content="scope.row.documentationConsistencyHoverTip"
            placement="top"
          >
            <el-tag
              :type="scope.row.documentationConsistency == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.documentationConsistency == 1 ? '一致' : '不一致' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.documentationConsistency == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.documentationConsistency == 1 ? '一致' : '不一致' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="内控标准符合状态"
        align="center"
        width="140"
      >
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.internalStandardCompliance == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.internalStandardCompliance == 1 ? '符合' : '不符合' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column
        label="是否可生产"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.canProduceRemark"
            :content="scope.row.canProduceRemark"
            placement="top"
          >
            <el-tag
              :type="scope.row.canProduce == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.canProduce == 1 ? '可生产' : '不可生产' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.canProduce == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.canProduce == 1 ? '可生产' : '不可生产' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="是否可出库"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.canDeliverRemark"
            :content="scope.row.canDeliverRemark"
            placement="top"
          >
            <el-tag
              :type="scope.row.canDeliver == 1 ? 'success' : 'danger'"
              size="small"
            >
              {{ scope.row.canDeliver == 1 ? '可出库' : '不可出库' }}
            </el-tag>
          </el-tooltip>
          <el-tag
            v-else
            :type="scope.row.canDeliver == 1 ? 'success' : 'danger'"
            size="small"
          >
            {{ scope.row.canDeliver == 1 ? '可出库' : '不可出库' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['qc:audit:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['qc:audit:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改产品准入检查对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="产品ID" prop="productId">
          <el-input v-model="form.productId" placeholder="请输入产品ID" />
        </el-form-item>
        <el-form-item label="产品代码" prop="productCode">
          <el-input v-model="form.productCode" placeholder="请输入产品代码" />
        </el-form-item>
        <el-form-item label="实验室编号" prop="laboratoryCode">
          <el-select
            v-model="form.laboratoryCode"
            placeholder="请选择实验室编号"
          >
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="生产企业" prop="manufacturer">
          <el-select v-model="form.manufacturer" placeholder="请选择生产企业">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="productName">
          <el-input v-model="form.productName" placeholder="请输入产品名称" />
        </el-form-item>
        <el-form-item label="规格" prop="spec">
          <el-input v-model="form.spec" placeholder="请输入规格" />
        </el-form-item>
        <el-form-item label="预计生产日期" prop="plannedProductionDate">
          <el-date-picker
            clearable
            v-model="form.plannedProductionDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择预计生产日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="订单数量" prop="orderQuantity">
          <el-input v-model="form.orderQuantity" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="产品交期" prop="deliveryDate">
          <el-date-picker
            clearable
            v-model="form.deliveryDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择产品交期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产品类型" prop="productType">
          <el-select v-model="form.productType" placeholder="请选择产品类型">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="配方稳定性报告状态" prop="formulaStabilityReport">
          <el-input
            v-model="form.formulaStabilityReport"
            placeholder="请输入配方稳定性报告状态"
          />
        </el-form-item>
        <el-form-item
          label="配方稳定性报告风险内容"
          prop="formulaStabilityReportHoverTip"
        >
          <el-input
            v-model="form.formulaStabilityReportHoverTip"
            placeholder="请输入配方稳定性报告风险内容"
          />
        </el-form-item>
        <el-form-item
          label="配制可行性评估状态"
          prop="formulaFeasibilityAssessment"
        >
          <el-input
            v-model="form.formulaFeasibilityAssessment"
            placeholder="请输入配制可行性评估状态"
          />
        </el-form-item>
        <el-form-item
          label="配制可行性评估风险内容"
          prop="formulaFeasibilityAssessmentHoverTip"
        >
          <el-input
            v-model="form.formulaFeasibilityAssessmentHoverTip"
            placeholder="请输入配制可行性评估风险内容"
          />
        </el-form-item>
        <el-form-item label="标准配制工艺单状态" prop="standardFormulaProcess">
          <el-input
            v-model="form.standardFormulaProcess"
            placeholder="请输入标准配制工艺单状态"
          />
        </el-form-item>
        <el-form-item label="生产模具治具确认状态" prop="moldToolConfirmation">
          <el-input
            v-model="form.moldToolConfirmation"
            placeholder="请输入生产模具治具确认状态"
          />
        </el-form-item>
        <el-form-item
          label="生产模具治具确认预计时间"
          prop="moldToolConfirmationHoverTip"
        >
          <el-input
            v-model="form.moldToolConfirmationHoverTip"
            placeholder="请输入生产模具治具确认预计时间"
          />
        </el-form-item>
        <el-form-item
          label="灌包可行性评估状态"
          prop="fillingPackagingFeasibility"
        >
          <el-input
            v-model="form.fillingPackagingFeasibility"
            placeholder="请输入灌包可行性评估状态"
          />
        </el-form-item>
        <el-form-item
          label="灌包可行性评估风险内容"
          prop="fillingPackagingFeasibilityHoverTip"
        >
          <el-input
            v-model="form.fillingPackagingFeasibilityHoverTip"
            placeholder="请输入灌包可行性评估风险内容"
          />
        </el-form-item>
        <el-form-item label="灌装/包装SOP状态" prop="fillingPackagingSop">
          <el-input
            v-model="form.fillingPackagingSop"
            placeholder="请输入灌装/包装SOP状态"
          />
        </el-form-item>
        <el-form-item label="成品检验标准状态" prop="finishedProductStandard">
          <el-input
            v-model="form.finishedProductStandard"
            placeholder="请输入成品检验标准状态"
          />
        </el-form-item>
        <el-form-item label="质量协议状态" prop="qualityAgreement">
          <el-input
            v-model="form.qualityAgreement"
            placeholder="请输入质量协议状态"
          />
        </el-form-item>
        <el-form-item
          label="质量协议合同类型是独立还是主框架合同"
          prop="qualityAgreementHoverTip"
        >
          <el-input
            v-model="form.qualityAgreementHoverTip"
            placeholder="请输入质量协议合同类型是独立还是主框架合同"
          />
        </el-form-item>
        <el-form-item label="包材标准状态" prop="packagingMaterialStandard">
          <el-input
            v-model="form.packagingMaterialStandard"
            placeholder="请输入包材标准状态"
          />
        </el-form-item>
        <el-form-item label="料体标样状态" prop="liquidSample">
          <el-input
            v-model="form.liquidSample"
            placeholder="请输入料体标样状态"
          />
        </el-form-item>
        <el-form-item label="包材标准样状态" prop="packagingMaterialSample">
          <el-input
            v-model="form.packagingMaterialSample"
            placeholder="请输入包材标准样状态"
          />
        </el-form-item>
        <el-form-item label="成品标样状态" prop="finishedProductSample">
          <el-input
            v-model="form.finishedProductSample"
            placeholder="请输入成品标样状态"
          />
        </el-form-item>
        <el-form-item
          label="过度包装确认状态"
          prop="excessivePackagingConfirmation"
        >
          <el-input
            v-model="form.excessivePackagingConfirmation"
            placeholder="请输入过度包装确认状态"
          />
        </el-form-item>
        <el-form-item
          label="过度包装风险内容"
          prop="excessivePackagingConfirmationHoverTip"
        >
          <el-input
            v-model="form.excessivePackagingConfirmationHoverTip"
            placeholder="请输入过度包装风险内容"
          />
        </el-form-item>
        <el-form-item
          label="注册备案是否完成状态"
          prop="registrationCompletion"
        >
          <el-input
            v-model="form.registrationCompletion"
            placeholder="请输入注册备案是否完成状态"
          />
        </el-form-item>
        <el-form-item label="备案号" prop="registrationCompletionHoverTip">
          <el-input
            v-model="form.registrationCompletionHoverTip"
            placeholder="请输入备案号"
          />
        </el-form-item>
        <el-form-item
          label="大货标准配方工艺单与注册/备案一致性状态"
          prop="formulaProcessConsistency"
        >
          <el-input
            v-model="form.formulaProcessConsistency"
            placeholder="请输入大货标准配方工艺单与注册/备案一致性状态"
          />
        </el-form-item>
        <el-form-item label="风险内容" prop="formulaProcessConsistencyHoverTip">
          <el-input
            v-model="form.formulaProcessConsistencyHoverTip"
            placeholder="请输入风险内容"
          />
        </el-form-item>
        <el-form-item
          label="大货文案与备案资料一致性状态"
          prop="documentationConsistency"
        >
          <el-input
            v-model="form.documentationConsistency"
            placeholder="请输入大货文案与备案资料一致性状态"
          />
        </el-form-item>
        <el-form-item label="风险内容" prop="documentationConsistencyHoverTip">
          <el-input
            v-model="form.documentationConsistencyHoverTip"
            placeholder="请输入风险内容"
          />
        </el-form-item>
        <el-form-item
          label="产品内控标准符合备案执行标准状态"
          prop="internalStandardCompliance"
        >
          <el-input
            v-model="form.internalStandardCompliance"
            placeholder="请输入产品内控标准符合备案执行标准状态"
          />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="是否可生产" prop="canProduce">
          <el-input v-model="form.canProduce" placeholder="请输入是否可生产" />
        </el-form-item>
        <el-form-item label="是否可生产备注" prop="canProduceRemark">
          <el-input
            v-model="form.canProduceRemark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="是否可出库" prop="canDeliver">
          <el-input v-model="form.canDeliver" placeholder="请输入是否可出库" />
        </el-form-item>
        <el-form-item label="是否可出库备注" prop="canDeliverRemark">
          <el-input
            v-model="form.canDeliverRemark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listAudit,
  getAudit,
  delAudit,
  addAudit,
  updateAudit,
} from "@/api/qc/audit";

export default {
  name: "Audit",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 产品准入检查表格数据
      auditList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productCode: null,
        laboratoryCode: null,
        manufacturer: null,
        productName: null,
        plannedProductionDate: null,
        deliveryDate: null,
        productType: null,
        registrationCompletionHoverTip: null,
        canProduce: null,
        canDeliver: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        productCode: [
          { required: true, message: "产品代码不能为空", trigger: "blur" },
        ],
        productName: [
          { required: true, message: "产品名称不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询产品准入检查列表 */
    getList() {
      this.loading = true;
      listAudit(this.queryParams).then((response) => {
        this.auditList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        productId: null,
        productCode: null,
        laboratoryCode: null,
        manufacturer: null,
        productName: null,
        spec: null,
        plannedProductionDate: null,
        orderQuantity: null,
        deliveryDate: null,
        productType: null,
        formulaStabilityReport: null,
        formulaStabilityReportHoverTip: null,
        formulaFeasibilityAssessment: null,
        formulaFeasibilityAssessmentHoverTip: null,
        standardFormulaProcess: null,
        moldToolConfirmation: null,
        moldToolConfirmationHoverTip: null,
        fillingPackagingFeasibility: null,
        fillingPackagingFeasibilityHoverTip: null,
        fillingPackagingSop: null,
        finishedProductStandard: null,
        qualityAgreement: null,
        qualityAgreementHoverTip: null,
        packagingMaterialStandard: null,
        liquidSample: null,
        packagingMaterialSample: null,
        finishedProductSample: null,
        excessivePackagingConfirmation: null,
        excessivePackagingConfirmationHoverTip: null,
        registrationCompletion: null,
        registrationCompletionHoverTip: null,
        formulaProcessConsistency: null,
        formulaProcessConsistencyHoverTip: null,
        documentationConsistency: null,
        documentationConsistencyHoverTip: null,
        internalStandardCompliance: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        canProduce: null,
        canProduceRemark: null,
        canDeliver: null,
        canDeliverRemark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加产品准入检查";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAudit(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改产品准入检查";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAudit(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAudit(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除产品准入检查编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAudit(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "qc/audit/export",
        {
          ...this.queryParams,
        },
        `audit_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>

<style scoped>
/* 状态标签样式优化 */
.el-tag {
  cursor: pointer;
}

/* 悬浮提示样式 */
.el-tooltip__popper {
  max-width: 300px;
}

/* 表格列宽度优化 */
.el-table .cell {
  padding: 0 8px;
}
</style>
