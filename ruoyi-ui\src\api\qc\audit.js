import request from '@/utils/request'

// 查询产品准入检查列表
export function listAudit(query) {
  return request({
    url: '/qc/audit/list',
    method: 'get',
    params: query
  })
}

// 查询产品准入检查详细
export function getAudit(id) {
  return request({
    url: '/qc/audit/' + id,
    method: 'get'
  })
}

// 新增产品准入检查
export function addAudit(data) {
  return request({
    url: '/qc/audit',
    method: 'post',
    data: data
  })
}

// 修改产品准入检查
export function updateAudit(data) {
  return request({
    url: '/qc/audit',
    method: 'put',
    data: data
  })
}

// 删除产品准入检查
export function delAudit(id) {
  return request({
    url: '/qc/audit/' + id,
    method: 'delete'
  })
}

// 导出产品准入检查
export function exportAudit(query) {
  return request({
    url: '/qc/audit/export',
    method: 'get',
    params: query
  })
}
