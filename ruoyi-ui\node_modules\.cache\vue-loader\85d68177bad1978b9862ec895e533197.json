{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue?vue&type=style&index=0&id=bd747408&scoped=true&lang=scss", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue", "mtime": 1754031707465}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1744596552583}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5zZWxlY3Qtd3JhcHBlciB7DQogIC5pdGVtIHsNCiAgICBoZWlnaHQ6IDI0cHg7DQogICAgcGFkZGluZzogNXB4IDEwcHg7DQogICAgZm9udC1zaXplOiAxMnB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNEQ0RGRTY7DQogICAgYm9yZGVyLXJhZGl1czogMnB4Ow0KICAgIGJveC1zaGFkb3c6IDAgMCAzNXB4IDAgcmdiKDE1NCAxNjEgMTcxIC8gMTUlKTsNCiAgICBtYXJnaW4tdG9wOiA1cHg7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICB9DQoNCiAgLnNlbGVjdGVkIHsNCiAgICBjb2xvcjogIzAwYWZmZjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["saveOrUpdate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm2HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "saveOrUpdate.vue", "sourceRoot": "src/views/software/softwareDevelopingFormula", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 添加或修改研发配方对话框 -->\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n        <el-tab-pane v-for=\"(formula,index) in formulaTabs\" :key=\"index\" :label=\"formula.title\"\r\n                     :name=\"formula.code\">\r\n          <template v-if=\"formula.code==='base'\">\r\n            <fieldset>\r\n              <legend>项目信息</legend>\r\n              <el-row v-if=\"form.id\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    {{form.projectNo}}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row v-else>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    <el-select style=\"width: 500px\" clearable filterable v-model=\"form.projectNo\" @change=\"projectChange\">\r\n                      <el-option\r\n                        v-for=\"item in projectList\"\r\n                        :key=\"item.projectNo\"\r\n                        :label=\"item.projectNo+'('+item.productName+'|'+item.customerName+')'\"\r\n                        :value=\"item.projectNo\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户名称\" prop=\"customerName\">\r\n                    <el-input disabled=\"true\" size=\"small\" v-model=\"form.customerName\" placeholder=\"请输入客户名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品名称\" prop=\"productName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"品牌名称\" prop=\"brandName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.brandName\" placeholder=\"请输入品牌名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"系列名称\" prop=\"seriesName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.seriesName\" placeholder=\"请输入系列名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"子名称\" prop=\"itemName\">\r\n                    <el-select clearable filterable v-model=\"form.itemName\" @change=\"itemNameChange\">\r\n                      <el-option\r\n                        v-for=\"item in itemNames\"\r\n                        :key=\"item.id\"\r\n                        :label=\"item.text\"\r\n                        :value=\"item.id\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"用途\" prop=\"purpose\">\r\n                    <el-select clearable v-model=\"form.purpose\" placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in purposeOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :disabled=\"item.isShow===0\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n<!--              <el-row>-->\r\n<!--                <el-col :span=\"8\">-->\r\n<!--                  <el-form-item label=\"状态\" prop=\"formulaStatus\">-->\r\n<!--                    <el-radio-group v-model=\"form.formulaStatus\">-->\r\n<!--                      <el-radio :label=\"0\">正常</el-radio>-->\r\n<!--                      <el-radio :label=\"1\">停用</el-radio>-->\r\n<!--                     </el-radio-group>-->\r\n<!--                  </el-form-item>-->\r\n<!--                </el-col>-->\r\n<!--              </el-row>-->\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>编码信息</legend>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方编码\" prop=\"formulaCode\">\r\n                    {{form.formulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品分类代码\" prop=\"cpfldm\">\r\n                    {{form.cpfldm}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"化妆品分类\" prop=\"cosmeticClassification\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156604264.png\" style=\"height: 500px\" >\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156703377.png\" style=\"height: 500px\" >\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n                      化妆品分类\r\n                    </div>\r\n                    <el-select clearable v-model=\"form.cosmeticClassification\">\r\n                      <el-option\r\n                        v-for=\"item in cosmeticClassificationOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='1'\" label=\"情形\" prop=\"cosmeticCaseFirst\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseFirst\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseFirstOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2'\" label=\"情形\" prop=\"cosmeticCase\">\r\n                    <el-select clearable v-model=\"form.cosmeticCase\">\r\n                      <el-option\r\n                        v-for=\"item in caseOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2' && form.cosmeticCase==='1'\" label=\"情形\" prop=\"cosmeticCaseSecond\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseSecond\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseSecondOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n                    <el-input v-model=\"form.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"form.oldFormulaCode\" :span=\"8\">\r\n                  <el-form-item label=\"复制的配方编码\" prop=\"oldFormulaCode\">\r\n                      {{form.oldFormulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方类别\" prop=\"categoryText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.categoryText\"\r\n                      :options=\"categoryArray\"\r\n                      :props=\"categoryProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"CIR历史用量\" prop=\"cirText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.cirText\"\r\n                      :options=\"cirDataArray\"\r\n                      :props=\"cirDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"毒理使用量参考\" prop=\"duliText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.duliText\"\r\n                      :options=\"duliDataArray\"\r\n                      :props=\"duliDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>备案相关</legend>\r\n              <el-divider content-position=\"left\">功效宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxc\" style=\"width: 1000px\"  @change=\"codeChange(1)\">\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 0)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 1)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 3)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">其他特别宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxcOther\">\r\n                <el-checkbox\r\n                  v-for=\"dict in otherSpecialClaimsOptions\"\r\n                  :key=\"dict.id\"\r\n                  :label=\"dict.id\">\r\n                  {{dict.id}}.{{ dict.title }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">申报类别(特殊化妆品填报)</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"染发类\">\r\n                    <el-checkbox-group @change=\"categoryChange\"  v-model=\"form.ranfalei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in rflOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"祛斑美白类\">\r\n                    <el-checkbox-group\r\n                      @change=\"categoryChange\" v-model=\"form.qubanmeibailei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in qbmblOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"\" prop=\"fangshailei\">\r\n                    <el-checkbox  @change=\"categoryChange\" v-model=\"form.fangshailei\">防晒类</el-checkbox>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"SPF值\" prop=\"sfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.sfa\" placeholder=\"SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"PA值\" prop=\"pa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.pa\" placeholder=\"PA值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"浴后SPF值\" prop=\"yushousfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.yushousfa\" placeholder=\"浴后SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"xgx\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          1.特定宣称：宣传试用敏感皮肤，无泪配方<br/>\r\n                          2.特定宣称：原料功效<br/>\r\n                          3.宣称温和：无刺激<br/>\r\n                          4.宣称量化指标（时间、统计数据等）<br/>\r\n                          5.孕妇和哺乳期妇女适用\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n\r\n                      <el-checkbox  @change=\"categoryChange\" v-model=\"form.xingongxiao\">新功效</el-checkbox>\r\n                    </div>\r\n                    <el-input @input=\"categoryChange('1')\" v-model=\"form.xingongxiaocontent\" autosize type=\"textarea\" placeholder=\"请输入新功效\" style=\"width: 800px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">作用部位</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.zybw\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in zybwOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">产品剂型</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.cpjx\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in cpjxOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">适用人群</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.syrq\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syrqOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">使用方法</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.pflx\"  @change=\"codeChange(2)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syffOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <br />\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"制造量\" prop=\"weight\">\r\n                    <el-input v-model=\"form.weight\" placeholder=\"请输入制造量\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"稳定性结果\" prop=\"stabilityresult\">\r\n                    <el-input autosize type=\"textarea\" v-model=\"form.stabilityresult\" placeholder=\"请输入稳定性结果\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"功效概述\" prop=\"gxgs\">\r\n                    <el-input :autosize=\"{ minRows: 3, maxRows: 20}\" type=\"textarea\" v-model=\"form.gxgs\" placeholder=\"请输入功效概述\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider  content-position=\"left\">稳定性测试记录</el-divider>\r\n              <el-table :data=\"stabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\"  :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-divider content-position=\"left\">关联稳定性测试记录</el-divider>\r\n              <el-table :data=\"relationStabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\" :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注\" prop=\"formulaRemark\">\r\n                    <el-input  autosize v-model=\"form.formulaRemark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaMaterial'\">\r\n            <el-row v-if=\"!form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"是否复制配方\">\r\n                  <el-radio-group v-model=\"isCopy\">\r\n                    <el-radio :label=\"0\">否</el-radio>\r\n                    <el-radio :label=\"1\">是</el-radio>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===0\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:300px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择配方\">\r\n                  <el-input v-model=\"form.formulaCodeParams\"  @keyup.enter.native=\"queryFormulaCode\" style=\"width:300px\"  placeholder=\"如果需要添加配方,请输入配方编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryFormulaCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-button v-if=\"isGenFormula===1\" type=\"primary\" @click=\"genPformulaInfo\">生成P配方</el-button>\r\n              </el-col>\r\n              <el-col :span=\"12\" v-if=\"form.pFormulaCount>0\">\r\n                <el-button v-if=\"isBMformula===1\" type=\"primary\" @click=\"genNewformulaInfo\">生成含B代码配方</el-button>\r\n                <div v-if=\"form.formulaCodeBuff\">已生成含B代码配方:<span v-html=\"form.formulaCodeBuff\"></span></div>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===1\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"请输入需要复制的实验室编码\">\r\n                  <el-input v-model=\"form.formulaCodeParams\" @focus=\"toChoose\" style=\"width:350px\"  placeholder=\"请选择要复制的实验室编码\" >\r\n                    <el-button\r\n                      slot=\"append\"\r\n                      class=\"el-icon-zoom-in\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"toChoose\" />\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:350px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-table :data=\"formulaMaterialDatas\" :row-style=\"formulaMaterialBack\" show-summary :summary-method=\"getSummaries\" @selection-change=\"handleFormulaMaterialSelectionChange\">\r\n                  <el-table-column v-if=\"form.id\" align=\"center\" type=\"selection\" width=\"50\" :selectable=\"selectable\"></el-table-column>\r\n                  <el-table-column align=\"center\" width=\"60\">\r\n                    <template slot-scope=\"scope\">\r\n                      <i v-if=\"(form.isLock===1) && isLook\" class=\"el-icon-remove-outline\" @click=\"delFormulaMaterial(scope.row)\" ></i>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料代码\" align=\"center\" prop=\"materialCode\" width=\"80\">\r\n                    <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.type==0\" @click=\"materialDetails(scope.row)\" style=\"color: #00afff;cursor: pointer\">{{scope.row.materialCode}}</span>\r\n                       <span v-else >{{scope.row.materialCode}}</span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"推荐原料\" align=\"center\" prop=\"relationCode\" width=\"140\"  />\r\n                  <el-table-column label=\"商品名称\" v-if=\"isShowMaterialGoodsName===1\" align=\"center\" prop=\"materialGoodsName\" width=\"280\"  />\r\n                  <el-table-column label=\"比例(%)\" width=\"140\" align=\"center\" prop=\"percentage\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"number\" v-model=\"scope.row.percentage\" @input=\"limitDecimal(scope.row)\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"分相\" width=\"100\" align=\"center\"prop=\"subItem\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input v-model=\"scope.row.subItem\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料用途\" width=\"120\" align=\"center\" prop=\"designatedUse\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select @change=\"designateChange(scope.row)\" v-model=\"scope.row.designatedUse\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in useOptions\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.value\"\r\n                          :value=\"item.value\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"指定代码\" width=\"150\" align=\"center\" prop=\"appointCode\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select clearable v-model=\"scope.row.appointCode\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in scope.row.materialCodes\"\r\n                          :key=\"item.materialSubCode\"\r\n                          :label=\"item.materialSubCode\"\r\n                          :value=\"item.materialSubCode\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"使用目的\" width=\"200\" align=\"center\" prop=\"symdInfo\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input style=\"width: 190px\" v-model=\"scope.row.symdInfo\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"周期(天)\" align=\"center\" prop=\"orderingCycle\" />\r\n                  <el-table-column label=\"原料认证\" align=\"center\" prop=\"certification\">\r\n                      <template slot-scope=\"scopoe\">\r\n                        {{selectDictLabel(certificationOptions,scopoe.row.certification)}}\r\n                      </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"进口国家\" align=\"center\" prop=\"importCountry\" />\r\n                  <el-table-column label=\"备注\" align=\"center\" width=\"300\" prop=\"remark\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isRelation==1\"><el-input v-model=\"scope.row.remark\"/></span>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-col>\r\n            </el-row>\r\n            <br />\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"配方图片\">\r\n                  <imageUpload v-model=\"form.formulaImage\" :limit=\"3\"></imageUpload>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"配方搭建思路\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.formulaConstructionIdeas\" placeholder=\"请输入配方搭建思路\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                 <div style=\"text-align: center\">\r\n                   <el-button type=\"primary\" @click=\"submitUploadForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                 </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaFile'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"检测标准\" prop=\"introFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.introFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试结果\" prop=\"wendingxingResult\">\r\n                  <el-select v-model=\"form.wendingxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试备注\" prop=\"wendingxingRemark\">\r\n                   <el-input type=\"textarea\" v-model=\"form.wendingxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"稳定性检测报告\" prop=\"wendingxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.wendingxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"工艺\" prop=\"gongyiFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.gongyiFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性结果\" prop=\"xiangrongxingResult\">\r\n                  <el-select v-model=\"form.xiangrongxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性备注\" prop=\"xiangrongxingRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.xiangrongxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"相容性测试报告\" prop=\"xiangrongxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiangrongxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战结果\" prop=\"weishenwuResult\">\r\n                  <div slot=\"label\">\r\n                    <el-tooltip>\r\n                      <i class=\"el-icon-question\" ></i>\r\n                      <div slot=\"content\">\r\n                        <p>高风险(没有测试过相关防腐体系)</p>\r\n                        <p>中风险(测试进行中,有一定数据量)</p>\r\n                        <p>低风险(有相似配方的测试数据,且测试通过)</p>\r\n                        <p>无风险(测试通过)</p>\r\n                        <p>测试没通过(不能释放)</p>\r\n                      </div>\r\n                    </el-tooltip>\r\n                    防腐挑战结果\r\n                  </div>\r\n                  <el-select v-model=\"form.weishenwuResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in ffjtxfxpgOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战备注\" prop=\"weishenwuRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.weishenwuRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"防腐实验报告\" prop=\"weishenwuFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.weishenwuFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"消费者测试报告\" prop=\"xiaofeizheFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiaofeizheFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"其它\" prop=\"qitaFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.qitaFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                <div style=\"text-align: center\">\r\n                  <el-button type=\"primary\" @click=\"submitUploadFileForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='recipeChangeHistory'\">\r\n            <el-table :data=\"recipeChangeHistoryData\">\r\n              <el-table-column align=\"center\" prop=\"modifiedTime\" label=\"更改日期\" />\r\n              <el-table-column align=\"center\" prop=\"materialCode\" label=\"原料代码\" />\r\n              <el-table-column align=\"center\" prop=\"percentageOld\" label=\"原始比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"percentageNew\" label=\"新比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"remark\" label=\"更改内容\" />\r\n              <el-table-column align=\"center\" prop=\"inciName\" label=\"INCI中文名\" />\r\n              <el-table-column align=\"center\" prop=\"operator\" label=\"编辑人\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='spec'\">\r\n            <el-divider content-position=\"left\">检测标准</el-divider>\r\n            <table class=\"base-table\"   style=\"margin-top: 0 !important;\">\r\n              <tr>\r\n                <td style=\"width:70px\" rowspan=\"5\">执行标准</td>\r\n              </tr>\r\n              <tr>\r\n                <td>标准名称</td>\r\n                <td>\r\n                  <el-select clearable filterable v-model=\"form.execNumberId\" @change=\"zxbzChange\">\r\n                    <el-option\r\n                      v-for=\"item in zxbzList\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.zxbzh+'('+item.bzmc+')'\"\r\n                      :value=\"item.id\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </td>\r\n                <td>执行标准号</td>\r\n                <td id=\"zxbzhTd\">{{zxbzDetail.zxbzh}}({{zxbzDetail.bzmc}})</td>\r\n                <td>定义</td>\r\n                <td id=\"dingyiTd\">{{zxbzDetail.dingyi}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>外观及pH</td>\r\n                <td  id=\"waiguanjiphTd\">{{zxbzDetail.waiguan}}{{zxbzDetail.ph}}</td>\r\n                <td >耐热</td>\r\n                <td  id=\"naireTd\">{{zxbzDetail.naire}}</td>\r\n                <td >耐寒</td>\r\n                <td  id=\"naihanTd\">{{zxbzDetail.naihan}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>归属公司</td>\r\n                <td  id=\"gsgsTd\">\r\n                  {{selectDictLabel(ownershopCompanyOptions,zxbzDetail.ownershopCompany)}}\r\n                </td>\r\n                <td >状态</td>\r\n                <td  id=\"zhtTd\">\r\n                  {{selectDictLabel(statusOptions,zxbzDetail.status)}}\r\n                </td>\r\n                <td >标准性质</td>\r\n                <td  id=\"bzxzTd\">\r\n                  {{selectDictLabel(bzxzOptions,zxbzDetail.bzxz)}}\r\n                </td>\r\n              </tr>\r\n              <!--                <tr>-->\r\n              <!--                  <td colspan=\"7\">-->\r\n              <!--                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>-->\r\n              <!--                  </td>-->\r\n              <!--                </tr>-->\r\n            </table>\r\n            <el-divider content-position=\"left\">检测项目</el-divider>\r\n            <el-tabs v-model=\"currentTab\" >\r\n              <el-tab-pane key=\"base\" label=\"常规检测\" name=\"base\" >\r\n                <div class=\"cell-wrapper\" >\r\n                  <div class=\"label\">选择模板:</div>\r\n                  <div class=\"content\">\r\n                    <el-select @change=\"changeFun\" v-model=\"form.currentTemplateId\" filterable size=\"mini\" >\r\n                      <el-option\r\n                        v-for=\"item in templateList\"\r\n                        :key=\"item.id\"\r\n                        :value=\"item.id\"\r\n                        :label=\"item.name\" />\r\n                    </el-select>\r\n                    <el-button icon=\"el-icon-search\" size=\"mini\" @click=\"changeTemplate(1)\" :loading=\"btnLoading\" />\r\n                  </div>\r\n                </div>\r\n                <div class=\"table-wrapper\" style=\"text-align: center;\" v-if=\"itemArray.length\">\r\n                  <table class=\"base-table small-table\">\r\n                    <tr>\r\n                      <th style=\"width: 50px\">\r\n                        <i @click=\"selectProject\" class=\"el-icon-circle-plus\" />\r\n                      </th>\r\n                      <th style=\"width: 120px\">类型</th>\r\n                      <th style=\"width: 120px\">检测项目</th>\r\n                      <th style=\"width: 320px\">检验标准</th>\r\n                      <th style=\"width: 320px\">检验方法</th>\r\n                      <th style=\"width: 320px\">标准值</th>\r\n                      <th style=\"width: 120px\">检验频次</th>\r\n                    </tr>\r\n                    <tr v-for=\"(item,i) in itemArray\" :key=\"item.id\" >\r\n                      <td><i v-if=\"(form.isLock===1 && isLook)\" class=\"el-icon-remove-outline\" @click=\"delItem(i)\"/></td>\r\n                      <td>{{item.type}}</td>\r\n                      <td>{{item.label}}</td>\r\n                      <td>{{item.standard}}</td>\r\n                      <td>{{item.methodTemplate}}</td>\r\n                      <td>\r\n                        <span v-if=\"isEditStandard(item.id)\"><el-input v-model=\"item.standardVal\" /> </span>\r\n                        <span v-else>\r\n                      <span v-if=\"(form.isLock===1 && isLook)\"><el-input v-model=\"item.standardVal\" /></span>\r\n                      <span v-else>{{item.standardVal}}</span>\r\n                    </span>\r\n                      </td>\r\n                      <td>{{item.frequency}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <br /><br />\r\n                  <div style=\"text-align: center;\">\r\n                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>\r\n                  </div>\r\n                </div>\r\n              </el-tab-pane>\r\n              <el-tab-pane key=\"special\" label=\"微生物检测\" name=\"microbe\" >\r\n                <el-row>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"样品物性\" prop=\"wxId\">\r\n                      <div style=\"cursor: pointer\" @click=\"showWx\" >\r\n                        <span v-if=\"form.wxId\" >{{wxLabel(form.wxId)}}</span>\r\n                        <i v-else style=\"color: #00afff;\">请选择</i>\r\n                      </div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"检验依据\" prop=\"inspectBasis\">\r\n                      <el-input v-model=\"form.inspectBasis\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-tab-pane>\r\n\r\n              <el-form-item label=\"备注\" style=\"margin-top: 20px\" prop=\"microbeRemark\">\r\n                <el-input v-model=\"form.microbeRemark\" type=\"textarea\" autosize />\r\n              </el-form-item>\r\n\r\n\r\n            </el-tabs>\r\n\r\n            <br /><br />\r\n            <el-divider content-position=\"left\">检测记录</el-divider>\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  plain\r\n                  icon=\"el-icon-plus\"\r\n                  size=\"mini\"\r\n                  v-if=\"form.isLock===1 && isLook\"\r\n                  @click=\"handleFormulaSpecAdd\"\r\n                >新增</el-button>\r\n              </el-col>\r\n            </el-row>\r\n            <el-table v-loading=\"loading\" :data=\"softwareFormulaSpecList\" style=\"overflow: scroll\">\r\n              <el-table-column label=\"样品来源\" align=\"center\" prop=\"type\">\r\n                <template slot-scope=\"scope\">\r\n                  {{selectDictLabel(yplyOptions,scope.row.type)}}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"测试样批号\" width=\"130\" align=\"center\" prop=\"ceshiyangpihao\" />\r\n              <el-table-column label=\"外观\" align=\"center\" prop=\"waiguan\" />\r\n              <el-table-column label=\"颜色\" align=\"center\" prop=\"yanse\" />\r\n              <el-table-column label=\"气味\" align=\"center\" prop=\"qiwei\" />\r\n              <el-table-column label=\"PH\" align=\"center\" prop=\"ph\" />\r\n              <el-table-column label=\"耐热\" align=\"center\" prop=\"naire\" />\r\n              <el-table-column label=\"耐寒\" align=\"center\" prop=\"naihan\" />\r\n              <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdTime\" width=\"180\" />\r\n              <el-table-column label=\"操作人\" align=\"center\" prop=\"operator\" />\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template v-slot=\"scope\">\r\n                  <el-tooltip content=\"修改\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      @click=\"handleFormulaSpecEdit(scope.row)\"\r\n                    />\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='workmanship'\">\r\n            <div style=\"width: 100%\">\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>工艺简述</span>\r\n                  <el-tooltip content=\"更新工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"refreshFormulaLegalGy('1')\"\r\n                    />\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(0)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>备案工艺</span>\r\n                  <el-tooltip content=\"复制工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"copyGongyi()\"\r\n                    >\r\n                      复制工艺简述\r\n                    </el-button>\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(1)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"formula.code==='compositionTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\" :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"INCI 中文名\" prop=\"chiName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                    <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">\r\n                       <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                          <div slot=\"content\">\r\n                            <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                          </div>\r\n                          <i class=\"el-icon-question\" ></i>\r\n                        </el-tooltip>\r\n                       <span v-html=\"scope.row.chiNameNew\"></span>\r\n                    </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" prop=\"engName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">{{scope.row.engName}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\" prop=\"percert\" align=\"center\"  width=\"150\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{totalPercent}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?'color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"国家简化版安评要求\" align=\"center\">\r\n                <el-table-column label=\"驻留类产品最高历史使用量\" prop=\"zllzglssyl\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"淋洗类产品最高历史使用量\" prop=\"lxlzglssyl\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"法规要求\" align=\"center\">\r\n                <el-table-column label=\"备案(明细)\" prop=\"bzmx\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"化妆品使用时的最大允许浓度\" prop=\"maxAllowConcentration\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"适用及(或)使用范围\" prop=\"scopeOfApplication\" align=\"center\"  width=\"220\"  />\r\n                <el-table-column label=\"其他限制和要求\" prop=\"otherLimit\" align=\"center\"  width=\"220\" />\r\n                <el-table-column label=\"标签上必须标印的 使用条件和注意事项\" prop=\"labelCondition\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n            </el-table>\r\n            <br />\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以上：</td>\r\n                <td align=\"left\">\r\n                   <span v-html=\"gtNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以下：</td>\r\n                <td align=\"left\">\r\n                  <span v-html=\"ltNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n            </table>\r\n          </template>\r\n          <template v-if=\"formula.code==='productSafetyAssessmentReport'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"产品评估结论\" prop=\"aqpgjl\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.aqpgjl\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n            <br />\r\n            <el-table height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n              <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                         <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI名称/英文民称\" prop=\"engName\" align=\"center\" fixed  width=\"200\" />\r\n              <el-table-column label=\"含量(%)\" prop=\"percert\" align=\"center\"  width=\"120\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?';color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"在《已使用原料目录》中的序号\" prop=\"cppfSyylxh\" align=\"center\"  width=\"220\" />\r\n              <el-table-column label=\"产品配方表备注\" prop=\"cppfRemark\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"《化妆品安全技术规范》要求\" prop=\"gcfJsgf\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"权威机构评估结论\" prop=\"gcfQwjgpgjl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"本企业原料历史使用量(%)\" prop=\"gcfBqyysyl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"评估结论\" prop=\"gcfPgjl\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isColor==1?'color:red':''\">{{scope.row.gcfPgjl}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"参考文献序号\" prop=\"gcfCkwx\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献内容\" prop=\"gcfCkwxnr\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献下载链接\" prop=\"gcfCkwxlj\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"可能含有的风险物质\" prop=\"aqxKnhyfxw\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"风险物质备注\" prop=\"aqxRemark\" align=\"center\"  width=\"200\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessment'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                   <span v-if=\"form.isCiji>0\" style=\"color:red;\">配方需测刺激性</span>\r\n                   <span v-if=\"form.isZhimin>0\" style=\"color:red;\"><span v-if=\"form.isCiji>0\">、</span><span v-else>配方需测</span>致敏性</span>\r\n                   <span v-if=\"form.zmCjTips\" style=\"color:red;\">。({{form.zmCjTips}})</span>\r\n                </el-row>\r\n                <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n                  <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">更新使用目的</el-button>\r\n                </div>\r\n                <br />\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"   :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"dataObj.zl\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"dataObj.newTotal\" align=\"center\" />\r\n                    <el-table-column label=\"适用及(或)使用范围\" :show-overflow-tooltip=\"true\"  width=\"220\" prop=\"dataObj.newRange\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"化妆品安全卫生规范\" align=\"center\">\r\n                    <el-table-column label=\"符合卫生规范\" width=\"120\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.dataObj.bzmx  === '否'\"></span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"备案明细\" width=\"220\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.dataObj.bzmx  === '是'\">\r\n                         {{scope.row.dataObj.bzmxDetail}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dataObj.syjsyfw\"  width=\"260\" :show-overflow-tooltip=\"true\"  label=\"适用及(或)使用范围\" align=\"center\" />\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\"  prop=\"dataObj.zdsynd\" align=\"center\" />\r\n                    <el-table-column prop=\"otherLimit\"  width=\"260\"  :show-overflow-tooltip=\"true\" label=\"其他限制和要求\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" width=\"140\" prop=\"zlxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" width=\"140\" prop=\"lxxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"药食同源\" align=\"center\">\r\n                    <el-table-column label=\"类型\" prop=\"ystyType\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{selectDictLabel(typeOptions,scope.row.dataObj.ystyType)}}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"使用限制\" prop=\"\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{scope.row.dataObj.ystyMax}}\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"CIR安全级别\" prop=\"finding\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      {{scope.row.dataObj.finding}}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"已上市产品原料使用信息2025/《国际化妆品安全评估数据索引》收录的部分原料使用信息\" width=\"800\" align=\"center\">\r\n                    <el-table-column align=\"center\" label=\"驻留\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"淋洗\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"total(中检院)\" prop=\"maxTotals\" />\r\n                    <el-table-column align=\"center\" label=\"total(国际化妆品)\" prop=\"aqpgMaxTotals\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威评估结论\" :show-overflow-tooltip=\"true\"  width=\"400\" prop=\"dataObj.conclusion\" align=\"center\" />\r\n                  <el-table-column label=\"评估结论\"  width=\"400\" prop=\"finalConclusion\" align=\"center\" />\r\n<!--                  <el-table-column label=\"已使用化妆品原料目录(2021年版)\"   align=\"center\">-->\r\n<!--                    <el-table-column label=\"驻留类\" prop=\"dataObj.zllzglssyl\" align=\"center\" />-->\r\n<!--                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lxlzglssyl\" align=\"center\" />-->\r\n<!--                  </el-table-column>-->\r\n                  <el-table-column label=\"EWG\" align=\"center\">\r\n                    <el-table-column label=\"EWG分值\" prop=\"ewgScore\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                        <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致癌性\" prop=\"dataObj.cancer\" align=\"center\" />\r\n                    <el-table-column label=\"过敏/免疫毒性\" width=\"160\" prop=\"dataObj.allergies\" align=\"center\" />\r\n                    <el-table-column label=\"发育/生殖毒性\" width=\"160\" prop=\"dataObj.developmental\" align=\"center\" />\r\n                    <el-table-column label=\"使用限制\" prop=\"dataObj.useRestrictions\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n                  <el-table-column label=\"是否新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"cirzlx\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"cirlxx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"安全技术规范\" align=\"center\">\r\n                    <el-table-column label=\"规范\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.bzmx  === '否'\"></span>\r\n                        <span v-else>\r\n                        {{scope.row.bzmx}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" prop=\"zlxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" prop=\"lxxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData_\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessmentSimple'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"EWG成分安全分\" prop=\"ewgScore\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='pFomula'\">\r\n            <el-row v-for=\"(item, index) in pFormulaMapData\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"配方编码\">\r\n                  {{item.formulaCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"实验室编码\">\r\n                  {{item.laboratoryCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-button  v-hasPermi=\"['software:softwareDevelopingFormula:genBMaterialInfo']\" type=\"primary\" @click=\"generBMaterialInfo(item.id)\">生成B代码</el-button>\r\n                <span v-if=\"item.materialCode\">该配方已生成了B代码,代码为:{{item.materialCode }}</span>\r\n              </el-col>\r\n              <el-table :data=\"item.materialDatas\" show-summary :summary-method=\"getSummariesPFormula\">\r\n                <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n                <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"200\" />\r\n                <el-table-column v-hasPermi=\"['software:softwareDevelopingFormula:lookMaterialGoodsName']\" label=\"商品名称\" prop=\"materialChiName\" align=\"center\"  width=\"400\" />\r\n                <el-table-column label=\"比例\" prop=\"percentage\" align=\"center\"  width=\"200\" />\r\n              </el-table>\r\n              <br /><br />\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"formulaTableDataList\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"分相\" prop=\"subItem\" align=\"center\"  width=\"60\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\" width=\"80\" />\r\n              <el-table-column label=\"INCI 中文名\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciName}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciNameEng}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  比例({{totalPercentVal}}%)\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"复配百分比(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量(%)\"  width=\"150px\" prop=\"subItem\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{sjTotalPercet}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"最低纯度(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\"  width=\"140px\" prop=\"sjProportion\" align=\"center\">\r\n                <template slot=\"header\" slot-scope=\"scope\">\r\n                  <el-tooltip content=\"按原料最低比例计算\" >\r\n                    <i class=\"el-icon-question\" ></i>\r\n                  </el-tooltip>\r\n                  实际成分含量\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"供应商数据\" prop=\"supplieData\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"周期(天)\" prop=\"orderingcycle\" align=\"center\" />\r\n              <el-table-column label=\"备注\"  width=\"220px\" prop=\"inicRemark\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span v-html=\"scope.row.inicRemark\"></span>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='specMaterial'\">\r\n            <el-table :data=\"specMaterialDatas\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"100\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"150\" />\r\n              <el-table-column label=\"原始INCI 中文名\" prop=\"inciName\" align=\"center\" />\r\n              <el-table-column label=\"替换为\" prop=\"replaceInciName\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div v-if=\"scope.row.inciName==='二氧化钛' || scope.row.inciName==='CI 77891'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas1\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                  <div v-else-if=\"scope.row.inciName==='氧化锌' || scope.row.inciName==='CI 77947'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas2\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <div v-if=\"form.isLock===1  && isLook && specMaterialDatas.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button @loading=\"btnLoading\" @click=\"submitTipsMaterialFormulaInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n          </template>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-form>\r\n\r\n    <div v-if=\"(form.isLock===1 || form.isLock===2) && isLook && activeName!=='workmanship'\" slot=\"footer\" class=\"dialog-footer\" style=\"margin-top:10px\">\r\n      <el-button @loading=\"btnLoading\" v-if=\"form.isDraft===1\" type=\"primary\" @click=\"submitForm(1)\">保存草稿</el-button>\r\n      <el-button @loading=\"btnLoading\" type=\"primary\" @click=\"submitForm(0)\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n\r\n    <el-dialog title=\"选择配方\" :visible.sync=\"visible\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n       <selectFormula @selected=\"selected\"/>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择项目\" :visible.sync=\"categoryOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <template v-for=\"category in categoryList\" >\r\n        <el-divider content-position=\"left\">\r\n          {{category.category}}\r\n          <el-tooltip content=\"全选/反选\" >\r\n            <i class=\"el-icon-circle-check\" @click=\"selectCategory(category)\" />\r\n          </el-tooltip>\r\n        </el-divider>\r\n\r\n        <el-row :gutter=\"20\" class=\"select-wrapper\" >\r\n          <el-col v-for=\"item in category.array\" :key=\"item.id\" :span=\"4\" style=\"display: flex;align-items: center\" >\r\n            <div class=\"item\" @click=\"selectXm(item.id)\" :class=\"xmIds.includes(item.id) ? 'selected':''\">\r\n              {{item.title}}\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </template>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 10px\" >\r\n        <el-button type=\"primary\" size=\"mini\" @click=\"confirmXm\" >确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"添加SPEC\" :visible.sync=\"specOpen\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row v-if=\"specId\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n              {{selectDictLabel(yplyOptions,form.type)}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row v-else>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n               <el-select v-model=\"form.type\" clearable>\r\n                 <el-option\r\n                   v-for=\"dict in yplyOptions\"\r\n                   :key=\"dict.dictValue\"\r\n                   :label=\"dict.dictLabel\"\r\n                   :value=\"dict.dictValue\"\r\n                 ></el-option>\r\n               </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <table class=\"base-table small-table\">\r\n            <tr>\r\n              <th style=\"width: 120px\">类型</th>\r\n              <th style=\"width: 120px\">检测项目</th>\r\n              <th style=\"width: 320px\">检验标准</th>\r\n              <th style=\"width: 320px\">标准值</th>\r\n              <th style=\"width: 120px\">检验频次</th>\r\n            </tr>\r\n            <tr v-for=\"(item,i) in userItemArray\" :key=\"item.id\" >\r\n              <td>{{item.type}}</td>\r\n              <td>{{item.label}}</td>\r\n              <td>{{item.standard}}</td>\r\n              <td><el-input v-model=\"item.standardVal\" /></td>\r\n              <td>{{item.frequency}}</td>\r\n            </tr>\r\n          </table>\r\n        </el-row>\r\n         <div style=\"text-align: center;margin-top:10px\">\r\n          <el-button @loading=\"btnLoading\" @click=\"submitUserSpec\" type=\"primary\">提 交</el-button>\r\n         </div>\r\n       </el-form>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"分享配方\" :visible.sync=\"shareOpen\" width=\"800px\" top=\"5vh\" append-to-body>\r\n      <el-checkbox-group v-model=\"shareDeptIds\">\r\n        < <el-checkbox\r\n          v-for=\"dict in shareDeptDatas\"\r\n        :key=\"dict.id\"\r\n        :label=\"dict.id\">\r\n          {{ dict.name }}\r\n      </el-checkbox>\r\n      </el-checkbox-group>\r\n      <div style=\"margin-top: 10px;text-align: center\">\r\n        <el-button  @loading=\"btnLoading\" @click=\"submitShareFormulaInfo\" type=\"primary\">提 交</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <el-dialog title=\"样品物性\" :visible.sync=\"wxOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <el-tree\r\n        :data=\"wxTree\"\r\n        @node-click=\"handleNodeClick\"\r\n        node-key=\"id\"\r\n        default-expand-all\r\n      />\r\n    </el-dialog>\r\n\r\n    <SoftwareMaterialSave ref=\"softwareMaterialSave\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listSoftwareDevelopingFormula,\r\n  getSoftwareDevelopingFormula,\r\n  delSoftwareDevelopingFormula,\r\n  addSoftwareDevelopingFormula,\r\n  updateSoftwareDevelopingFormula,\r\n  exportSoftwareDevelopingFormula,\r\n  querySoftwareDevelopingFormulaDict,\r\n  queryFormulaClassifyData,\r\n  getFormulaCategoryTree,\r\n  queryFormulaMaterialData,\r\n  getFormulaLabNoInfoByCode,\r\n  queryFormualMaterialRecipeChangeHistoryData,\r\n  queryFormulaZxbzDataList,\r\n  queryFormulaZxbzDataDetail,\r\n  getSoftwareDevelopingFormulaDetail,\r\n  addSoftwareDevelopingFormulaSpecZxbz,\r\n  queryCirHistoryData,\r\n  getCirDataTree,\r\n  queryDuliHistoryData,\r\n  getDuliDataTree,\r\n  addFormulaSpecMaterialData,\r\n  addFormulaSymdForm,\r\n  generatePFormulaInfo,\r\n  generateBMaterialInfo,\r\n  generateNewformulaInfo,\r\n  addSoftwareDevelopingUserFormulaSpecZxbz,\r\n  queryMaterialFormulaSpecDataList,\r\n  queryMaterialFormulaSpecDataDetail,\r\n  addFormulaGyjsBeianInfo,\r\n  queryFormulaLegalGy,\r\n  queryFormulaShareDeptDataList,\r\n  queryFormulaShareDeptDataDetail,\r\n  addFormulaShareDataInfo,\r\n  queryLookFormulaTabs,\r\n  updateSoftwareDevelopingFormulaImg,\r\n  queryFormulaStabilityRecordDataList,\r\n  updateSoftwareDevelopingFormulaFileImg, queryFormulaAppointMaterialDataList\r\n} from \"@/api/software/softwareDevelopingFormula\";\r\nimport {formualList, formualProjectDetail} from \"@/api/project/project\";\r\nimport {getFormulaInfoByCode, getRawMaterialInfoByCode} from \"@/api/software/softwareMaterial\";\r\nimport {isArray, isString} from \"@/utils/validate\";\r\nimport selectFormula from \"@/views/software/formula/components/selectFormula\";\r\nimport {allBcpTemplate, getBcpTemplate} from \"@/api/qc/bcpTemplate\";\r\nimport {allJcxm} from \"@/api/qc/jcxm\";\r\nimport {checkPermi} from \"@/utils/permission\";\r\nimport SoftwareMaterialSave from \"@/views/software/softwareMaterial/save\";\r\nimport { Base64 } from 'js-base64'\r\nimport {selectDictLabel} from \"../../../utils/ruoyi\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\n\r\nexport default {\r\n  name: \"SoftwareDevelopingFormulaSave\",\r\n  components:{selectFormula,SoftwareMaterialSave},\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      wxOpen:false,\r\n      activeName: \"base\",\r\n      currentTab: 'base',\r\n      wxOptions: [],\r\n      conclusionOfSafetyAssessmentName: \"first\",\r\n      loading: false,\r\n      visible: false,\r\n      btnLoading: false,\r\n      categoryOpen: false,\r\n      exportLoading: false,\r\n      fullscreenFlag: false,\r\n      shareOpen: false,\r\n      specOpen: false,\r\n      totalPercentVal:0,\r\n      sjTotalPercet:0,\r\n      isShowMaterialGoodsName:0,\r\n      isGenFormula:0,\r\n      isBMformula:0,\r\n      activeNames: [],\r\n      ids: [],\r\n      statusOptions: [],\r\n      shareDeptDatas: [],\r\n      shareDeptIds: [],\r\n      specId: null,\r\n      ownershopCompanyOptions: [],\r\n      bzxzOptions: [],\r\n      projectList: [],\r\n      typeOptions: [],\r\n      categoryList: [],\r\n      zxbzList: [],\r\n      xmIds: [],\r\n      recipeChangeHistoryData: [],\r\n      formulaTableDataList: [],\r\n      formulaTableDataListBack: [],\r\n      compositionTableDataList: [],\r\n      compositionTableDataListBack: [],\r\n      softwareFormulaSpecList: [],\r\n      specMaterialDatas: [],\r\n      itemArray: [],\r\n      userItemArray: [],\r\n      gtNumStr: '',\r\n      ltNumStr: '',\r\n      itemNames: [],\r\n      zxbzDetail:{},\r\n      gyjsData:{},\r\n      gyjsDataList:[],\r\n      zfylDataList:[],\r\n      gyjsBeianDataList:[],\r\n      zfylBeianDataList:[],\r\n      specObj:{},\r\n      single: true,\r\n      showSearch: false,\r\n      total: 0,\r\n      isCopy: 0,\r\n      softwareDevelopingFormulaList: [],\r\n      efficacyOptions: [],\r\n      otherSpecialClaimsOptions: [],\r\n      formulaMaterialDatas: [],\r\n      chooseFormulaMaterialDatas: [],\r\n      pFormulaMapData: [],\r\n      zybwOptions: [],\r\n      templateList: [],\r\n      cpjxOptions: [],\r\n      syrqOptions: [],\r\n      syffOptions: [],\r\n      wxTree: [],\r\n      purposeOptions: [],\r\n      categoryArray: [],\r\n      jcXmList: [],\r\n      stabilityDataList: [],\r\n      relationStabilityDataList: [],\r\n      jlOptions: [\r\n        10,50,100,500,1000,\r\n      ],\r\n      mjOptions: [\r\n        10,50,100,\r\n      ],\r\n      plOptions: [],\r\n      checkRow: {\r\n        id:null,\r\n        deptId:null\r\n      },\r\n      categoryProps: {\r\n        label: 'categoryName',\r\n        value: 'categoryId'\r\n      },\r\n      cirDataArray: [],\r\n      isLook:false,\r\n      cirDataProps: {\r\n        label: 'label',\r\n        value: 'id'\r\n      },\r\n      duliDataArray: [],\r\n      cosmeticCaseFirstOptions: [],\r\n      cosmeticCaseSecondOptions: [],\r\n      duliDataProps: {\r\n        label: 'zhType',\r\n        value: 'id'\r\n      },\r\n      formulaTabs:[{\r\n        title:'基础信息',\r\n        code:'base'\r\n      },{\r\n        title:'配方页面',\r\n        code:'formulaMaterial'\r\n      },{\r\n        title:'附件',\r\n        code:'formulaFile'\r\n      }],\r\n      title: \"\",\r\n      open: false,\r\n      isEdit: true,\r\n      certificationOptions:[],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        laboratoryCode: null,\r\n        formulaName: null,\r\n        ewgColor: null,\r\n        comclusionType:null\r\n      },\r\n      form: {},\r\n      rules: {\r\n        projectNo: [\r\n          { required: true, message: '请选择项目编码'},\r\n        ],\r\n        customerName: [\r\n          { required: true, message: '客户名称不允许为空'},\r\n        ],\r\n        productName: [\r\n          { required: true, message: '产品名称不允许为空'},\r\n        ],\r\n        cirText: [\r\n          { required: true, message: '请选择CIR历史用量'},\r\n        ],\r\n        duliText: [\r\n          { required: true, message: '请选择毒理使用量参考'},\r\n        ],\r\n        laboratoryCode: [\r\n          { required: true, message: '实验室编码不允许为空'},\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: '配方类别不允许为空'},\r\n        ],\r\n        pflx: [\r\n          { required: true, message: '使用方法不允许为空'},\r\n        ],\r\n      },\r\n      rflOptions:[\r\n        {\r\n          dictValue:'3',\r\n          dictLabel:'染发类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'防脱类'\r\n        }, {\r\n          dictValue:'1',\r\n          dictLabel:'烫发类'\r\n        }\r\n      ],\r\n      yplyOptions:[\r\n        {\r\n          dictValue:'0',\r\n          dictLabel:'检测方法'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'实验室小样'\r\n        }, {\r\n          dictValue:'3',\r\n          dictLabel:'中试样品'\r\n        }, {\r\n          dictValue:'4',\r\n          dictLabel:'大货样品'\r\n        }\r\n      ],\r\n      qiOptions:[\r\n        {\r\n          dictValue:'有香味',\r\n         }, {\r\n          dictValue:'有原料特征性气味',\r\n         }, {\r\n          dictValue:'无味',\r\n         }\r\n      ],\r\n      qbmblOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'祛斑美白类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'祛斑美白类(仅具物理遮盖作用)'\r\n        }\r\n      ],\r\n      cosmeticClassificationOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'第一类化妆品'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'第二类化妆品'\r\n        }\r\n      ],\r\n      caseOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'情形一'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'情形二'\r\n        }\r\n      ],\r\n      useOptions:[\r\n        {\r\n          value:'/',\r\n        },  {\r\n          value:'指定',\r\n        }\r\n      ],\r\n      specMaterialDatas1:[\r\n        {\r\n          value:'二氧化钛'\r\n        },\r\n        {\r\n          value:'CI 77891'\r\n        }\r\n      ],\r\n      specMaterialDatas2:[\r\n        {\r\n          value:'氧化锌'\r\n        },\r\n        {\r\n          value:'CI 77947'\r\n        }\r\n      ],\r\n      ffjtxfxpgOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'高风险'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'中风险'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'低风险'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'无风险'\r\n      },{\r\n        dictValue:'4',\r\n        dictLabel:'测试没通过'\r\n      }],\r\n      wdxOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'进行中'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'测试通过'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'测试失败'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'条件接受'\r\n      }],\r\n      ypFromOptions: [\r\n        {label: '实验室',value: 0},\r\n        {label: '中试',value: 1},\r\n        {label: '生产',value: 2},\r\n        {label: '生技复样',value: 3},\r\n      ],\r\n      stabilityStatusOptions: [\r\n        {label: '进行中',value: 0},\r\n        {label: '测试通过',value: 1},\r\n        {label: '测试失败',value: 2},\r\n        {label: '条件接受',value: 3},\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.wxTree = this.toTree(this.wxOptions, 0)\r\n\r\n    //使用目的\r\n    let certificationRes = await this.getDicts(\"SOFTWARE_CERTIFICATION\")\r\n    this.certificationOptions = certificationRes.data\r\n\r\n    this.getDicts(\"qc_jypl\").then(response => {\r\n      this.plOptions = response.data\r\n    })\r\n  },\r\n  watch: {\r\n    \"$route.query.params\": {\r\n      immediate: true,\r\n      handler() {\r\n        let params = this.$route.query.params;\r\n        if(params) {\r\n          let query = Base64.decode(Base64.decode(params));\r\n          if(query){\r\n            query = JSON.parse(query);\r\n            this.reset();\r\n            this.init(1);\r\n            this.handleUpdate(query.id,query.shareType===1?true:false);\r\n            this.btnLoading = true;\r\n            this.title = query.shareType===1?'修改配方':'查看配方';\r\n          }\r\n        }else{\r\n          this.reset();\r\n          this.init(2);\r\n          this.queryProjectList();\r\n          this.handleAdd();\r\n          this.isLook = true;\r\n        }\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    async showWx() {\r\n      this.wxOpen = true\r\n    },\r\n    selectDictLabel,\r\n    async designateChange(row){\r\n       let designatedUse = row.designatedUse;\r\n       if('指定'===designatedUse){  //指定原料\r\n          let materialId = row.materialId;\r\n          let res = await queryFormulaAppointMaterialDataList({id:materialId});\r\n          row.materialCodes = res;\r\n       }else{\r\n          row.appointCode = '';\r\n          row.materialCodes = [];\r\n       }\r\n    },\r\n    async queryProjectList(){\r\n      let projectList  = await formualList();\r\n      this.projectList = projectList;\r\n    },\r\n    async init(type){\r\n      if(checkPermi(['software:softwareDevelopingFormula:lookMaterialGoodsName'])) {\r\n        this.isShowMaterialGoodsName = 1;\r\n      }else{\r\n        this.isShowMaterialGoodsName = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genPformulaInfo'])) {\r\n        this.isGenFormula = 1;\r\n      }else{\r\n        this.isGenFormula = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genBMFormulaInfo'])) {\r\n        this.isBMformula = 1;\r\n      }else{\r\n        this.isBMformula = 0;\r\n      }\r\n      this.getDicts(\"ZXBZ_STATUS\").then(response => {\r\n        this.statusOptions = response.data;\r\n      })\r\n      this.getDicts(\"OWNERSHOP_COMPANY\").then(response => {\r\n        this.ownershopCompanyOptions = response.data;\r\n      })\r\n      this.getDicts(\"BZXZ\").then(response => {\r\n        this.bzxzOptions = response.data;\r\n      })\r\n      this.getDicts(\"rd_ysty_type\").then(response => {\r\n        this.typeOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE1\").then(response => {\r\n        this.cosmeticCaseFirstOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE2\").then(response => {\r\n        this.cosmeticCaseSecondOptions = response.data;\r\n      })\r\n      const categorySet = new Set()\r\n      const jcXmList = await allJcxm({type:1})\r\n      this.jcXmList = jcXmList\r\n      for (const item of jcXmList) {\r\n        categorySet.add(item.category)\r\n      }\r\n      const categoryList = []\r\n      for (const category of categorySet) {\r\n        categoryList.push({\r\n          category,\r\n          array: jcXmList.filter(i=>i.category === category),\r\n        })\r\n      }\r\n      this.categoryList = categoryList.filter(i=>i.category !== '微生物')\r\n      this.templateList = await allBcpTemplate();\r\n      //配方使用用途\r\n      let purposeRes =  await this.getDicts(\"SOFTWARE_FORMULA_PURPOSE\")\r\n      this.purposeOptions = purposeRes.data\r\n      //获取字典数据\r\n      let dictObj = await querySoftwareDevelopingFormulaDict();\r\n      this.efficacyOptions = dictObj.GXXC_DATA_LIST;\r\n      this.otherSpecialClaimsOptions = dictObj.GXXC_DATA_LIST_OTHER;\r\n      this.zybwOptions = dictObj.ZYBW_DATA_LIST;\r\n      this.cpjxOptions = dictObj.CPJX_DATA_LIST;\r\n      this.syrqOptions = dictObj.SYRQ_DATA_LIST;\r\n      this.syffOptions = dictObj.PFLX_DATA_LIST;\r\n      let categoryAllArray = await queryFormulaClassifyData()\r\n      if(type==2){\r\n        let datas = [22,23,24,25,26,27,43,50,61];\r\n        categoryAllArray = categoryAllArray.filter(i=>!datas.includes(i.categoryId));\r\n      }\r\n      this.categoryArray = await getFormulaCategoryTree(categoryAllArray)\r\n      let zxbzList = await queryFormulaZxbzDataList();\r\n      this.zxbzList = zxbzList;\r\n      //获取cir历史使用量\r\n      let cirDataAllArray = await queryCirHistoryData();\r\n      this.cirDataArray = await getCirDataTree(cirDataAllArray);\r\n      //获取毒理/供应商使用量参考\r\n      let duliDataAllArray = await queryDuliHistoryData();\r\n      this.duliDataArray = await getDuliDataTree(duliDataAllArray);\r\n    },\r\n    async itemNameChange(itemName){\r\n      let itemNames = this.itemNames;\r\n      let arr = itemNames.filter(i=> i.id === itemName)\r\n      let itemNameText = '';\r\n      if(arr && arr[0]) {\r\n        itemNameText = arr[0].text;\r\n      }\r\n       this.form.itemNameText = itemNameText;\r\n     },\r\n    async projectChange(projectNo) {\r\n      //获取项目详情\r\n      let projectDetail = await formualProjectDetail({projectNo});\r\n      let isEdit = true;\r\n      if(projectNo.indexOf(\"P\")!=-1 || projectNo=='210002089' ||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n         isEdit = false;\r\n      }\r\n      this.isEdit = isEdit;\r\n      this.form.itemName = '';\r\n      this.form.itemNameText = '';\r\n      this.itemNames = [];\r\n      if(projectDetail!=null && projectDetail.id){\r\n        this.form.customerName = projectDetail.customerName;\r\n        this.form.productName = projectDetail.productName;\r\n        this.form.brandName = projectDetail.brandName;\r\n        this.form.seriesName = projectDetail.seriesName;\r\n        let itemNames = projectDetail.itemNames;\r\n        if(itemNames){\r\n          itemNames = JSON.parse(itemNames);\r\n          this.itemNames = itemNames;\r\n        }\r\n       }else{\r\n        this.form.customerName = '';\r\n        this.form.productName = '';\r\n        this.form.brandName = '';\r\n        this.form.seriesName = '';\r\n       }\r\n    },\r\n    async zxbzChange(id){\r\n      let zxbzDetail = await queryFormulaZxbzDataDetail({id});\r\n      this.form.execNumber = zxbzDetail.zxbzh;\r\n      this.zxbzDetail = zxbzDetail;\r\n    },\r\n    async getList() {\r\n      let params = Object.assign({},this.queryParams)\r\n      this.loading = true\r\n      let res = await listSoftwareDevelopingFormula(params)\r\n      this.loading = false\r\n      this.softwareDevelopingFormulaList = res.rows\r\n      this.total = res.total\r\n    },\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    async reset() {\r\n      this.currentTab = 'base';\r\n      this.stabilityDataList = [];\r\n      this.relationStabilityDataList = [];\r\n      this.activeNames = [];\r\n      this.isCopy = 0;\r\n      this.isLook = false;\r\n      this.activeName = 'base';\r\n      this.itemNames = [];\r\n      this.formulaMaterialDatas = [];\r\n      this.chooseFormulaMaterialDatas = [];\r\n      this.pFormulaMapData = [];\r\n      this.recipeChangeHistoryData = [];\r\n      this.gyjsData = {};\r\n      this.gyjsDataList = [];\r\n      this.zfylDataList = [];\r\n      this.gyjsBeianDataList = [];\r\n      this.zfylBeianDataList = [];\r\n      this.softwareFormulaSpecList = [];\r\n      this.formulaTableDataList = [];\r\n      this.formulaTableDataListBack = [];\r\n      this.compositionTableDataList = [];\r\n      this.compositionTableDataListBack = [];\r\n      this.specMaterialDatas = [];\r\n      this.itemArray = [];\r\n      this.userItemArray = [];\r\n      this.zxbzDetail = {};\r\n      this.gtNumStr = '';\r\n      this.ltNumStr = '';\r\n      this.specObj = {};\r\n      this.totalPercentVal = 0;\r\n      this.specId = null;\r\n      this.sjTotalPercet = 0;\r\n      this.form = {\r\n        id: null,\r\n        currentTemplateId: null,\r\n        relationMaterialCode: null,\r\n        formulaName: null,\r\n        englishName: null,\r\n        materialCode: null,\r\n        formulaCodeParams: null,\r\n        price: null,\r\n        weight: 100,\r\n        isLock: 1,\r\n        isMateral: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        operator: null,\r\n        createdTime: null,\r\n        isDel: null,\r\n        lastModifiedTime: null,\r\n        note: null,\r\n        formulaCode: null,\r\n        duliId: null,\r\n        cirId: null,\r\n        cirText: null,\r\n        duliText: null,\r\n        laboratoryCode: null,\r\n        productName: null,\r\n        brandId: null,\r\n        customerCode: null,\r\n        customerName: null,\r\n        seriesName: null,\r\n        appearance: null,\r\n        colour: null,\r\n        ph: null,\r\n        viscosity: null,\r\n        stabilityresult: null,\r\n        gxgs: null,\r\n        category: null,\r\n        brandName: null,\r\n        standard: null,\r\n        introFile: [],\r\n        organizationId: null,\r\n        oldFormulaCode: null,\r\n        copyFormulaId: null,\r\n        addTips: null,\r\n        wendingxingFile: [],\r\n        gongyiFile: [],\r\n        xiangrongxingFile: [],\r\n        weishenwuFile: [],\r\n        xiaofeizheFile: [],\r\n        qitaFile: [],\r\n        execNumber: null,\r\n        isDraft: 1,\r\n        gxxc: [],\r\n        gxxcOther: [],\r\n        zybw: [],\r\n        syrq: [],\r\n        cpjx: [],\r\n        pflx: [],\r\n        cpfldm: null,\r\n        cosmeticClassification: null,\r\n        cosmeticCase: null,\r\n        execNumberId: null,\r\n        aqpgjl: null,\r\n        gongyijianshu: null,\r\n        gongyijianshuBeian: null,\r\n        ranfalei: [],\r\n        cosmeticCaseFirst: [],\r\n        cosmeticCaseSecond: [],\r\n        qubanmeibailei: [],\r\n        fangshailei: false,\r\n        sfa: null,\r\n        pa: null,\r\n        yushousfa: null,\r\n        xingongxiao: false,\r\n        xingongxiaocontent: null,\r\n        ftlTime: null,\r\n        filCode: null,\r\n        baCode: null,\r\n        baTime: null,\r\n        filCodeNote: null,\r\n        baCodeNote: null,\r\n        waxcName: null,\r\n        waxcOthername: null,\r\n        waxcStatus: null,\r\n        baStatus: null,\r\n        formulaPid: null,\r\n        bpNote: null,\r\n        zsTime: null,\r\n        zsCode: null,\r\n        gongyijianshuZs: null,\r\n        yfFile: null,\r\n        zsFile: null,\r\n        isLove: null,\r\n        upRate: null,\r\n        oriPrice: null,\r\n        levelNum: null,\r\n        purpose: '普通',\r\n        formulaStatus: 0,\r\n        formulaRemark: null,\r\n        projectNo: null,\r\n        itemName: null,\r\n        itemNameText: null,\r\n        formulaImage: null,\r\n        formulaConstructionIdeas: null,\r\n        isRealse: null,\r\n        materialStatusInfo: null,\r\n        importCountryInfo: null,\r\n        operatorName: null,\r\n        stabilityStatus: null,\r\n        isResult: null,\r\n        isGt: null,\r\n        type: null,\r\n        weishenwuResult: null,\r\n        weishenwuRemark: null,\r\n        xiangrongxingResult: null,\r\n        xiangrongxingRemark: null,\r\n        wendingxingResult: null,\r\n        wendingxingRemark: null,\r\n        materialCycle: null\r\n      };\r\n      this.resetForm(\"form\");\r\n\r\n      if (!this.wxOptions.length) {\r\n        this.wxOptions = await allTreeData({type: 9})\r\n      }\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n    },\r\n    handleFormulaMaterialSelectionChange(selection) {\r\n      this.chooseFormulaMaterialDatas = selection;\r\n    },\r\n    async genPformulaInfo() {\r\n      let chooseFormulaMaterialDatas = this.chooseFormulaMaterialDatas;\r\n      if (chooseFormulaMaterialDatas && chooseFormulaMaterialDatas.length > 0) {\r\n        for (let item of chooseFormulaMaterialDatas) {\r\n          let type = item.type;\r\n          if (type == 1) {\r\n            this.msgError('生成错误,请选择原料编码信息');\r\n            return;\r\n          }\r\n        }\r\n        let id = this.form.id;\r\n        let data = await generatePFormulaInfo({id,formulaMaterialDatas:JSON.stringify(chooseFormulaMaterialDatas)});\r\n        this.msgSuccess(\"操作成功\");\r\n      } else {\r\n        this.msgError('请选择原料信息!');\r\n      }\r\n    },\r\n    limitDecimal(row){\r\n       const inputValue = row.percentage;\r\n      // 正则表达式匹配最多6位小数的数字\r\n      const regex = /^\\d*\\.?\\d{0,6}$/;\r\n      if (regex.test(inputValue)) {\r\n        this.lastValue = inputValue;\r\n      } else {\r\n        // 如果不符合条件，回退到上一个有效值\r\n        row.percentage = this.lastValue;\r\n      }\r\n    },\r\n    async generBMaterialInfo(id) {\r\n       this.$confirm('您确定要生成B代码吗?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"info\"\r\n      }).then(async function () {\r\n         let data = await generateBMaterialInfo({id});\r\n       }).then(() => {\r\n         this.msgSuccess(\"操作成功\");\r\n      }).catch(() => {});\r\n    },\r\n    async genNewformulaInfo() {\r\n      let id = this.form.id;\r\n      let res = await generateNewformulaInfo({id});\r\n      this.msgSuccess(\"操作成功\");\r\n    },\r\n    handleAdd() {\r\n      this.reset();\r\n      this.formulaTabs = [{\r\n        title: '基础信息',\r\n        code: 'base'\r\n      }, {\r\n        title: '配方页面',\r\n        code: 'formulaMaterial'\r\n      }, {\r\n        title: '附件',\r\n        code: 'formulaFile'\r\n      }];\r\n      this.open = true;\r\n      this.title = \"创建配方\";\r\n    },\r\n    async handleUpdate(id,isLook) {\r\n      this.reset();\r\n      let formulaTabs = await queryLookFormulaTabs();\r\n      this.formulaTabs = formulaTabs;\r\n      this.btnLoading = true;\r\n      getSoftwareDevelopingFormulaDetail(id).then(async response => {\r\n        let form = response.data;\r\n        let isEdit = true;\r\n        let projectNo = form.projectNo;\r\n        if(projectNo.indexOf(\"P\")!=-1 ||projectNo=='210002089'||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n          isEdit = false;\r\n        }\r\n        if(form.isLock===2){\r\n          isEdit = false;\r\n        }\r\n        this.isEdit = isEdit;\r\n        if(form.fangshailei){\r\n          form.fangshailei = form.fangshailei==1?true:false;\r\n        }else{\r\n          form.fangshailei = false;\r\n        }\r\n        if(form.xingongxiao){\r\n          form.xingongxiao =form.xingongxiao==1?true:false;\r\n        }else{\r\n          form.xingongxiao = false;\r\n        }\r\n        let specObj = form.specObj;\r\n        if(specObj){\r\n          this.specObj = JSON.parse(specObj);\r\n        }else{\r\n          this.specObj = {};\r\n        }\r\n        let formulaObj = form.formulaObj;\r\n        if(formulaObj){\r\n          formulaObj = JSON.parse(formulaObj);\r\n          this.totalPercentVal = formulaObj.totalPercentVal;\r\n          this.sjTotalPercet = formulaObj.sjTotalPercet;\r\n          if(formulaObj.dataList){\r\n            this.formulaTableDataList = formulaObj.dataList;\r\n            this.formulaTableDataListBack = formulaObj.dataList;\r\n          }else{\r\n            this.formulaTableDataList = [];\r\n            this.formulaTableDataListBack = [];\r\n          }\r\n          if(formulaObj.allList){\r\n            this.compositionTableDataList = formulaObj.allList;\r\n            this.compositionTableDataListBack = formulaObj.allList;\r\n          }else{\r\n            this.compositionTableDataList = [];\r\n            this.compositionTableDataListBack = [];\r\n          }\r\n          if(formulaObj.specMaterialData){\r\n            this.specMaterialDatas = formulaObj.specMaterialData;\r\n          }else{\r\n            this.specMaterialDatas = [];\r\n          }\r\n          if(formulaObj.ltNumStr){\r\n            this.ltNumStr = formulaObj.ltNumStr;\r\n          }else{\r\n            this.ltNumStr = '';\r\n          }\r\n          if(formulaObj.gtNumStr){\r\n            this.gtNumStr = formulaObj.gtNumStr;\r\n          }else{\r\n            this.gtNumStr = '';\r\n          }\r\n        }\r\n        //获取配方原料信息\r\n        let formulaMaterialDatas = form.formulaMaterialDatas;\r\n        if(formulaMaterialDatas){\r\n           this.formulaMaterialDatas = JSON.parse(formulaMaterialDatas);\r\n        }else{\r\n          this.formulaMaterialDatas = [];\r\n        }\r\n        if (form.categoryText) {\r\n          let categoryTextList = form.categoryText.split(',');\r\n          let categoryIds = []\r\n          for (let t of categoryTextList) {\r\n            categoryIds.push(parseInt(t))\r\n          }\r\n          form.categoryText = categoryIds\r\n        } else {\r\n          form.categoryText = [];\r\n        }\r\n        if (form.cirText) {\r\n          let cirTextList = form.cirText.split(',');\r\n          let cirId = []\r\n          for (let t of cirTextList) {\r\n            cirId.push(parseInt(t))\r\n          }\r\n          form.cirText = cirId\r\n        } else {\r\n          form.cirText = [];\r\n        }\r\n        if (form.duliText) {\r\n          let duliTextList = form.duliText.split(',');\r\n          let duliId = []\r\n          for (let t of duliTextList) {\r\n            duliId.push(parseInt(t))\r\n          }\r\n          form.duliText = duliId\r\n        } else {\r\n          form.duliText = [];\r\n        }\r\n        if (form.gxxc) {\r\n          form.gxxc = form.gxxc.split(\",\");\r\n        } else {\r\n          form.gxxc = [];\r\n        }\r\n        if (form.gxxcOther) {\r\n          form.gxxcOther = form.gxxcOther.split(\",\");\r\n        } else {\r\n          form.gxxcOther = [];\r\n        }\r\n        if (form.zybw) {\r\n          form.zybw = form.zybw.split(\",\");\r\n        } else {\r\n          form.zybw = [];\r\n        }\r\n        if (form.syrq) {\r\n          form.syrq = form.syrq.split(\",\");\r\n        } else {\r\n          form.syrq = [];\r\n        }\r\n        if (form.cpjx) {\r\n          form.cpjx = form.cpjx.split(\",\");\r\n        } else {\r\n          form.cpjx = [];\r\n        }\r\n        if (form.pflx) {\r\n          form.pflx = form.pflx.split(\",\");\r\n        } else {\r\n          form.pflx = [];\r\n        }\r\n        if (form.ranfalei) {\r\n          form.ranfalei = form.ranfalei.split(\",\");\r\n        } else {\r\n          form.ranfalei = [];\r\n        }\r\n        if (form.cosmeticCaseFirst) {\r\n          form.cosmeticCaseFirst = form.cosmeticCaseFirst.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseFirst = [];\r\n        }\r\n        if (form.cosmeticCaseSecond) {\r\n          form.cosmeticCaseSecond = form.cosmeticCaseSecond.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseSecond = [];\r\n        }\r\n        if (form.qubanmeibailei) {\r\n          form.qubanmeibailei = form.qubanmeibailei.split(\",\");\r\n        } else {\r\n          form.qubanmeibailei = [];\r\n        }\r\n        if (form.introFile) {\r\n          form.introFile = JSON.parse(form.introFile);\r\n        } else {\r\n          form.introFile = [];\r\n        }\r\n        if (form.wendingxingFile) {\r\n          form.wendingxingFile = JSON.parse(form.wendingxingFile);\r\n        } else {\r\n          form.wendingxingFile = [];\r\n        }\r\n        if (form.gongyiFile) {\r\n          form.gongyiFile = JSON.parse(form.gongyiFile);\r\n        } else {\r\n          form.gongyiFile = [];\r\n        }\r\n        if (form.xiangrongxingFile) {\r\n          form.xiangrongxingFile = JSON.parse(form.xiangrongxingFile);\r\n        } else {\r\n          form.xiangrongxingFile = [];\r\n        }\r\n        if (form.weishenwuFile) {\r\n          form.weishenwuFile = JSON.parse(form.weishenwuFile);\r\n        } else {\r\n          form.weishenwuFile = [];\r\n        }\r\n        if (form.xiaofeizheFile) {\r\n          form.xiaofeizheFile = JSON.parse(form.xiaofeizheFile);\r\n        } else {\r\n          form.xiaofeizheFile = [];\r\n        }\r\n        if (form.qitaFile) {\r\n          form.qitaFile = JSON.parse(form.qitaFile);\r\n        } else {\r\n          form.qitaFile = [];\r\n        }\r\n        let itemNames = [];\r\n        if(form.itemArr){\r\n          itemNames = form.itemArr;\r\n        }\r\n        this.itemNames = itemNames;\r\n\r\n        let pFormulaMapData = [];\r\n        if(form.pFormulaMapData){\r\n          pFormulaMapData = form.pFormulaMapData;\r\n        }\r\n        this.pFormulaMapData = pFormulaMapData;\r\n        //获取信息数据\r\n        let execNumberId = form.execNumberId;\r\n        if(execNumberId){\r\n          this.zxbzChange(execNumberId);\r\n        }else{\r\n          this.zxbzDetail = {};\r\n        }\r\n        let jcxmJson = form.jcxmJson;\r\n        if(jcxmJson){\r\n           this.itemArray = JSON.parse(jcxmJson);\r\n        }else{\r\n          this.itemArray = [];\r\n        }\r\n        this.form = form;\r\n        let gongyijianshu = form.gongyijianshu;\r\n        if(gongyijianshu){\r\n          let gyjsData = JSON.parse(gongyijianshu);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.refreshFormulaLegalGy('0');\r\n            this.gyjsDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsDataList = [];\r\n          this.zfylDataList = [];\r\n          this.refreshFormulaLegalGy('0');\r\n        }\r\n        let gongyijianshuBeian = form.gongyijianshuBeian;\r\n        if(gongyijianshuBeian){\r\n          let gyjsData = JSON.parse(gongyijianshuBeian);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsBeianDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.gyjsBeianDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylBeianDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylBeianDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsBeianDataList = [];\r\n          this.zfylBeianDataList = [];\r\n        }\r\n        this.open = true;\r\n        if(isLook){\r\n          this.title = \"修改配方\";\r\n        }else{\r\n          this.title = \"查看配方\";\r\n        }\r\n        this.isLook = isLook;\r\n        this.btnLoading = false;\r\n      });\r\n      let recipeChangeHistoryData = await queryFormualMaterialRecipeChangeHistoryData({id});\r\n      this.recipeChangeHistoryData = recipeChangeHistoryData;\r\n      //获取spec内容\r\n      this.queryMaterialFormulaSpecDataList(id);\r\n      //获取关联稳定性记录内容\r\n      this.queryFormulaStabilityRecordDataList(id);\r\n    },\r\n    stabilityStatusFormat(row) {\r\n      const arr = this.stabilityStatusOptions.filter(i=> i.value === row.stabilityStatus)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    ypFormat(row) {\r\n      const arr = this.ypFromOptions.filter(i=> i.value === row.ypFrom)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    async queryMaterialFormulaSpecDataList(id) {\r\n      let softwareFormulaSpecList = await queryMaterialFormulaSpecDataList({id});\r\n      this.softwareFormulaSpecList = softwareFormulaSpecList;\r\n    },\r\n    async queryFormulaStabilityRecordDataList(id) {\r\n      let formulaStabilityObj = await queryFormulaStabilityRecordDataList({id});\r\n      let relationStabilityDataList = formulaStabilityObj.relationStabilityDataList;\r\n      let stabilityDataList = formulaStabilityObj.stabilityDataList;\r\n      this.stabilityDataList = stabilityDataList\r\n      this.relationStabilityDataList = relationStabilityDataList\r\n    },\r\n    async copyGongyi() {\r\n      await this.$confirm('是否确认复制工艺数据,会清空已填数据!')\r\n      this.gyjsBeianDataList = JSON.parse(JSON.stringify(this.gyjsDataList));\r\n      this.zfylBeianDataList = JSON.parse(JSON.stringify(this.zfylDataList));\r\n    },\r\n    async submitUploadForm() {\r\n      this.btnLoading = true;\r\n      let formulaImage = this.form.formulaImage;\r\n      let formulaConstructionIdeas = this.form.formulaConstructionIdeas;\r\n      let id = this.form.id;\r\n      let remark = this.form.remark;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n      let params = {\r\n        id,formulaImage,formulaMaterialDatas,remark,formulaConstructionIdeas\r\n      };\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaImg(params);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitUploadFileForm() {\r\n      this.btnLoading = true;\r\n      let form = this.form;\r\n      let param = {};\r\n      param.id = form.id;\r\n      if (form.introFile) {\r\n        param.introFile = JSON.stringify(form.introFile);\r\n      } else {\r\n        param.introFile = \"\";\r\n      }\r\n      if (form.wendingxingFile) {\r\n        param.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      } else {\r\n        param.wendingxingFile = \"\";\r\n      }\r\n      if (form.gongyiFile) {\r\n        param.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      } else {\r\n        param.gongyiFile = \"\";\r\n      }\r\n      if (form.xiangrongxingFile) {\r\n        param.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      } else {\r\n        param.xiangrongxingFile = \"\";\r\n      }\r\n      if (form.weishenwuFile) {\r\n        param.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      } else {\r\n        param.weishenwuFile = \"\";\r\n      }\r\n      if (form.xiaofeizheFile) {\r\n        param.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      } else {\r\n        param.xiaofeizheFile = \"\";\r\n      }\r\n      if (form.qitaFile) {\r\n        param.qitaFile = JSON.stringify(form.qitaFile);\r\n      } else {\r\n        param.qitaFile = \"\";\r\n      }\r\n      param.wendingxingResult = form.wendingxingResult;\r\n      param.wendingxingRemark = form.wendingxingRemark;\r\n      param.xiangrongxingResult = form.xiangrongxingResult;\r\n      param.xiangrongxingRemark = form.xiangrongxingRemark;\r\n      param.weishenwuResult = form.weishenwuResult;\r\n      param.weishenwuRemark = form.weishenwuRemark;\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaFileImg(param);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitForm(isDraft) {\r\n      if(isDraft===0){\r\n        await this.$refs[\"form\"].validate()\r\n      }\r\n      let form = Object.assign({},this.form);\r\n      let projectNo = form.projectNo;\r\n      if(!projectNo || projectNo.length==0){\r\n        this.msgError('请选择项目');\r\n        return;\r\n      }\r\n      let categoryText = form.categoryText;\r\n      if(!categoryText || categoryText.length==0){\r\n        this.msgError('请选择配方类别');\r\n        return;\r\n      }\r\n      form.isDraft = isDraft;\r\n      if(form.categoryText && form.categoryText.length > 0 && isArray(form.categoryText)) {\r\n        form.categoryText = form.categoryText.join(',')\r\n      }else{\r\n        form.categoryText = '';\r\n      }\r\n      if(form.cirText && form.cirText.length > 0 && isArray(form.cirText)) {\r\n        form.cirText = form.cirText.join(',')\r\n      }else{\r\n        form.cirText = '';\r\n      }\r\n      if(form.duliText && form.duliText.length > 0 && isArray(form.duliText)) {\r\n        form.duliText = form.duliText.join(',')\r\n      }else{\r\n        form.duliText = '';\r\n      }\r\n      if(form.gxxc){\r\n        form.gxxc = form.gxxc.join(',');\r\n      }else{\r\n        form.gxxc = '';\r\n      }\r\n      if(form.gxxcOther){\r\n        form.gxxcOther = form.gxxcOther.join(',');\r\n      }else{\r\n        form.gxxcOther = '';\r\n      }\r\n      if(form.zybw){\r\n        form.zybw = form.zybw.join(',');\r\n      }else{\r\n        form.zybw = '';\r\n      }\r\n      if(form.syrq){\r\n        form.syrq = form.syrq.join(',');\r\n      }else{\r\n        form.syrq = '';\r\n      }\r\n      if(form.cpjx){\r\n        form.cpjx = form.cpjx.join(',');\r\n      }else{\r\n        form.cpjx = '';\r\n      }\r\n      if(form.pflx){\r\n        form.pflx = form.pflx.join(',');\r\n      }else{\r\n        form.pflx = '';\r\n      }\r\n      if(form.ranfalei){\r\n        form.ranfalei = form.ranfalei.join(',');\r\n      }else{\r\n        form.ranfalei = '';\r\n      }\r\n      if(form.cosmeticCaseFirst){\r\n        form.cosmeticCaseFirst = form.cosmeticCaseFirst.join(',');\r\n      }else{\r\n        form.cosmeticCaseFirst = '';\r\n      }\r\n      if(form.cosmeticCaseSecond){\r\n        form.cosmeticCaseSecond = form.cosmeticCaseSecond.join(',');\r\n      }else{\r\n        form.cosmeticCaseSecond = '';\r\n      }\r\n      if(form.qubanmeibailei){\r\n        form.qubanmeibailei = form.qubanmeibailei.join(',');\r\n      }else{\r\n        form.qubanmeibailei = '';\r\n      }\r\n      if(form.introFile){\r\n        form.introFile = JSON.stringify(form.introFile);\r\n      }else{\r\n        form.introFile = '';\r\n      }\r\n      if(form.wendingxingFile){\r\n        form.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      }else{\r\n        form.wendingxingFile = '';\r\n      }\r\n      if(form.gongyiFile){\r\n        form.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      }else{\r\n        form.gongyiFile = '';\r\n      }\r\n      if(form.xiangrongxingFile){\r\n        form.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      }else{\r\n        form.xiangrongxingFile = '';\r\n      }\r\n      if(form.weishenwuFile){\r\n        form.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      }else{\r\n        form.weishenwuFile = '';\r\n      }\r\n      if(form.xiaofeizheFile){\r\n        form.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      }else{\r\n        form.xiaofeizheFile = '';\r\n      }\r\n      if(form.qitaFile){\r\n        form.qitaFile = JSON.stringify(form.qitaFile);\r\n      }else{\r\n        form.qitaFile = '';\r\n      }\r\n      if(form.fangshailei){\r\n        form.fangshailei = 1;\r\n      }else{\r\n        form.fangshailei = 0;\r\n      }\r\n      if(form.xingongxiao){\r\n        form.xingongxiao = 1;\r\n      }else{\r\n        form.xingongxiao = 0;\r\n      }\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n        for(let item of formulaMaterialDatas){\r\n          let designatedUse = item.designatedUse;\r\n          let isRelation = item.isRelation;\r\n          let isFx = item.isFx;\r\n          let remark = item.remark;\r\n          let relationCode = item.relationCode;\r\n          let materialCode = item.materialCode;\r\n          let appointCode = item.appointCode;\r\n          if(designatedUse==='指定' && !appointCode){\r\n            this.msgError('请选择指定原料!');\r\n            return;\r\n          }\r\n          if(isRelation==1 && !remark){\r\n            this.msgError('请输入使用代码['+materialCode+']的备注,存在推荐原料['+relationCode+']');\r\n            return;\r\n          }\r\n          if(isFx==1){\r\n            let msg = materialCode + \"为护肤风险原料，请核实!\";\r\n            await this.$confirm(msg, \"警告\", {\r\n              confirmButtonText: \"确定\",\r\n              cancelButtonText: \"取消\",\r\n              type: \"warning\"\r\n            })\r\n          }\r\n        }\r\n        form.formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n        let returnObj = this.isRepeat(formulaMaterialDatas);\r\n        let num = returnObj.num;\r\n        if(num>0){\r\n          let repeatCode = returnObj.repeatCode;\r\n          await this.$confirm('存在重复原料'+repeatCode+',是否确认添加!')\r\n        }\r\n      }else{\r\n        await this.$confirm('您还没有选择原料，确定添加配方？');\r\n        form.formulaMaterialDatas = '';\r\n      }\r\n      if(!form.pflx && isDraft===0){\r\n         this.msgError('请选择使用方法!');\r\n         return;\r\n      }\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.form.currentVersion = parseFloat(this.form.currentVersion) + 1;\r\n          this.msgSuccess(\"修改成功\")\r\n          //this.close();\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        try {\r\n          this.btnLoading = true\r\n          await addSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"新增成功\")\r\n          this.close();\r\n         } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    close() {\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      let view = {\r\n        fullPath : '/rd/softwareDevelopingFormula',\r\n        name:\"SoftwareDevelopingFormula\",\r\n        path:\"/rd/softwareDevelopingFormula\",\r\n        title:\"研发配方\"\r\n      };\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    //判断是否重复\r\n    isRepeat(datas){\r\n       let returnObj = {num:0,repeatCode:''};\r\n       let repeatCodesSet = new Set();\r\n      if(datas && datas.length>0){\r\n         let codes = [];\r\n         for(let item of datas){\r\n            codes.push(item.materialCode);\r\n         }\r\n         for(let code of codes){\r\n             let index = 0;\r\n             for(let item of datas){\r\n                 let materialCode = item.materialCode;\r\n                 if(code === materialCode){\r\n                   index++;\r\n                 }\r\n             }\r\n             if(index>1){\r\n               repeatCodesSet.add(code);\r\n             }\r\n         }\r\n       }\r\n       if(repeatCodesSet && repeatCodesSet.size>0){\r\n         let str = JSON.stringify(Array.from(repeatCodesSet));\r\n         returnObj = {num:1,repeatCode:str};\r\n       }\r\n       return returnObj;\r\n    },\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除研发配方编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delSoftwareDevelopingFormula(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有研发配方数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.exportLoading = true;\r\n          return exportSoftwareDevelopingFormula(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    async queryMaterialCode() {\r\n      let materialCode = this.form.materialCode;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (materialCode) {\r\n        let res = await getRawMaterialInfoByCode({materialCode});\r\n        if(res.data){\r\n          let isRelation = res.data.isRelation;\r\n           if(isRelation==1){\r\n             let tipsInfo = res.data.tipsInfo;\r\n             this.msgInfo(tipsInfo);\r\n          }\r\n          formulaMaterialDatas.unshift(res.data);\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        this.codeChange(1);\r\n      } else {\r\n        this.msgError('请输入原料代码!');\r\n      }\r\n    },\r\n    async queryFormulaCode() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaInfoByCode({formulaCode:formulaCodeParams});\r\n         if(res.data){\r\n          formulaMaterialDatas.unshift(res.data);\r\n          }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入配方编码!');\r\n      }\r\n    },\r\n    async confirmSelectGoods() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaLabNoInfoByCode({laboratoryCode:formulaCodeParams});\r\n         if(res.data){\r\n           formulaMaterialDatas.push(res.data);\r\n         }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入实验室编码!');\r\n      }\r\n    },\r\n    async submitSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.itemArray;\r\n      if(!form.execNumberId){\r\n        this.msgError('请选择执行标准/标准名称');\r\n        return;\r\n      }\r\n      if(!(itemArray &&itemArray.length>0)){\r\n        this.msgError('请选择标准模板');\r\n        return;\r\n      }\r\n      specObj.execNumberId = form.execNumberId;\r\n      specObj.execNumber = form.execNumber;\r\n      specObj.currentTemplateId = this.form.currentTemplateId;\r\n      specObj.formulaId = form.id;\r\n      specObj.itemArray = itemArray;\r\n      specObj.isLock = form.isLock;\r\n      specObj.formulaCode = form.formulaCode;\r\n      specObj.laboratoryCode = form.laboratoryCode;\r\n      specObj.productName = form.productName;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async submitUserSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.userItemArray;\r\n      if(!form.type){\r\n        this.msgError('请选择样品来源');\r\n        return;\r\n      }\r\n      specObj.formulaId = form.id;\r\n      specObj.specId = this.specId;\r\n      specObj.itemArray = itemArray;\r\n      specObj.type = form.type;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingUserFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n        this.specOpen = false;\r\n        this.queryMaterialFormulaSpecDataList(form.id);\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async delFormulaMaterial(row){\r\n      this.formulaMaterialDatas = this.formulaMaterialDatas.filter(x => {\r\n        return x.key != row.key;\r\n      });\r\n    },\r\n    categoryChange(id){\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n      if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        let cosmeticCaseFirst = [];\r\n        if(!cosmeticCaseFirst.includes('1')){\r\n          cosmeticCaseFirst.push('1');\r\n          this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        }\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        this.codeChange(1);\r\n      }\r\n    },\r\n    categoryChangeNew(id){\r\n       let res = 1;\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n       if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        res = 3;\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        res = 2;\r\n      }\r\n      return res;\r\n    },\r\n    async codeChange(type) {\r\n      let code = []\r\n      let form = this.form\r\n      if (form.gxxc.length > 0) {\r\n        code.push(this.efficacyOptions.filter(i => form.gxxc.includes(i.id))\r\n          .sort((n1, n2) => n1.id - n2.id).map(i => i.id).join('/'))\r\n      }\r\n      if (form.zybw.length > 0) {\r\n        code.push(this.zybwOptions.filter(i => form.zybw.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.cpjx.length > 0) {\r\n        code.push(this.cpjxOptions.filter(i => form.cpjx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.syrq.length > 0) {\r\n        code.push(this.syrqOptions.filter(i => form.syrq.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.pflx.length > 0) {\r\n        code.push(this.syffOptions.filter(i => form.pflx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      this.form.cpfldm = code.join('~')\r\n\r\n      if (type == 1) {\r\n        let cosmeticClassification = \"\";\r\n        let cosmeticCase = \"\";\r\n        let gxxc = form.gxxc;\r\n        let gxxc1 = ['A', '3', '4', '1', '2', '5'];\r\n        let gxxc2 = ['14', '15', '19', '6', '23', '25'];\r\n\r\n        let zybw = form.zybw;\r\n        let zybw1 = ['B'];\r\n\r\n        let cpjx = form.cpjx;\r\n        let cpjx2 = ['9', '10'];\r\n\r\n        let syrq = form.syrq;\r\n        let syrq1 = ['C', '1', '2'];\r\n        let syrq2 = ['1', '2'];\r\n        let isProcess = true;\r\n        let cosmeticCaseFirst = [];\r\n        let cosmeticCaseSecond = [];\r\n        if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          if(this.arrayContainsAnother(syrq,syrq2)){\r\n            if(!cosmeticCaseFirst.includes('2')){\r\n              cosmeticCaseFirst.push('2');\r\n            }\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc2)){\r\n          if(!cosmeticCaseSecond.includes('3')){\r\n            cosmeticCaseSecond.push('3');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(cpjx, cpjx2)){\r\n          if(!cosmeticCaseSecond.includes('4')){\r\n            cosmeticCaseSecond.push('4');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(zybw, zybw1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(gxxc, gxxc2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else if (this.arrayContainsAnother(cpjx, cpjx2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '2';\r\n          let res = await this.categoryChangeNew(1);\r\n          if(res===1 || res ===3){\r\n            isProcess = false;\r\n          }else{\r\n            let formulaMaterialDatas = this.formulaMaterialDatas;\r\n            let isFirst = false;\r\n            let isSecond = false;\r\n            for(let item of formulaMaterialDatas){\r\n                let isNewMaterial = item.isNewMaterial;\r\n                let inicNmjyl = item.inicNmjyl;\r\n                if(isNewMaterial=='是'){\r\n                  isFirst = true;\r\n                }else if(inicNmjyl=='是'){\r\n                  isSecond = true;\r\n                }\r\n            }\r\n            if(isFirst){\r\n              cosmeticClassification = '1';\r\n              isProcess = true;\r\n            }else if(isSecond){\r\n              cosmeticClassification = '2';\r\n              cosmeticCase = '1';\r\n              isProcess = true;\r\n            }\r\n          }\r\n        }\r\n        let res1 = await this.categoryChangeNew(1);\r\n        if(res1==3){\r\n          if(!cosmeticCaseFirst.includes('1')){\r\n            cosmeticCaseFirst.push('1');\r\n          }\r\n        }\r\n        let formulaMaterialDatas = this.formulaMaterialDatas;\r\n        for(let item of formulaMaterialDatas){\r\n          let isNewMaterial = item.isNewMaterial;\r\n          let inicNmjyl = item.inicNmjyl;\r\n          let symdInfo = item.symdInfo;\r\n          if(isNewMaterial=='是'){\r\n            if(!cosmeticCaseFirst.includes('3')){\r\n              cosmeticCaseFirst.push('3');\r\n            }\r\n          }\r\n          if(inicNmjyl=='是'){\r\n            if(!cosmeticCaseSecond.includes('1')){\r\n              cosmeticCaseSecond.push('1');\r\n            }\r\n          }\r\n          if(symdInfo.indexOf('防晒剂')!=-1 ||symdInfo.indexOf('光稳定剂')!=-1 ){\r\n            if(!cosmeticCaseSecond.includes('2')){\r\n              cosmeticCaseSecond.push('2');\r\n            }\r\n          }\r\n        }\r\n        this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        this.form.cosmeticCaseSecond = cosmeticCaseSecond;\r\n        if(isProcess){\r\n          this.form.cosmeticClassification = cosmeticClassification;\r\n          this.form.cosmeticCase = cosmeticCase;\r\n        }\r\n\r\n      }\r\n    },\r\n    arrayContainsAnother(arr1, arr2) {\r\n      return arr1.some(item => arr2.includes(item));\r\n    },\r\n    toChoose(){\r\n       this.visible = true;\r\n    },\r\n    async selected(formulaId, laboratoryCode) {\r\n      this.form.oldFormulaCode = laboratoryCode;\r\n      this.form.copyFormulaId = formulaId;\r\n      this.visible = false;\r\n      let formulaMaterialDatas = [];\r\n      if (laboratoryCode && formulaId) {\r\n        let res = await getFormulaLabNoInfoByCode({id: formulaId});\r\n        if (res.data) {\r\n          if(res.data.dataList){\r\n            formulaMaterialDatas = res.data.dataList;\r\n          }\r\n          if(res.data.tips){\r\n            this.form.addTips = res.data.tips;\r\n          }\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n          this.codeChange(1);\r\n        }\r\n      } else {\r\n        this.msgError('请选择配方!');\r\n      }\r\n    },\r\n    handleFormulaSpecAdd(){\r\n       this.specOpen = true;\r\n       this.userItemArray = [];\r\n       this.specId = null;\r\n       this.userItemArray = this.itemArray;\r\n    },\r\n    async handleFormulaSpecEdit(row) {\r\n      this.specOpen = true;\r\n      let dataObj = await queryMaterialFormulaSpecDataDetail({id:row.id});\r\n      let jcxmJson = dataObj.jcxmJson;\r\n      if(jcxmJson){\r\n        this.userItemArray = JSON.parse(jcxmJson);\r\n      }else{\r\n        this.userItemArray = [];\r\n      }\r\n      this.form.type = dataObj.type+'';\r\n      this.specId = dataObj.id;\r\n    },\r\n    async refreshFormulaLegalGy(type){\r\n      if(type==='1'){\r\n        await this.$confirm('是否刷新工艺数据,会清空已填数据!')\r\n      }\r\n       let id = this.form.id;\r\n       this.btnLoading = true;\r\n       let data = await queryFormulaLegalGy({id});\r\n       let gyjsData = data.data;\r\n        let gyjs = gyjsData.gyjs;\r\n        if(gyjs){\r\n          this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n        }else{\r\n          this.gyjsDataList = [];\r\n        }\r\n        let zfyl = gyjsData.zfyl;\r\n        if(gyjs){\r\n          this.zfylDataList = zfyl.map(name => ({ name }));;\r\n        }else{\r\n          this.zfylDataList = [];\r\n        }\r\n       this.btnLoading = false;\r\n    },\r\n    //提交特殊原料信息\r\n    async submitTipsMaterialFormulaInfo() {\r\n      let formulaId = this.form.id;\r\n      let specMaterialDatas = this.specMaterialDatas;\r\n      this.btnLoading = true;\r\n      try {\r\n        let res = await addFormulaSpecMaterialData({id:formulaId,specMaterialDatas: JSON.stringify(specMaterialDatas)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    //提交配方使用目的\r\n    async submitSymdInfo() {\r\n      let formulaId = this.form.id;\r\n      this.btnLoading = true;\r\n      try {\r\n        let formulaSymd = [];\r\n        let compositionTableDataList = this.compositionTableDataList;\r\n        for(let item of compositionTableDataList){\r\n          formulaSymd.push({\r\n            chiName:item.chiName,\r\n            cppfSymd:item.cppfSymd,\r\n          });\r\n        }\r\n        let res = await addFormulaSymdForm({id:formulaId,formulaSymd: JSON.stringify(compositionTableDataList)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    getSummaries(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例(%)'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    getSummariesPFormula(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    formulaMaterialBack(o) {\r\n      let status = o.row.status;\r\n      if(status){\r\n        if(status==3){\r\n          return {\r\n            background: 'orange'\r\n          }\r\n        }else if(status==4 || status==5){\r\n          return {\r\n            background: '#8cc0a8'\r\n            // background: '#9999ff'\r\n          }\r\n        }else if(status>0){\r\n          return {\r\n            background: 'red'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionTableStyle(o) {\r\n      let percert = o.row.percert;\r\n      let isColor = o.row.isColor;\r\n      if(isColor==1){\r\n        return {\r\n          color: 'red'\r\n        }\r\n      }else{\r\n        if(percert<=0.1){\r\n          return {\r\n            color: 'blue'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionCellTableStyle({ row, column }) {\r\n      if (column.label === '中文名称' || column.label === 'INCI 中文名') {\r\n        if (row.isTips===1) {\r\n          return \"background:#9966FF\";\r\n        }\r\n      }\r\n    },\r\n    materialDetails(row){\r\n      if(row.type==0){\r\n        this.$nextTick(async () => {\r\n          this.$refs.softwareMaterialSave.reset();\r\n          this.$refs.softwareMaterialSave.init();\r\n          let dataRow = {id:row.materialId};\r\n          this.$refs.softwareMaterialSave.handleUpdate(dataRow,'查看原料',0);\r\n          this.$refs.softwareMaterialSave.open = true;\r\n        });\r\n      }\r\n    },\r\n    selectable(row,index){\r\n      if (row.isUse == 1) {\r\n        return false\r\n      } else {\r\n        return true\r\n      }\r\n    },\r\n    async addFormulaGyjsBeianInfo(type) {\r\n      let gongyijianshuBeian = {};\r\n      gongyijianshuBeian.gyjs = this.gyjsDataList.map(item => item.name);\r\n      gongyijianshuBeian.zfyl = this.zfylDataList.map(item => item.name);\r\n      let param = {\r\n        id: this.form.id,\r\n        gongyijianshu: JSON.stringify(gongyijianshuBeian),\r\n        type\r\n      };\r\n      if(type===1){\r\n        gongyijianshuBeian.gyjs = this.gyjsBeianDataList.map(item => item.name);\r\n        gongyijianshuBeian.zfyl = this.zfylBeianDataList.map(item => item.name);\r\n        param = {\r\n          id: this.form.id,\r\n          gongyijianshuBeian: JSON.stringify(gongyijianshuBeian),\r\n          type\r\n        };\r\n      }\r\n      let res = await addFormulaGyjsBeianInfo(param);\r\n      this.msgSuccess('保存成功!');\r\n    },\r\n    async changeFun(){\r\n      this.changeTemplate(0);\r\n    },\r\n    async changeTemplate(type) {\r\n      if(this.form.currentTemplateId) {\r\n        try {\r\n          if(type===1){\r\n            await this.$confirm('是否确认带入,会清空已填数据!')\r\n          }\r\n          this.btnLoading = true\r\n          const res = await getBcpTemplate(this.form.currentTemplateId)\r\n          if (res.code === 200 && res.data && res.data.itemArray) {\r\n            let itemArray = JSON.parse(res.data.itemArray);\r\n            for(let item of itemArray){\r\n              item.standardVal = '';\r\n            }\r\n            this.itemArray = itemArray;\r\n          }\r\n          this.btnLoading = false\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    delItem(i) {\r\n      this.itemArray.splice(i,1)\r\n    },\r\n    selectProject() {\r\n      this.categoryOpen = true\r\n      this.xmIds = []\r\n    },\r\n    selectCategory(category) {\r\n      for (const item of category.array) {\r\n        this.selectXm(item.id)\r\n      }\r\n    },\r\n    selectXm(id) {\r\n      if(this.xmIds.includes(id)) {\r\n        this.xmIds = this.xmIds.filter(i=>i !== id)\r\n      } else {\r\n        this.xmIds.push(id)\r\n      }\r\n    },\r\n    confirmXm() {\r\n      if(this.xmIds.length) {\r\n        let arr = this.jcXmList.filter(i=> this.xmIds.includes(i.id))\r\n        const itemArray = this.itemArray\r\n        for (const a of arr) {\r\n          if(!itemArray.map(i=>i.id).includes(a.id)) {\r\n            const o = {\r\n              id: a.id,\r\n              label: a.title,\r\n              type: a.category,\r\n              serialNo: a.seq,\r\n              standard: a.standard,\r\n              frequency: a.frequency,\r\n              standardVal: a.standardVal,\r\n              yqIds: a.yqIds?a.yqIds.split(',').map(i=>Number(i)):[],\r\n            }\r\n            let methodArray = []\r\n            const dataArray = []\r\n            if(a.methodDesc) {\r\n              const methodTemplate = a.methodDesc.replace(/（/g,'(').replace(/）/g,')').replace(/\\s/g,\"\")\r\n              o.methodTemplate = methodTemplate.replace(/\\(\\{param}\\)/g,'_____')\r\n              methodArray = o.methodTemplate.split('_____')\r\n              for (let i = 0; i < methodArray.length -1; i++) {\r\n                const o = {}\r\n                o['param_ava_' + i] = ''\r\n                dataArray.push(o)\r\n              }\r\n            }\r\n            o.methodArray = methodArray\r\n            o.dataArray = dataArray\r\n            itemArray.push(o)\r\n          }\r\n        }\r\n        itemArray.sort((a,b)=>a.serialNo - b.serialNo)\r\n        this.categoryOpen = false\r\n      }\r\n    },\r\n    async handleShare(row) {\r\n      this.shareOpen = true;\r\n      let shareDeptDatas = await queryFormulaShareDeptDataList({id:row.deptId});\r\n      this.shareDeptDatas = shareDeptDatas;\r\n      let shareDeptIds = await queryFormulaShareDeptDataDetail({id:row.id});\r\n      this.shareDeptIds = shareDeptIds;\r\n      let checkRow = {\r\n        id:row.id,\r\n        deptId:row.deptId\r\n      };\r\n      this.checkRow = checkRow;\r\n     },\r\n    async submitShareFormulaInfo(){\r\n       let shareDeptIds = this.shareDeptIds;\r\n       if(shareDeptIds && shareDeptIds.length>0){\r\n          this.btnLoading = true;\r\n          let checkRow = this.checkRow;\r\n          checkRow.shareDeptIds = shareDeptIds.join(\",\");\r\n          let res = await addFormulaShareDataInfo(checkRow);\r\n          this.shareOpen = false;\r\n          this.btnLoading = false;\r\n          this.msgSuccess('操作成功');\r\n       }else {\r\n          this.msgError('请选择要分享的部门!');\r\n       }\r\n    },\r\n    handleCompositionQuery(){\r\n       let ewgColor = this.queryParams.ewgColor;\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let compositionTableDataList = this.compositionTableDataListBack;\r\n       if(ewgColor){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.dataObj.ewgColor === ewgColor);\r\n       }\r\n       if(comclusionType){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.compositionTableDataList = compositionTableDataList;\r\n    },\r\n    resetCompositionQuery(){\r\n      this.compositionTableDataList = this.compositionTableDataListBack;\r\n      this.queryParams.ewgColor = null;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    handleMaterialQuery(){\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let formulaTableDataList = this.formulaTableDataListBack;\r\n       if(comclusionType){\r\n         formulaTableDataList = formulaTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.formulaTableDataList = formulaTableDataList;\r\n    },\r\n    resetMaterialQuery(){\r\n      this.formulaTableDataList = this.formulaTableDataListBack;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    isEditStandard(id){\r\n       let isOpr = true;\r\n       let arr = [1,2,7];\r\n       if(arr.includes(id)){\r\n         isOpr = false;\r\n       }\r\n       return isOpr;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.select-wrapper {\r\n  .item {\r\n    height: 24px;\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n    border: 1px solid #DCDFE6;\r\n    border-radius: 2px;\r\n    box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n    margin-top: 5px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .selected {\r\n    color: #00afff;\r\n  }\r\n}\r\n</style>\r\n"]}]}