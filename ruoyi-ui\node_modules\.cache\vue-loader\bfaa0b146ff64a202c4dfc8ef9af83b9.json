{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue?vue&type=template&id=bd747408&scoped=true", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue", "mtime": 1754031707465}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}