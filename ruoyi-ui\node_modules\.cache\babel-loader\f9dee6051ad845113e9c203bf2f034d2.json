{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue", "mtime": 1754032835659}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "require", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "auditList", "title", "open", "queryParams", "pageNum", "pageSize", "productCode", "laboratoryCode", "manufacturer", "productName", "plannedProductionDate", "deliveryDate", "productType", "registrationCompletionHoverTip", "canProduce", "canDeliver", "form", "rules", "required", "message", "trigger", "created", "getList", "methods", "_this", "listAudit", "then", "response", "rows", "cancel", "reset", "id", "productId", "spec", "orderQuantity", "formulaStabilityReport", "formulaStabilityReportHoverTip", "formulaFeasibilityAssessment", "formulaFeasibilityAssessmentHoverTip", "standardFormulaProcess", "moldToolConfirmation", "moldToolConfirmationHoverTip", "fillingPackagingFeasibility", "fillingPackagingFeasibilityHoverTip", "fillingPackagingSop", "finishedProductStandard", "qualityAgreement", "qualityAgreementHoverTip", "packagingMaterialStandard", "liquidSample", "packagingMaterialSample", "finishedProductSample", "excessivePackagingConfirmation", "excessivePackagingConfirmationHoverTip", "registrationCompletion", "formulaProcessConsistency", "formulaProcessConsistencyHoverTip", "documentationConsistency", "documentationConsistencyHoverTip", "internalStandardCompliance", "delFlag", "createBy", "createTime", "updateBy", "updateTime", "remark", "canProduceRemark", "canDeliverRemark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "get<PERSON><PERSON><PERSON>", "submitForm", "_this3", "$refs", "validate", "valid", "updateAudit", "msgSuccess", "add<PERSON><PERSON><PERSON>", "handleDelete", "_this4", "$confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/qc/audit/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form\n      :model=\"queryParams\"\n      ref=\"queryForm\"\n      size=\"small\"\n      :inline=\"true\"\n      v-show=\"showSearch\"\n      label-width=\"68px\"\n    >\n      <el-form-item label=\"产品代码\" prop=\"productCode\">\n        <el-input\n          v-model=\"queryParams.productCode\"\n          placeholder=\"请输入产品代码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"实验室编号\" label-width=\"100px\" prop=\"laboratoryCode\">\n        <el-select\n          v-model=\"queryParams.laboratoryCode\"\n          placeholder=\"请选择实验室编号\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"生产企业\" prop=\"manufacturer\">\n        <el-select\n          v-model=\"queryParams.manufacturer\"\n          placeholder=\"请选择生产企业\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"产品名称\" prop=\"productName\">\n        <el-input\n          v-model=\"queryParams.productName\"\n          placeholder=\"请输入产品名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"预计生产日期\" label-width=\"110px\" prop=\"plannedProductionDate\">\n        <el-date-picker\n          clearable\n          v-model=\"queryParams.plannedProductionDate\"\n          type=\"date\"\n          value-format=\"yyyy-MM-dd\"\n          placeholder=\"请选择预计生产日期\"\n        >\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\n        <el-date-picker\n          clearable\n          v-model=\"queryParams.deliveryDate\"\n          type=\"date\"\n          value-format=\"yyyy-MM-dd\"\n          placeholder=\"请选择产品交期\"\n        >\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"产品类型\" prop=\"productType\">\n        <el-select\n          v-model=\"queryParams.productType\"\n          placeholder=\"请选择产品类型\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\n        <el-input\n          v-model=\"queryParams.registrationCompletionHoverTip\"\n          placeholder=\"请输入备案号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <!-- <el-form-item label=\"是否可生产\" prop=\"canProduce\">\n        <el-input\n          v-model=\"queryParams.canProduce\"\n          placeholder=\"请输入是否可生产\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item> -->\n      <!-- <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\n        <el-input\n          v-model=\"queryParams.canDeliver\"\n          placeholder=\"请输入是否可出库\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item> -->\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          size=\"mini\"\n          @click=\"handleQuery\"\n          >搜索</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n          >重置</el-button\n        >\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['qc:audit:add']\"\n          >新增</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['qc:audit:edit']\"\n          >修改</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['qc:audit:remove']\"\n          >删除</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['qc:audit:export']\"\n          >导出</el-button\n        >\n      </el-col>\n      <right-toolbar\n        :showSearch.sync=\"showSearch\"\n        @queryTable=\"getList\"\n      ></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"auditList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\n      <el-table-column label=\"产品ID\" align=\"center\" prop=\"productId\" />\n      <el-table-column label=\"产品代码\" align=\"center\" prop=\"productCode\" />\n      <el-table-column\n        label=\"实验室编号\"\n        align=\"center\"\n        prop=\"laboratoryCode\"\n      />\n      <el-table-column label=\"生产企业\" align=\"center\" prop=\"manufacturer\" />\n      <el-table-column label=\"产品名称\" align=\"center\" prop=\"productName\" />\n      <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" />\n      <el-table-column\n        label=\"预计生产日期\"\n        align=\"center\"\n        prop=\"plannedProductionDate\"\n        width=\"180\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{\n            parseTime(scope.row.plannedProductionDate, \"{y}-{m}-{d}\")\n          }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"订单数量\" align=\"center\" prop=\"orderQuantity\" />\n      <el-table-column\n        label=\"产品交期\"\n        align=\"center\"\n        prop=\"deliveryDate\"\n        width=\"180\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.deliveryDate, \"{y}-{m}-{d}\") }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"产品类型\" align=\"center\" prop=\"productType\" />\n      <el-table-column\n        label=\"配方稳定性报告状态\"\n        align=\"center\"\n        prop=\"formulaStabilityReport\"\n      />\n      <el-table-column\n        label=\"配方稳定性报告风险内容\"\n        align=\"center\"\n        prop=\"formulaStabilityReportHoverTip\"\n      />\n      <el-table-column\n        label=\"配制可行性评估状态\"\n        align=\"center\"\n        prop=\"formulaFeasibilityAssessment\"\n      />\n      <el-table-column\n        label=\"配制可行性评估风险内容\"\n        align=\"center\"\n        prop=\"formulaFeasibilityAssessmentHoverTip\"\n      />\n      <el-table-column\n        label=\"标准配制工艺单状态\"\n        align=\"center\"\n        prop=\"standardFormulaProcess\"\n      />\n      <el-table-column\n        label=\"生产模具治具确认状态\"\n        align=\"center\"\n        prop=\"moldToolConfirmation\"\n      />\n      <el-table-column\n        label=\"生产模具治具确认预计时间\"\n        align=\"center\"\n        prop=\"moldToolConfirmationHoverTip\"\n      />\n      <el-table-column\n        label=\"灌包可行性评估状态\"\n        align=\"center\"\n        prop=\"fillingPackagingFeasibility\"\n      />\n      <el-table-column\n        label=\"灌包可行性评估风险内容\"\n        align=\"center\"\n        prop=\"fillingPackagingFeasibilityHoverTip\"\n      />\n      <el-table-column\n        label=\"灌装/包装SOP状态\"\n        align=\"center\"\n        prop=\"fillingPackagingSop\"\n      />\n      <el-table-column\n        label=\"成品检验标准状态\"\n        align=\"center\"\n        prop=\"finishedProductStandard\"\n      />\n      <el-table-column\n        label=\"质量协议状态\"\n        align=\"center\"\n        prop=\"qualityAgreement\"\n      />\n      <el-table-column\n        label=\"质量协议合同类型是独立还是主框架合同\"\n        align=\"center\"\n        prop=\"qualityAgreementHoverTip\"\n      />\n      <el-table-column\n        label=\"包材标准状态\"\n        align=\"center\"\n        prop=\"packagingMaterialStandard\"\n      />\n      <el-table-column\n        label=\"料体标样状态\"\n        align=\"center\"\n        prop=\"liquidSample\"\n      />\n      <el-table-column\n        label=\"包材标准样状态\"\n        align=\"center\"\n        prop=\"packagingMaterialSample\"\n      />\n      <el-table-column\n        label=\"成品标样状态\"\n        align=\"center\"\n        prop=\"finishedProductSample\"\n      />\n      <el-table-column\n        label=\"过度包装确认状态\"\n        align=\"center\"\n        prop=\"excessivePackagingConfirmation\"\n      />\n      <el-table-column\n        label=\"过度包装风险内容\"\n        align=\"center\"\n        prop=\"excessivePackagingConfirmationHoverTip\"\n      />\n      <el-table-column\n        label=\"注册备案是否完成状态\"\n        align=\"center\"\n        prop=\"registrationCompletion\"\n      />\n      <el-table-column\n        label=\"备案号\"\n        align=\"center\"\n        prop=\"registrationCompletionHoverTip\"\n      />\n      <el-table-column\n        label=\"大货标准配方工艺单与注册/备案一致性状态\"\n        align=\"center\"\n        prop=\"formulaProcessConsistency\"\n      />\n      <el-table-column\n        label=\"风险内容\"\n        align=\"center\"\n        prop=\"formulaProcessConsistencyHoverTip\"\n      />\n      <el-table-column\n        label=\"大货文案与备案资料一致性状态\"\n        align=\"center\"\n        prop=\"documentationConsistency\"\n      />\n      <el-table-column\n        label=\"风险内容\"\n        align=\"center\"\n        prop=\"documentationConsistencyHoverTip\"\n      />\n      <el-table-column\n        label=\"产品内控标准符合备案执行标准状态\"\n        align=\"center\"\n        prop=\"internalStandardCompliance\"\n      />\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\n      <el-table-column label=\"是否可生产\" align=\"center\" prop=\"canProduce\" />\n      <el-table-column\n        label=\"是否可生产备注\"\n        align=\"center\"\n        prop=\"canProduceRemark\"\n      />\n      <el-table-column label=\"是否可出库\" align=\"center\" prop=\"canDeliver\" />\n      <el-table-column\n        label=\"是否可出库备注\"\n        align=\"center\"\n        prop=\"canDeliverRemark\"\n      />\n      <el-table-column\n        label=\"操作\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['qc:audit:edit']\"\n            >修改</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['qc:audit:remove']\"\n            >删除</el-button\n          >\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改产品准入检查对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"产品ID\" prop=\"productId\">\n          <el-input v-model=\"form.productId\" placeholder=\"请输入产品ID\" />\n        </el-form-item>\n        <el-form-item label=\"产品代码\" prop=\"productCode\">\n          <el-input v-model=\"form.productCode\" placeholder=\"请输入产品代码\" />\n        </el-form-item>\n        <el-form-item label=\"实验室编号\" prop=\"laboratoryCode\">\n          <el-select\n            v-model=\"form.laboratoryCode\"\n            placeholder=\"请选择实验室编号\"\n          >\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"生产企业\" prop=\"manufacturer\">\n          <el-select v-model=\"form.manufacturer\" placeholder=\"请选择生产企业\">\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"产品名称\" prop=\"productName\">\n          <el-input v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\n        </el-form-item>\n        <el-form-item label=\"规格\" prop=\"spec\">\n          <el-input v-model=\"form.spec\" placeholder=\"请输入规格\" />\n        </el-form-item>\n        <el-form-item label=\"预计生产日期\" prop=\"plannedProductionDate\">\n          <el-date-picker\n            clearable\n            v-model=\"form.plannedProductionDate\"\n            type=\"date\"\n            value-format=\"yyyy-MM-dd\"\n            placeholder=\"请选择预计生产日期\"\n          >\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"订单数量\" prop=\"orderQuantity\">\n          <el-input v-model=\"form.orderQuantity\" placeholder=\"请输入订单数量\" />\n        </el-form-item>\n        <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\n          <el-date-picker\n            clearable\n            v-model=\"form.deliveryDate\"\n            type=\"date\"\n            value-format=\"yyyy-MM-dd\"\n            placeholder=\"请选择产品交期\"\n          >\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"产品类型\" prop=\"productType\">\n          <el-select v-model=\"form.productType\" placeholder=\"请选择产品类型\">\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"配方稳定性报告状态\" prop=\"formulaStabilityReport\">\n          <el-input\n            v-model=\"form.formulaStabilityReport\"\n            placeholder=\"请输入配方稳定性报告状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配方稳定性报告风险内容\"\n          prop=\"formulaStabilityReportHoverTip\"\n        >\n          <el-input\n            v-model=\"form.formulaStabilityReportHoverTip\"\n            placeholder=\"请输入配方稳定性报告风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配制可行性评估状态\"\n          prop=\"formulaFeasibilityAssessment\"\n        >\n          <el-input\n            v-model=\"form.formulaFeasibilityAssessment\"\n            placeholder=\"请输入配制可行性评估状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配制可行性评估风险内容\"\n          prop=\"formulaFeasibilityAssessmentHoverTip\"\n        >\n          <el-input\n            v-model=\"form.formulaFeasibilityAssessmentHoverTip\"\n            placeholder=\"请输入配制可行性评估风险内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"标准配制工艺单状态\" prop=\"standardFormulaProcess\">\n          <el-input\n            v-model=\"form.standardFormulaProcess\"\n            placeholder=\"请输入标准配制工艺单状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"生产模具治具确认状态\" prop=\"moldToolConfirmation\">\n          <el-input\n            v-model=\"form.moldToolConfirmation\"\n            placeholder=\"请输入生产模具治具确认状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"生产模具治具确认预计时间\"\n          prop=\"moldToolConfirmationHoverTip\"\n        >\n          <el-input\n            v-model=\"form.moldToolConfirmationHoverTip\"\n            placeholder=\"请输入生产模具治具确认预计时间\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"灌包可行性评估状态\"\n          prop=\"fillingPackagingFeasibility\"\n        >\n          <el-input\n            v-model=\"form.fillingPackagingFeasibility\"\n            placeholder=\"请输入灌包可行性评估状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"灌包可行性评估风险内容\"\n          prop=\"fillingPackagingFeasibilityHoverTip\"\n        >\n          <el-input\n            v-model=\"form.fillingPackagingFeasibilityHoverTip\"\n            placeholder=\"请输入灌包可行性评估风险内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"灌装/包装SOP状态\" prop=\"fillingPackagingSop\">\n          <el-input\n            v-model=\"form.fillingPackagingSop\"\n            placeholder=\"请输入灌装/包装SOP状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"成品检验标准状态\" prop=\"finishedProductStandard\">\n          <el-input\n            v-model=\"form.finishedProductStandard\"\n            placeholder=\"请输入成品检验标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"质量协议状态\" prop=\"qualityAgreement\">\n          <el-input\n            v-model=\"form.qualityAgreement\"\n            placeholder=\"请输入质量协议状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"质量协议合同类型是独立还是主框架合同\"\n          prop=\"qualityAgreementHoverTip\"\n        >\n          <el-input\n            v-model=\"form.qualityAgreementHoverTip\"\n            placeholder=\"请输入质量协议合同类型是独立还是主框架合同\"\n          />\n        </el-form-item>\n        <el-form-item label=\"包材标准状态\" prop=\"packagingMaterialStandard\">\n          <el-input\n            v-model=\"form.packagingMaterialStandard\"\n            placeholder=\"请输入包材标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"料体标样状态\" prop=\"liquidSample\">\n          <el-input\n            v-model=\"form.liquidSample\"\n            placeholder=\"请输入料体标样状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"包材标准样状态\" prop=\"packagingMaterialSample\">\n          <el-input\n            v-model=\"form.packagingMaterialSample\"\n            placeholder=\"请输入包材标准样状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"成品标样状态\" prop=\"finishedProductSample\">\n          <el-input\n            v-model=\"form.finishedProductSample\"\n            placeholder=\"请输入成品标样状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"过度包装确认状态\"\n          prop=\"excessivePackagingConfirmation\"\n        >\n          <el-input\n            v-model=\"form.excessivePackagingConfirmation\"\n            placeholder=\"请输入过度包装确认状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"过度包装风险内容\"\n          prop=\"excessivePackagingConfirmationHoverTip\"\n        >\n          <el-input\n            v-model=\"form.excessivePackagingConfirmationHoverTip\"\n            placeholder=\"请输入过度包装风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"注册备案是否完成状态\"\n          prop=\"registrationCompletion\"\n        >\n          <el-input\n            v-model=\"form.registrationCompletion\"\n            placeholder=\"请输入注册备案是否完成状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\n          <el-input\n            v-model=\"form.registrationCompletionHoverTip\"\n            placeholder=\"请输入备案号\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"大货标准配方工艺单与注册/备案一致性状态\"\n          prop=\"formulaProcessConsistency\"\n        >\n          <el-input\n            v-model=\"form.formulaProcessConsistency\"\n            placeholder=\"请输入大货标准配方工艺单与注册/备案一致性状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"风险内容\" prop=\"formulaProcessConsistencyHoverTip\">\n          <el-input\n            v-model=\"form.formulaProcessConsistencyHoverTip\"\n            placeholder=\"请输入风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"大货文案与备案资料一致性状态\"\n          prop=\"documentationConsistency\"\n        >\n          <el-input\n            v-model=\"form.documentationConsistency\"\n            placeholder=\"请输入大货文案与备案资料一致性状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"风险内容\" prop=\"documentationConsistencyHoverTip\">\n          <el-input\n            v-model=\"form.documentationConsistencyHoverTip\"\n            placeholder=\"请输入风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"产品内控标准符合备案执行标准状态\"\n          prop=\"internalStandardCompliance\"\n        >\n          <el-input\n            v-model=\"form.internalStandardCompliance\"\n            placeholder=\"请输入产品内控标准符合备案执行标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\n          <el-input v-model=\"form.delFlag\" placeholder=\"请输入删除标志\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"是否可生产\" prop=\"canProduce\">\n          <el-input v-model=\"form.canProduce\" placeholder=\"请输入是否可生产\" />\n        </el-form-item>\n        <el-form-item label=\"是否可生产备注\" prop=\"canProduceRemark\">\n          <el-input\n            v-model=\"form.canProduceRemark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\n          <el-input v-model=\"form.canDeliver\" placeholder=\"请输入是否可出库\" />\n        </el-form-item>\n        <el-form-item label=\"是否可出库备注\" prop=\"canDeliverRemark\">\n          <el-input\n            v-model=\"form.canDeliverRemark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listAudit,\n  getAudit,\n  delAudit,\n  addAudit,\n  updateAudit,\n} from \"@/api/qc/audit\";\n\nexport default {\n  name: \"Audit\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 产品准入检查表格数据\n      auditList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        productCode: null,\n        laboratoryCode: null,\n        manufacturer: null,\n        productName: null,\n        plannedProductionDate: null,\n        deliveryDate: null,\n        productType: null,\n        registrationCompletionHoverTip: null,\n        canProduce: null,\n        canDeliver: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        productCode: [\n          { required: true, message: \"产品代码不能为空\", trigger: \"blur\" },\n        ],\n        productName: [\n          { required: true, message: \"产品名称不能为空\", trigger: \"blur\" },\n        ],\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询产品准入检查列表 */\n    getList() {\n      this.loading = true;\n      listAudit(this.queryParams).then((response) => {\n        this.auditList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        productId: null,\n        productCode: null,\n        laboratoryCode: null,\n        manufacturer: null,\n        productName: null,\n        spec: null,\n        plannedProductionDate: null,\n        orderQuantity: null,\n        deliveryDate: null,\n        productType: null,\n        formulaStabilityReport: null,\n        formulaStabilityReportHoverTip: null,\n        formulaFeasibilityAssessment: null,\n        formulaFeasibilityAssessmentHoverTip: null,\n        standardFormulaProcess: null,\n        moldToolConfirmation: null,\n        moldToolConfirmationHoverTip: null,\n        fillingPackagingFeasibility: null,\n        fillingPackagingFeasibilityHoverTip: null,\n        fillingPackagingSop: null,\n        finishedProductStandard: null,\n        qualityAgreement: null,\n        qualityAgreementHoverTip: null,\n        packagingMaterialStandard: null,\n        liquidSample: null,\n        packagingMaterialSample: null,\n        finishedProductSample: null,\n        excessivePackagingConfirmation: null,\n        excessivePackagingConfirmationHoverTip: null,\n        registrationCompletion: null,\n        registrationCompletionHoverTip: null,\n        formulaProcessConsistency: null,\n        formulaProcessConsistencyHoverTip: null,\n        documentationConsistency: null,\n        documentationConsistencyHoverTip: null,\n        internalStandardCompliance: null,\n        delFlag: null,\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        remark: null,\n        canProduce: null,\n        canProduceRemark: null,\n        canDeliver: null,\n        canDeliverRemark: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加产品准入检查\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getAudit(id).then((response) => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改产品准入检查\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateAudit(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addAudit(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除产品准入检查编号为\"' + ids + '\"的数据项？')\n        .then(function () {\n          return delAudit(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download(\n        \"qc/audit/export\",\n        {\n          ...this.queryParams,\n        },\n        `audit_${new Date().getTime()}.xlsx`\n      );\n    },\n  },\n};\n</script>\n"], "mappings": ";;;;;;;;;;;;AAiqBA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAQA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,SAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAC,qBAAA;QACAC,YAAA;QACAC,WAAA;QACAC,8BAAA;QACAC,UAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAX,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,WAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,iBACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,gBAAA,OAAAtB,WAAA,EAAAuB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,SAAA,GAAA2B,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAzB,KAAA,GAAA4B,QAAA,CAAA5B,KAAA;QACAyB,KAAA,CAAA9B,OAAA;MACA;IACA;IACA;IACAmC,MAAA,WAAAA,OAAA;MACA,KAAA3B,IAAA;MACA,KAAA4B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,IAAA;QACAe,EAAA;QACAC,SAAA;QACA1B,WAAA;QACAC,cAAA;QACAC,YAAA;QACAC,WAAA;QACAwB,IAAA;QACAvB,qBAAA;QACAwB,aAAA;QACAvB,YAAA;QACAC,WAAA;QACAuB,sBAAA;QACAC,8BAAA;QACAC,4BAAA;QACAC,oCAAA;QACAC,sBAAA;QACAC,oBAAA;QACAC,4BAAA;QACAC,2BAAA;QACAC,mCAAA;QACAC,mBAAA;QACAC,uBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,yBAAA;QACAC,YAAA;QACAC,uBAAA;QACAC,qBAAA;QACAC,8BAAA;QACAC,sCAAA;QACAC,sBAAA;QACAzC,8BAAA;QACA0C,yBAAA;QACAC,iCAAA;QACAC,wBAAA;QACAC,gCAAA;QACAC,0BAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAnD,UAAA;QACAoD,gBAAA;QACAnD,UAAA;QACAoD,gBAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAkB,OAAA;IACA;IACA,aACAgD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7E,GAAA,GAAA6E,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3C,EAAA;MAAA;MACA,KAAAnC,MAAA,GAAA4E,SAAA,CAAAG,MAAA;MACA,KAAA9E,QAAA,IAAA2E,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA9C,KAAA;MACA,KAAA5B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4E,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAjD,KAAA;MACA,IAAAC,EAAA,GAAA+C,GAAA,CAAA/C,EAAA,SAAApC,GAAA;MACA,IAAAqF,eAAA,EAAAjD,EAAA,EAAAL,IAAA,WAAAC,QAAA;QACAoD,MAAA,CAAA/D,IAAA,GAAAW,QAAA,CAAAlC,IAAA;QACAsF,MAAA,CAAA7E,IAAA;QACA6E,MAAA,CAAA9E,KAAA;MACA;IACA;IACA,WACAgF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAlE,IAAA,CAAAe,EAAA;YACA,IAAAuD,kBAAA,EAAAJ,MAAA,CAAAlE,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhF,IAAA;cACAgF,MAAA,CAAA5D,OAAA;YACA;UACA;YACA,IAAAkE,eAAA,EAAAN,MAAA,CAAAlE,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuD,MAAA,CAAAK,UAAA;cACAL,MAAA,CAAAhF,IAAA;cACAgF,MAAA,CAAA5D,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAmE,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAA/F,GAAA,GAAAmF,GAAA,CAAA/C,EAAA,SAAApC,GAAA;MACA,KAAAgG,QAAA,sBAAAhG,GAAA,aACA+B,IAAA;QACA,WAAAkE,eAAA,EAAAjG,GAAA;MACA,GACA+B,IAAA;QACAgE,MAAA,CAAApE,OAAA;QACAoE,MAAA,CAAAH,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,uBAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA9F,WAAA,YAAA+F,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}