{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue?vue&type=style&index=0&id=ccbd158a&scoped=true&lang=css", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\qc\\audit\\index.vue", "mtime": 1754034864927}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1744596528942}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1744596530059}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1744596529996}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLyog54q25oCB5qCH562+5qC35byP5LyY5YyWICovCi5lbC10YWcgewogIGN1cnNvcjogcG9pbnRlcjsKfQoKLyog5oKs5rWu5o+Q56S65qC35byPICovCi5lbC10b29sdGlwX19wb3BwZXIgewogIG1heC13aWR0aDogMzAwcHg7Cn0KCi8qIOihqOagvOWIl+WuveW6puS8mOWMliAqLwouZWwtdGFibGUgLmNlbGwgewogIHBhZGRpbmc6IDAgOHB4Owp9Cg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6nCA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/qc/audit", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form\n      :model=\"queryParams\"\n      ref=\"queryForm\"\n      size=\"small\"\n      :inline=\"true\"\n      v-show=\"showSearch\"\n      label-width=\"68px\"\n    >\n      <el-form-item label=\"产品代码\" prop=\"productCode\">\n        <el-input\n          v-model=\"queryParams.productCode\"\n          placeholder=\"请输入产品代码\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"实验室编号\" label-width=\"100px\" prop=\"laboratoryCode\">\n        <el-select\n          v-model=\"queryParams.laboratoryCode\"\n          placeholder=\"请选择实验室编号\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"生产企业\" prop=\"manufacturer\">\n        <el-select\n          v-model=\"queryParams.manufacturer\"\n          placeholder=\"请选择生产企业\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"产品名称\" prop=\"productName\">\n        <el-input\n          v-model=\"queryParams.productName\"\n          placeholder=\"请输入产品名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"预计生产日期\" label-width=\"110px\" prop=\"plannedProductionDate\">\n        <el-date-picker\n          clearable\n          v-model=\"queryParams.plannedProductionDate\"\n          type=\"date\"\n          value-format=\"yyyy-MM-dd\"\n          placeholder=\"请选择预计生产日期\"\n        >\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\n        <el-date-picker\n          clearable\n          v-model=\"queryParams.deliveryDate\"\n          type=\"date\"\n          value-format=\"yyyy-MM-dd\"\n          placeholder=\"请选择产品交期\"\n        >\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"产品类型\" prop=\"productType\">\n        <el-select\n          v-model=\"queryParams.productType\"\n          placeholder=\"请选择产品类型\"\n          clearable\n        >\n          <el-option label=\"请选择字典生成\" value=\"\" />\n        </el-select>\n      </el-form-item>\n      <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\n        <el-input\n          v-model=\"queryParams.registrationCompletionHoverTip\"\n          placeholder=\"请输入备案号\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <!-- <el-form-item label=\"是否可生产\" prop=\"canProduce\">\n        <el-input\n          v-model=\"queryParams.canProduce\"\n          placeholder=\"请输入是否可生产\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item> -->\n      <!-- <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\n        <el-input\n          v-model=\"queryParams.canDeliver\"\n          placeholder=\"请输入是否可出库\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item> -->\n      <el-form-item>\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-search\"\n          size=\"mini\"\n          @click=\"handleQuery\"\n          >搜索</el-button\n        >\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\n          >重置</el-button\n        >\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"primary\"\n          plain\n          icon=\"el-icon-plus\"\n          size=\"mini\"\n          @click=\"handleAdd\"\n          v-hasPermi=\"['qc:audit:add']\"\n          >新增</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"success\"\n          plain\n          icon=\"el-icon-edit\"\n          size=\"mini\"\n          :disabled=\"single\"\n          @click=\"handleUpdate\"\n          v-hasPermi=\"['qc:audit:edit']\"\n          >修改</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"danger\"\n          plain\n          icon=\"el-icon-delete\"\n          size=\"mini\"\n          :disabled=\"multiple\"\n          @click=\"handleDelete\"\n          v-hasPermi=\"['qc:audit:remove']\"\n          >删除</el-button\n        >\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button\n          type=\"warning\"\n          plain\n          icon=\"el-icon-download\"\n          size=\"mini\"\n          @click=\"handleExport\"\n          v-hasPermi=\"['qc:audit:export']\"\n          >导出</el-button\n        >\n      </el-col>\n      <right-toolbar\n        :showSearch.sync=\"showSearch\"\n        @queryTable=\"getList\"\n      ></right-toolbar>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"auditList\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <!-- <el-table-column label=\"主键ID\" align=\"center\" prop=\"id\" /> -->\n      <el-table-column label=\"产品ID\" align=\"center\" prop=\"productId\" />\n      <el-table-column label=\"产品代码\" align=\"center\" prop=\"productCode\" />\n      <el-table-column\n        label=\"实验室编号\"\n        align=\"center\"\n        prop=\"laboratoryCode\"\n      />\n      <el-table-column label=\"生产企业\" align=\"center\" prop=\"manufacturer\" />\n      <el-table-column label=\"产品名称\" align=\"center\" prop=\"productName\" />\n      <el-table-column label=\"规格\" align=\"center\" prop=\"spec\" />\n      <el-table-column\n        label=\"预计生产日期\"\n        align=\"center\"\n        prop=\"plannedProductionDate\"\n        width=\"180\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{\n            parseTime(scope.row.plannedProductionDate, \"{y}-{m}-{d}\")\n          }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"订单数量\" align=\"center\" prop=\"orderQuantity\" />\n      <el-table-column\n        label=\"产品交期\"\n        align=\"center\"\n        prop=\"deliveryDate\"\n        width=\"180\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ parseTime(scope.row.deliveryDate, \"{y}-{m}-{d}\") }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"产品类型\" align=\"center\" prop=\"productType\" />\n      <el-table-column\n        label=\"配方稳定性报告状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.formulaStabilityReportHoverTip\"\n            :content=\"scope.row.formulaStabilityReportHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.formulaStabilityReport == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.formulaStabilityReport == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.formulaStabilityReport == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.formulaStabilityReport == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"配制可行性评估状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.formulaFeasibilityAssessmentHoverTip\"\n            :content=\"scope.row.formulaFeasibilityAssessmentHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.formulaFeasibilityAssessment == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.formulaFeasibilityAssessment == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.formulaFeasibilityAssessment == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.formulaFeasibilityAssessment == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"标准配制工艺单状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.standardFormulaProcess == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.standardFormulaProcess == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"生产模具治具确认状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.moldToolConfirmationHoverTip\"\n            :content=\"scope.row.moldToolConfirmationHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.moldToolConfirmation == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.moldToolConfirmation == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.moldToolConfirmation == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.moldToolConfirmation == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"灌包可行性评估状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.fillingPackagingFeasibilityHoverTip\"\n            :content=\"scope.row.fillingPackagingFeasibilityHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.fillingPackagingFeasibility == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.fillingPackagingFeasibility == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.fillingPackagingFeasibility == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.fillingPackagingFeasibility == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"灌装/包装SOP状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.fillingPackagingSop == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.fillingPackagingSop == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"成品检验标准状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.finishedProductStandard == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.finishedProductStandard == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"质量协议状态\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.qualityAgreementHoverTip\"\n            :content=\"scope.row.qualityAgreementHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.qualityAgreement == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.qualityAgreement == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.qualityAgreement == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.qualityAgreement == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"包材标准状态\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.packagingMaterialStandard == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.packagingMaterialStandard == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"料体标样状态\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.liquidSample == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.liquidSample == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"包材标准样状态\"\n        align=\"center\"\n        width=\"130\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.packagingMaterialSample == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.packagingMaterialSample == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"成品标样状态\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.finishedProductSample == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.finishedProductSample == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"过度包装确认状态\"\n        align=\"center\"\n        width=\"140\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.excessivePackagingConfirmationHoverTip\"\n            :content=\"scope.row.excessivePackagingConfirmationHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.excessivePackagingConfirmation == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.excessivePackagingConfirmation == 1 ? '存在' : '不存在' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.excessivePackagingConfirmation == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.excessivePackagingConfirmation == 1 ? '存在' : '不存在' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"注册备案完成状态\"\n        align=\"center\"\n        width=\"140\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.registrationCompletionHoverTip\"\n            :content=\"'备案号：' + scope.row.registrationCompletionHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.registrationCompletion == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.registrationCompletion == 1 ? '已完成' : '未完成' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.registrationCompletion == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.registrationCompletion == 1 ? '已完成' : '未完成' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"配方工艺一致性状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.formulaProcessConsistencyHoverTip\"\n            :content=\"scope.row.formulaProcessConsistencyHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.formulaProcessConsistency == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.formulaProcessConsistency == 1 ? '一致' : '不一致' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.formulaProcessConsistency == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.formulaProcessConsistency == 1 ? '一致' : '不一致' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"文案资料一致性状态\"\n        align=\"center\"\n        width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.documentationConsistencyHoverTip\"\n            :content=\"scope.row.documentationConsistencyHoverTip\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.documentationConsistency == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.documentationConsistency == 1 ? '一致' : '不一致' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.documentationConsistency == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.documentationConsistency == 1 ? '一致' : '不一致' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"内控标准符合状态\"\n        align=\"center\"\n        width=\"140\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.internalStandardCompliance == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.internalStandardCompliance == 1 ? '符合' : '不符合' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" />\n      <el-table-column\n        label=\"是否可生产\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.canProduceRemark\"\n            :content=\"scope.row.canProduceRemark\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.canProduce == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.canProduce == 1 ? '可生产' : '不可生产' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.canProduce == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.canProduce == 1 ? '可生产' : '不可生产' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"是否可出库\"\n        align=\"center\"\n        width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tooltip\n            v-if=\"scope.row.canDeliverRemark\"\n            :content=\"scope.row.canDeliverRemark\"\n            placement=\"top\"\n          >\n            <el-tag\n              :type=\"scope.row.canDeliver == 1 ? 'success' : 'danger'\"\n              size=\"small\"\n            >\n              {{ scope.row.canDeliver == 1 ? '可出库' : '不可出库' }}\n            </el-tag>\n          </el-tooltip>\n          <el-tag\n            v-else\n            :type=\"scope.row.canDeliver == 1 ? 'success' : 'danger'\"\n            size=\"small\"\n          >\n            {{ scope.row.canDeliver == 1 ? '可出库' : '不可出库' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"操作\"\n        align=\"center\"\n        class-name=\"small-padding fixed-width\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['qc:audit:edit']\"\n            >修改</el-button\n          >\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['qc:audit:remove']\"\n            >删除</el-button\n          >\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <!-- 添加或修改产品准入检查对话框 -->\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\n        <el-form-item label=\"产品ID\" prop=\"productId\">\n          <el-input v-model=\"form.productId\" placeholder=\"请输入产品ID\" />\n        </el-form-item>\n        <el-form-item label=\"产品代码\" prop=\"productCode\">\n          <el-input v-model=\"form.productCode\" placeholder=\"请输入产品代码\" />\n        </el-form-item>\n        <el-form-item label=\"实验室编号\" prop=\"laboratoryCode\">\n          <el-select\n            v-model=\"form.laboratoryCode\"\n            placeholder=\"请选择实验室编号\"\n          >\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"生产企业\" prop=\"manufacturer\">\n          <el-select v-model=\"form.manufacturer\" placeholder=\"请选择生产企业\">\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"产品名称\" prop=\"productName\">\n          <el-input v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\n        </el-form-item>\n        <el-form-item label=\"规格\" prop=\"spec\">\n          <el-input v-model=\"form.spec\" placeholder=\"请输入规格\" />\n        </el-form-item>\n        <el-form-item label=\"预计生产日期\" prop=\"plannedProductionDate\">\n          <el-date-picker\n            clearable\n            v-model=\"form.plannedProductionDate\"\n            type=\"date\"\n            value-format=\"yyyy-MM-dd\"\n            placeholder=\"请选择预计生产日期\"\n          >\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"订单数量\" prop=\"orderQuantity\">\n          <el-input v-model=\"form.orderQuantity\" placeholder=\"请输入订单数量\" />\n        </el-form-item>\n        <el-form-item label=\"产品交期\" prop=\"deliveryDate\">\n          <el-date-picker\n            clearable\n            v-model=\"form.deliveryDate\"\n            type=\"date\"\n            value-format=\"yyyy-MM-dd\"\n            placeholder=\"请选择产品交期\"\n          >\n          </el-date-picker>\n        </el-form-item>\n        <el-form-item label=\"产品类型\" prop=\"productType\">\n          <el-select v-model=\"form.productType\" placeholder=\"请选择产品类型\">\n            <el-option label=\"请选择字典生成\" value=\"\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item label=\"配方稳定性报告状态\" prop=\"formulaStabilityReport\">\n          <el-input\n            v-model=\"form.formulaStabilityReport\"\n            placeholder=\"请输入配方稳定性报告状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配方稳定性报告风险内容\"\n          prop=\"formulaStabilityReportHoverTip\"\n        >\n          <el-input\n            v-model=\"form.formulaStabilityReportHoverTip\"\n            placeholder=\"请输入配方稳定性报告风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配制可行性评估状态\"\n          prop=\"formulaFeasibilityAssessment\"\n        >\n          <el-input\n            v-model=\"form.formulaFeasibilityAssessment\"\n            placeholder=\"请输入配制可行性评估状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"配制可行性评估风险内容\"\n          prop=\"formulaFeasibilityAssessmentHoverTip\"\n        >\n          <el-input\n            v-model=\"form.formulaFeasibilityAssessmentHoverTip\"\n            placeholder=\"请输入配制可行性评估风险内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"标准配制工艺单状态\" prop=\"standardFormulaProcess\">\n          <el-input\n            v-model=\"form.standardFormulaProcess\"\n            placeholder=\"请输入标准配制工艺单状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"生产模具治具确认状态\" prop=\"moldToolConfirmation\">\n          <el-input\n            v-model=\"form.moldToolConfirmation\"\n            placeholder=\"请输入生产模具治具确认状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"生产模具治具确认预计时间\"\n          prop=\"moldToolConfirmationHoverTip\"\n        >\n          <el-input\n            v-model=\"form.moldToolConfirmationHoverTip\"\n            placeholder=\"请输入生产模具治具确认预计时间\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"灌包可行性评估状态\"\n          prop=\"fillingPackagingFeasibility\"\n        >\n          <el-input\n            v-model=\"form.fillingPackagingFeasibility\"\n            placeholder=\"请输入灌包可行性评估状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"灌包可行性评估风险内容\"\n          prop=\"fillingPackagingFeasibilityHoverTip\"\n        >\n          <el-input\n            v-model=\"form.fillingPackagingFeasibilityHoverTip\"\n            placeholder=\"请输入灌包可行性评估风险内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"灌装/包装SOP状态\" prop=\"fillingPackagingSop\">\n          <el-input\n            v-model=\"form.fillingPackagingSop\"\n            placeholder=\"请输入灌装/包装SOP状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"成品检验标准状态\" prop=\"finishedProductStandard\">\n          <el-input\n            v-model=\"form.finishedProductStandard\"\n            placeholder=\"请输入成品检验标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"质量协议状态\" prop=\"qualityAgreement\">\n          <el-input\n            v-model=\"form.qualityAgreement\"\n            placeholder=\"请输入质量协议状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"质量协议合同类型是独立还是主框架合同\"\n          prop=\"qualityAgreementHoverTip\"\n        >\n          <el-input\n            v-model=\"form.qualityAgreementHoverTip\"\n            placeholder=\"请输入质量协议合同类型是独立还是主框架合同\"\n          />\n        </el-form-item>\n        <el-form-item label=\"包材标准状态\" prop=\"packagingMaterialStandard\">\n          <el-input\n            v-model=\"form.packagingMaterialStandard\"\n            placeholder=\"请输入包材标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"料体标样状态\" prop=\"liquidSample\">\n          <el-input\n            v-model=\"form.liquidSample\"\n            placeholder=\"请输入料体标样状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"包材标准样状态\" prop=\"packagingMaterialSample\">\n          <el-input\n            v-model=\"form.packagingMaterialSample\"\n            placeholder=\"请输入包材标准样状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"成品标样状态\" prop=\"finishedProductSample\">\n          <el-input\n            v-model=\"form.finishedProductSample\"\n            placeholder=\"请输入成品标样状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"过度包装确认状态\"\n          prop=\"excessivePackagingConfirmation\"\n        >\n          <el-input\n            v-model=\"form.excessivePackagingConfirmation\"\n            placeholder=\"请输入过度包装确认状态\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"过度包装风险内容\"\n          prop=\"excessivePackagingConfirmationHoverTip\"\n        >\n          <el-input\n            v-model=\"form.excessivePackagingConfirmationHoverTip\"\n            placeholder=\"请输入过度包装风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"注册备案是否完成状态\"\n          prop=\"registrationCompletion\"\n        >\n          <el-input\n            v-model=\"form.registrationCompletion\"\n            placeholder=\"请输入注册备案是否完成状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"备案号\" prop=\"registrationCompletionHoverTip\">\n          <el-input\n            v-model=\"form.registrationCompletionHoverTip\"\n            placeholder=\"请输入备案号\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"大货标准配方工艺单与注册/备案一致性状态\"\n          prop=\"formulaProcessConsistency\"\n        >\n          <el-input\n            v-model=\"form.formulaProcessConsistency\"\n            placeholder=\"请输入大货标准配方工艺单与注册/备案一致性状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"风险内容\" prop=\"formulaProcessConsistencyHoverTip\">\n          <el-input\n            v-model=\"form.formulaProcessConsistencyHoverTip\"\n            placeholder=\"请输入风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"大货文案与备案资料一致性状态\"\n          prop=\"documentationConsistency\"\n        >\n          <el-input\n            v-model=\"form.documentationConsistency\"\n            placeholder=\"请输入大货文案与备案资料一致性状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"风险内容\" prop=\"documentationConsistencyHoverTip\">\n          <el-input\n            v-model=\"form.documentationConsistencyHoverTip\"\n            placeholder=\"请输入风险内容\"\n          />\n        </el-form-item>\n        <el-form-item\n          label=\"产品内控标准符合备案执行标准状态\"\n          prop=\"internalStandardCompliance\"\n        >\n          <el-input\n            v-model=\"form.internalStandardCompliance\"\n            placeholder=\"请输入产品内控标准符合备案执行标准状态\"\n          />\n        </el-form-item>\n        <el-form-item label=\"删除标志\" prop=\"delFlag\">\n          <el-input v-model=\"form.delFlag\" placeholder=\"请输入删除标志\" />\n        </el-form-item>\n        <el-form-item label=\"备注\" prop=\"remark\">\n          <el-input\n            v-model=\"form.remark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"是否可生产\" prop=\"canProduce\">\n          <el-input v-model=\"form.canProduce\" placeholder=\"请输入是否可生产\" />\n        </el-form-item>\n        <el-form-item label=\"是否可生产备注\" prop=\"canProduceRemark\">\n          <el-input\n            v-model=\"form.canProduceRemark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n        <el-form-item label=\"是否可出库\" prop=\"canDeliver\">\n          <el-input v-model=\"form.canDeliver\" placeholder=\"请输入是否可出库\" />\n        </el-form-item>\n        <el-form-item label=\"是否可出库备注\" prop=\"canDeliverRemark\">\n          <el-input\n            v-model=\"form.canDeliverRemark\"\n            type=\"textarea\"\n            placeholder=\"请输入内容\"\n          />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  listAudit,\n  getAudit,\n  delAudit,\n  addAudit,\n  updateAudit,\n} from \"@/api/qc/audit\";\n\nexport default {\n  name: \"Audit\",\n  data() {\n    return {\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      // 非单个禁用\n      single: true,\n      // 非多个禁用\n      multiple: true,\n      // 显示搜索条件\n      showSearch: true,\n      // 总条数\n      total: 0,\n      // 产品准入检查表格数据\n      auditList: [],\n      // 弹出层标题\n      title: \"\",\n      // 是否显示弹出层\n      open: false,\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        productCode: null,\n        laboratoryCode: null,\n        manufacturer: null,\n        productName: null,\n        plannedProductionDate: null,\n        deliveryDate: null,\n        productType: null,\n        registrationCompletionHoverTip: null,\n        canProduce: null,\n        canDeliver: null,\n      },\n      // 表单参数\n      form: {},\n      // 表单校验\n      rules: {\n        productCode: [\n          { required: true, message: \"产品代码不能为空\", trigger: \"blur\" },\n        ],\n        productName: [\n          { required: true, message: \"产品名称不能为空\", trigger: \"blur\" },\n        ],\n      },\n    };\n  },\n  created() {\n    this.getList();\n  },\n  methods: {\n    /** 查询产品准入检查列表 */\n    getList() {\n      this.loading = true;\n      listAudit(this.queryParams).then((response) => {\n        this.auditList = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    // 表单重置\n    reset() {\n      this.form = {\n        id: null,\n        productId: null,\n        productCode: null,\n        laboratoryCode: null,\n        manufacturer: null,\n        productName: null,\n        spec: null,\n        plannedProductionDate: null,\n        orderQuantity: null,\n        deliveryDate: null,\n        productType: null,\n        formulaStabilityReport: null,\n        formulaStabilityReportHoverTip: null,\n        formulaFeasibilityAssessment: null,\n        formulaFeasibilityAssessmentHoverTip: null,\n        standardFormulaProcess: null,\n        moldToolConfirmation: null,\n        moldToolConfirmationHoverTip: null,\n        fillingPackagingFeasibility: null,\n        fillingPackagingFeasibilityHoverTip: null,\n        fillingPackagingSop: null,\n        finishedProductStandard: null,\n        qualityAgreement: null,\n        qualityAgreementHoverTip: null,\n        packagingMaterialStandard: null,\n        liquidSample: null,\n        packagingMaterialSample: null,\n        finishedProductSample: null,\n        excessivePackagingConfirmation: null,\n        excessivePackagingConfirmationHoverTip: null,\n        registrationCompletion: null,\n        registrationCompletionHoverTip: null,\n        formulaProcessConsistency: null,\n        formulaProcessConsistencyHoverTip: null,\n        documentationConsistency: null,\n        documentationConsistencyHoverTip: null,\n        internalStandardCompliance: null,\n        delFlag: null,\n        createBy: null,\n        createTime: null,\n        updateBy: null,\n        updateTime: null,\n        remark: null,\n        canProduce: null,\n        canProduceRemark: null,\n        canDeliver: null,\n        canDeliverRemark: null,\n      };\n      this.resetForm(\"form\");\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      this.ids = selection.map((item) => item.id);\n      this.single = selection.length !== 1;\n      this.multiple = !selection.length;\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加产品准入检查\";\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getAudit(id).then((response) => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改产品准入检查\";\n      });\n    },\n    /** 提交按钮 */\n    submitForm() {\n      this.$refs[\"form\"].validate((valid) => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateAudit(this.form).then((response) => {\n              this.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addAudit(this.form).then((response) => {\n              this.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$confirm('是否确认删除产品准入检查编号为\"' + ids + '\"的数据项？')\n        .then(function () {\n          return delAudit(ids);\n        })\n        .then(() => {\n          this.getList();\n          this.msgSuccess(\"删除成功\");\n        })\n        .catch(() => {});\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n      this.download(\n        \"qc/audit/export\",\n        {\n          ...this.queryParams,\n        },\n        `audit_${new Date().getTime()}.xlsx`\n      );\n    },\n  },\n};\n</script>\n\n<style scoped>\n/* 状态标签样式优化 */\n.el-tag {\n  cursor: pointer;\n}\n\n/* 悬浮提示样式 */\n.el-tooltip__popper {\n  max-width: 300px;\n}\n\n/* 表格列宽度优化 */\n.el-table .cell {\n  padding: 0 8px;\n}\n</style>\n"]}]}