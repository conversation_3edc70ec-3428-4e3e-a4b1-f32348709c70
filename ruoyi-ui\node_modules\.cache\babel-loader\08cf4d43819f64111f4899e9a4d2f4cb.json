{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue", "mtime": 1754031707465}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\babel.config.js", "mtime": 1743382537964}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_softwareDevelopingFormula", "require", "_project", "_softwareMaterial", "_validate", "_selectFormula", "_interopRequireDefault", "_bcpTemplate", "_jcxm", "_permission", "_save", "_jsBase", "_ruoyi", "_treeData", "name", "components", "selectFormula", "SoftwareMaterialSave", "props", "readonly", "type", "Boolean", "default", "data", "wxOpen", "activeName", "currentTab", "wxOptions", "conclusionOfSafetyAssessmentName", "loading", "visible", "btnLoading", "categoryOpen", "exportLoading", "fullscreenFlag", "shareOpen", "specOpen", "totalPercentVal", "sjTotalPercet", "isShowMaterialGoodsName", "isGenFormula", "isBMformula", "activeNames", "ids", "statusOptions", "shareDeptDatas", "shareDeptIds", "specId", "ownershopCompanyOptions", "bzxzOptions", "projectList", "typeOptions", "categoryList", "zxbzList", "xmIds", "recipeChangeHistoryData", "formulaTableDataList", "formulaTableDataListBack", "compositionTableDataList", "compositionTableDataListBack", "softwareFormulaSpecList", "specMaterialDatas", "itemArray", "userItemArray", "gtNumStr", "ltNumStr", "itemNames", "zxbzDetail", "gyjsData", "gyjsDataList", "zfylDataList", "gyjsBeianDataList", "zfylBeianDataList", "specObj", "single", "showSearch", "total", "isCopy", "softwareDevelopingFormulaList", "efficacyOptions", "otherSpecialClaimsOptions", "formulaMaterialDatas", "chooseFormulaMaterialDatas", "pFormulaMapData", "zybwOptions", "templateList", "cpjxOptions", "syrqOptions", "syffOptions", "wxTree", "purposeOptions", "categoryArray", "jcXmList", "stabilityDataList", "relationStabilityDataList", "jlOptions", "mjOptions", "plOptions", "checkRow", "id", "deptId", "categoryProps", "label", "value", "cirDataArray", "isLook", "cirDataProps", "duli<PERSON>ataA<PERSON>y", "cosmeticCaseFirstOptions", "cosmeticCaseSecondOptions", "duliDataProps", "formulaTabs", "title", "code", "open", "isEdit", "certificationOptions", "queryParams", "pageNum", "pageSize", "laboratoryCode", "formulaName", "ewgColor", "comclusionType", "form", "rules", "projectNo", "required", "message", "customerName", "productName", "cirText", "duliText", "categoryText", "pflx", "rflOptions", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "yplyOptions", "qiOptions", "qbmblOptions", "cosmeticClassificationOptions", "caseOptions", "useOptions", "specMaterialDatas1", "specMaterialDatas2", "ffjtxfxpgOptions", "wdxOptions", "ypFromOptions", "stabilityStatusOptions", "created", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "certificationRes", "wrap", "_callee$", "_context", "prev", "next", "toTree", "getDicts", "sent", "then", "response", "stop", "watch", "immediate", "handler", "params", "$route", "query", "Base64", "decode", "JSON", "parse", "reset", "init", "handleUpdate", "shareType", "queryProjectList", "handleAdd", "methods", "showWx", "_this2", "_callee2", "_callee2$", "_context2", "selectDictLabel", "designate<PERSON><PERSON><PERSON>", "row", "_callee3", "designatedUse", "materialId", "res", "_callee3$", "_context3", "queryFormulaAppointMaterialDataList", "materialCodes", "appointCode", "_this3", "_callee4", "_callee4$", "_context4", "formualList", "_this4", "_callee5", "categorySet", "_iterator", "_step", "item", "_iterator2", "_step2", "_loop", "purposeRes", "dictObj", "categoryAllArray", "datas", "cirDataAllArray", "duliDataAllArray", "_callee5$", "_context6", "check<PERSON><PERSON><PERSON>", "Set", "allJcxm", "_createForOfIteratorHelper2", "s", "n", "done", "add", "category", "err", "e", "f", "_loop$", "_context5", "push", "array", "filter", "i", "<PERSON><PERSON><PERSON>", "t1", "finish", "allBcpTemplate", "querySoftwareDevelopingFormulaDict", "GXXC_DATA_LIST", "GXXC_DATA_LIST_OTHER", "ZYBW_DATA_LIST", "CPJX_DATA_LIST", "SYRQ_DATA_LIST", "PFLX_DATA_LIST", "queryFormulaClassifyData", "includes", "categoryId", "getFormulaCategoryTree", "queryFormulaZxbzDataList", "queryCirHistoryData", "getCirDataTree", "queryDuliHistoryData", "getDuliDataTree", "itemNameChange", "itemName", "_this5", "_callee6", "arr", "itemNameText", "_callee6$", "_context7", "text", "projectChange", "_this6", "_callee7", "projectDetail", "_callee7$", "_context8", "formualProjectDetail", "indexOf", "brandName", "seriesName", "zxbzChange", "_this7", "_callee8", "_callee8$", "_context9", "queryFormulaZxbzDataDetail", "execNumber", "zxbzh", "getList", "_this8", "_callee9", "_callee9$", "_context10", "Object", "assign", "listSoftwareDevelopingFormula", "rows", "cancel", "_this9", "_callee10", "_callee10$", "_context11", "currentTemplateId", "relationMaterialCode", "englishName", "materialCode", "formulaCodeParams", "price", "weight", "isLock", "isMateral", "status", "remark", "operator", "createdTime", "isDel", "lastModifiedTime", "note", "formulaCode", "duliId", "cirId", "brandId", "customerCode", "appearance", "colour", "ph", "viscosity", "stabilityresult", "gxgs", "standard", "introFile", "organizationId", "oldFormulaCode", "copyFormulaId", "addTips", "wendingxingFile", "gongyiFile", "xiangrongxingFile", "weishenwuFile", "xiaofeizheFile", "qitaFile", "isDraft", "gxxc", "gxxcOther", "zybw", "syrq", "cpjx", "cpfldm", "cosmeticClassification", "cosmeticCase", "execNumberId", "aqpgjl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "cosmeticCaseFirst", "cosmeticCaseSecond", "qubanmeibailei", "<PERSON><PERSON><PERSON><PERSON>", "sfa", "pa", "yush<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ftlTime", "filCode", "baCode", "baTime", "filCodeNote", "baCodeNote", "waxcName", "waxcOthername", "wax<PERSON><PERSON><PERSON><PERSON>", "baStatus", "formulaPid", "bpNote", "zsTime", "zsCode", "gongyijianshuZs", "yfFile", "zsFile", "isLove", "upRate", "oriPrice", "levelNum", "purpose", "formulaStatus", "formulaRemark", "formulaImage", "formulaConstructionIdeas", "isRealse", "materialStatusInfo", "importCountryInfo", "operatorName", "stabilityStatus", "isResult", "isGt", "weishenwuResult", "weishenwuRemark", "xiangrongxingResult", "xiangrongxingRemark", "wendingxingResult", "wendingxingRemark", "materialCycle", "resetForm", "length", "allTreeData", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "handleFormulaMaterialSelectionChange", "genPformulaInfo", "_this10", "_callee11", "_iterator3", "_step3", "_callee11$", "_context12", "msgError", "abrupt", "t0", "generatePFormulaInfo", "stringify", "msgSuccess", "limitDecimal", "inputValue", "percentage", "regex", "test", "lastValue", "generBMaterialInfo", "_this11", "_callee13", "_callee13$", "_context14", "$confirm", "confirmButtonText", "cancelButtonText", "_callee12", "_callee12$", "_context13", "generateBMaterialInfo", "catch", "genNewformulaInfo", "_this12", "_callee14", "_callee14$", "_context15", "generateNewformulaInfo", "_this13", "_callee16", "_callee16$", "_context17", "queryLookFormulaTabs", "getSoftwareDevelopingFormulaDetail", "_ref2", "_callee15", "formulaObj", "categoryTextList", "categoryIds", "_iterator4", "_step4", "t", "cirTextList", "_iterator5", "_step5", "_t", "duliTextList", "_iterator6", "_step6", "_t2", "jcxm<PERSON>son", "gyjs", "zfyl", "_gyjsData", "_gyjs", "_zfyl", "_callee15$", "_context16", "dataList", "allList", "specMaterialData", "split", "parseInt", "itemArr", "refreshFormulaLegalGy", "_x", "apply", "arguments", "queryFormualMaterialRecipeChangeHistoryData", "queryMaterialFormulaSpecDataList", "queryFormulaStabilityRecordDataList", "stabilityStatusFormat", "ypFormat", "ypFrom", "_this14", "_callee17", "_callee17$", "_context18", "_this15", "_callee18", "formulaStabilityObj", "_callee18$", "_context19", "copyGongyi", "_this16", "_callee19", "_callee19$", "_context20", "submitUploadForm", "_this17", "_callee20", "_callee20$", "_context21", "updateSoftwareDevelopingFormulaImg", "submitUploadFileForm", "_this18", "_callee21", "param", "_callee21$", "_context22", "updateSoftwareDevelopingFormulaFileImg", "submitForm", "_this19", "_callee22", "_iterator7", "_step7", "isRelation", "isFx", "relationCode", "msg", "returnObj", "num", "repeatCode", "_callee22$", "_context23", "$refs", "validate", "isArray", "join", "isRepeat", "updateSoftwareDevelopingFormula", "currentVersion", "parseFloat", "addSoftwareDevelopingFormula", "close", "t2", "_this20", "$store", "dispatch", "view", "fullPath", "path", "$nextTick", "$router", "replace", "repeatCodesSet", "codes", "_iterator8", "_step8", "_i", "_codes", "index", "_iterator9", "_step9", "size", "str", "Array", "from", "handleDelete", "_this21", "delSoftwareDevelopingFormula", "handleExport", "_this22", "exportSoftwareDevelopingFormula", "download", "queryMaterialCode", "_this23", "_callee23", "tipsInfo", "_callee23$", "_context24", "getRawMaterialInfoByCode", "msgInfo", "unshift", "codeChange", "queryFormulaCode", "_this24", "_callee24", "_callee24$", "_context25", "getFormulaInfoByCode", "confirmSelectGoods", "_this25", "_callee25", "_callee25$", "_context26", "getFormulaLabNoInfoByCode", "submitSpec", "_this26", "_callee26", "_callee26$", "_context27", "formulaId", "addSoftwareDevelopingFormulaSpecZxbz", "submitUserSpec", "_this27", "_callee27", "_callee27$", "_context28", "addSoftwareDevelopingUserFormulaSpecZxbz", "delFormulaMaterial", "_this28", "_callee28", "_callee28$", "_context29", "x", "key", "categoryChange", "categoryChangeNew", "_this29", "_callee29", "gxxc1", "gxxc2", "zybw1", "cpjx2", "syrq1", "syrq2", "isProcess", "_formulaMaterialDatas", "<PERSON><PERSON><PERSON><PERSON>", "isSecond", "_iterator10", "_step10", "isNewMaterial", "inicNmjyl", "res1", "_iterator11", "_step11", "_item2", "_isNewMaterial", "_inicNmjyl", "symdInfo", "_callee29$", "_context30", "sort", "n1", "n2", "arrayContainsAnother", "arr1", "arr2", "some", "to<PERSON><PERSON>ose", "selected", "_this30", "_callee30", "_callee30$", "_context31", "tips", "handleFormulaSpecAdd", "handleFormulaSpecEdit", "_this31", "_callee31", "dataObj", "_callee31$", "_context32", "queryMaterialFormulaSpecDataDetail", "_this32", "_callee32", "_callee32$", "_context33", "queryFormulaLegalGy", "submitTipsMaterialFormulaInfo", "_this33", "_callee33", "_callee33$", "_context34", "addFormulaSpecMaterialData", "submitSymdInfo", "_this34", "_callee34", "formulaSymd", "_iterator12", "_step12", "_callee34$", "_context35", "chi<PERSON>ame", "cppfSymd", "addFormulaSymdForm", "getSummaries", "_this35", "columns", "sums", "for<PERSON>ach", "column", "values", "Number", "property", "every", "isNaN", "reduce", "curr", "keepDigits", "getSummariesPFormula", "_this36", "formulaMaterialBack", "o", "background", "compositionTableStyle", "percert", "isColor", "color", "compositionCellTableStyle", "_ref3", "isTips", "materialDetails", "_this37", "_callee35", "dataRow", "_callee35$", "_context36", "softwareMaterialSave", "selectable", "isUse", "addFormulaGyjsBeianInfo", "_this38", "_callee36", "_callee36$", "_context37", "changeFun", "_this39", "_callee37", "_callee37$", "_context38", "changeTemplate", "_this40", "_callee38", "_iterator13", "_step13", "_callee38$", "_context39", "getBcpTemplate", "standardVal", "delItem", "splice", "selectProject", "selectCategory", "_iterator14", "_step14", "selectXm", "confirmXm", "_this41", "_iterator15", "_step15", "a", "serialNo", "seq", "frequency", "yqIds", "methodArray", "dataArray", "methodDesc", "methodTemplate", "b", "handleShare", "_this42", "_callee39", "_callee39$", "_context40", "queryFormulaShareDeptDataList", "queryFormulaShareDeptDataDetail", "submitShareFormulaInfo", "_this43", "_callee40", "_callee40$", "_context41", "addFormulaShareDataInfo", "handleCompositionQuery", "componentType", "resetCom<PERSON><PERSON><PERSON><PERSON>", "handleMaterialQuery", "resetMaterialQuery", "isEditStandard", "isOpr"], "sources": ["src/views/software/softwareDevelopingFormula/saveOrUpdate.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 添加或修改研发配方对话框 -->\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n        <el-tab-pane v-for=\"(formula,index) in formulaTabs\" :key=\"index\" :label=\"formula.title\"\r\n                     :name=\"formula.code\">\r\n          <template v-if=\"formula.code==='base'\">\r\n            <fieldset>\r\n              <legend>项目信息</legend>\r\n              <el-row v-if=\"form.id\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    {{form.projectNo}}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row v-else>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    <el-select style=\"width: 500px\" clearable filterable v-model=\"form.projectNo\" @change=\"projectChange\">\r\n                      <el-option\r\n                        v-for=\"item in projectList\"\r\n                        :key=\"item.projectNo\"\r\n                        :label=\"item.projectNo+'('+item.productName+'|'+item.customerName+')'\"\r\n                        :value=\"item.projectNo\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户名称\" prop=\"customerName\">\r\n                    <el-input disabled=\"true\" size=\"small\" v-model=\"form.customerName\" placeholder=\"请输入客户名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品名称\" prop=\"productName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"品牌名称\" prop=\"brandName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.brandName\" placeholder=\"请输入品牌名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"系列名称\" prop=\"seriesName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.seriesName\" placeholder=\"请输入系列名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"子名称\" prop=\"itemName\">\r\n                    <el-select clearable filterable v-model=\"form.itemName\" @change=\"itemNameChange\">\r\n                      <el-option\r\n                        v-for=\"item in itemNames\"\r\n                        :key=\"item.id\"\r\n                        :label=\"item.text\"\r\n                        :value=\"item.id\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"用途\" prop=\"purpose\">\r\n                    <el-select clearable v-model=\"form.purpose\" placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in purposeOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :disabled=\"item.isShow===0\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n<!--              <el-row>-->\r\n<!--                <el-col :span=\"8\">-->\r\n<!--                  <el-form-item label=\"状态\" prop=\"formulaStatus\">-->\r\n<!--                    <el-radio-group v-model=\"form.formulaStatus\">-->\r\n<!--                      <el-radio :label=\"0\">正常</el-radio>-->\r\n<!--                      <el-radio :label=\"1\">停用</el-radio>-->\r\n<!--                     </el-radio-group>-->\r\n<!--                  </el-form-item>-->\r\n<!--                </el-col>-->\r\n<!--              </el-row>-->\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>编码信息</legend>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方编码\" prop=\"formulaCode\">\r\n                    {{form.formulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品分类代码\" prop=\"cpfldm\">\r\n                    {{form.cpfldm}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"化妆品分类\" prop=\"cosmeticClassification\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156604264.png\" style=\"height: 500px\" >\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156703377.png\" style=\"height: 500px\" >\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n                      化妆品分类\r\n                    </div>\r\n                    <el-select clearable v-model=\"form.cosmeticClassification\">\r\n                      <el-option\r\n                        v-for=\"item in cosmeticClassificationOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='1'\" label=\"情形\" prop=\"cosmeticCaseFirst\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseFirst\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseFirstOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2'\" label=\"情形\" prop=\"cosmeticCase\">\r\n                    <el-select clearable v-model=\"form.cosmeticCase\">\r\n                      <el-option\r\n                        v-for=\"item in caseOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2' && form.cosmeticCase==='1'\" label=\"情形\" prop=\"cosmeticCaseSecond\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseSecond\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseSecondOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n                    <el-input v-model=\"form.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"form.oldFormulaCode\" :span=\"8\">\r\n                  <el-form-item label=\"复制的配方编码\" prop=\"oldFormulaCode\">\r\n                      {{form.oldFormulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方类别\" prop=\"categoryText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.categoryText\"\r\n                      :options=\"categoryArray\"\r\n                      :props=\"categoryProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"CIR历史用量\" prop=\"cirText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.cirText\"\r\n                      :options=\"cirDataArray\"\r\n                      :props=\"cirDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"毒理使用量参考\" prop=\"duliText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.duliText\"\r\n                      :options=\"duliDataArray\"\r\n                      :props=\"duliDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>备案相关</legend>\r\n              <el-divider content-position=\"left\">功效宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxc\" style=\"width: 1000px\"  @change=\"codeChange(1)\">\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 0)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 1)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 3)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">其他特别宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxcOther\">\r\n                <el-checkbox\r\n                  v-for=\"dict in otherSpecialClaimsOptions\"\r\n                  :key=\"dict.id\"\r\n                  :label=\"dict.id\">\r\n                  {{dict.id}}.{{ dict.title }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">申报类别(特殊化妆品填报)</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"染发类\">\r\n                    <el-checkbox-group @change=\"categoryChange\"  v-model=\"form.ranfalei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in rflOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"祛斑美白类\">\r\n                    <el-checkbox-group\r\n                      @change=\"categoryChange\" v-model=\"form.qubanmeibailei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in qbmblOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"\" prop=\"fangshailei\">\r\n                    <el-checkbox  @change=\"categoryChange\" v-model=\"form.fangshailei\">防晒类</el-checkbox>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"SPF值\" prop=\"sfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.sfa\" placeholder=\"SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"PA值\" prop=\"pa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.pa\" placeholder=\"PA值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"浴后SPF值\" prop=\"yushousfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.yushousfa\" placeholder=\"浴后SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"xgx\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          1.特定宣称：宣传试用敏感皮肤，无泪配方<br/>\r\n                          2.特定宣称：原料功效<br/>\r\n                          3.宣称温和：无刺激<br/>\r\n                          4.宣称量化指标（时间、统计数据等）<br/>\r\n                          5.孕妇和哺乳期妇女适用\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n\r\n                      <el-checkbox  @change=\"categoryChange\" v-model=\"form.xingongxiao\">新功效</el-checkbox>\r\n                    </div>\r\n                    <el-input @input=\"categoryChange('1')\" v-model=\"form.xingongxiaocontent\" autosize type=\"textarea\" placeholder=\"请输入新功效\" style=\"width: 800px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">作用部位</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.zybw\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in zybwOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">产品剂型</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.cpjx\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in cpjxOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">适用人群</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.syrq\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syrqOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">使用方法</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.pflx\"  @change=\"codeChange(2)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syffOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <br />\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"制造量\" prop=\"weight\">\r\n                    <el-input v-model=\"form.weight\" placeholder=\"请输入制造量\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"稳定性结果\" prop=\"stabilityresult\">\r\n                    <el-input autosize type=\"textarea\" v-model=\"form.stabilityresult\" placeholder=\"请输入稳定性结果\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"功效概述\" prop=\"gxgs\">\r\n                    <el-input :autosize=\"{ minRows: 3, maxRows: 20}\" type=\"textarea\" v-model=\"form.gxgs\" placeholder=\"请输入功效概述\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider  content-position=\"left\">稳定性测试记录</el-divider>\r\n              <el-table :data=\"stabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\"  :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-divider content-position=\"left\">关联稳定性测试记录</el-divider>\r\n              <el-table :data=\"relationStabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\" :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注\" prop=\"formulaRemark\">\r\n                    <el-input  autosize v-model=\"form.formulaRemark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaMaterial'\">\r\n            <el-row v-if=\"!form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"是否复制配方\">\r\n                  <el-radio-group v-model=\"isCopy\">\r\n                    <el-radio :label=\"0\">否</el-radio>\r\n                    <el-radio :label=\"1\">是</el-radio>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===0\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:300px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择配方\">\r\n                  <el-input v-model=\"form.formulaCodeParams\"  @keyup.enter.native=\"queryFormulaCode\" style=\"width:300px\"  placeholder=\"如果需要添加配方,请输入配方编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryFormulaCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-button v-if=\"isGenFormula===1\" type=\"primary\" @click=\"genPformulaInfo\">生成P配方</el-button>\r\n              </el-col>\r\n              <el-col :span=\"12\" v-if=\"form.pFormulaCount>0\">\r\n                <el-button v-if=\"isBMformula===1\" type=\"primary\" @click=\"genNewformulaInfo\">生成含B代码配方</el-button>\r\n                <div v-if=\"form.formulaCodeBuff\">已生成含B代码配方:<span v-html=\"form.formulaCodeBuff\"></span></div>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===1\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"请输入需要复制的实验室编码\">\r\n                  <el-input v-model=\"form.formulaCodeParams\" @focus=\"toChoose\" style=\"width:350px\"  placeholder=\"请选择要复制的实验室编码\" >\r\n                    <el-button\r\n                      slot=\"append\"\r\n                      class=\"el-icon-zoom-in\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"toChoose\" />\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:350px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-table :data=\"formulaMaterialDatas\" :row-style=\"formulaMaterialBack\" show-summary :summary-method=\"getSummaries\" @selection-change=\"handleFormulaMaterialSelectionChange\">\r\n                  <el-table-column v-if=\"form.id\" align=\"center\" type=\"selection\" width=\"50\" :selectable=\"selectable\"></el-table-column>\r\n                  <el-table-column align=\"center\" width=\"60\">\r\n                    <template slot-scope=\"scope\">\r\n                      <i v-if=\"(form.isLock===1) && isLook\" class=\"el-icon-remove-outline\" @click=\"delFormulaMaterial(scope.row)\" ></i>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料代码\" align=\"center\" prop=\"materialCode\" width=\"80\">\r\n                    <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.type==0\" @click=\"materialDetails(scope.row)\" style=\"color: #00afff;cursor: pointer\">{{scope.row.materialCode}}</span>\r\n                       <span v-else >{{scope.row.materialCode}}</span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"推荐原料\" align=\"center\" prop=\"relationCode\" width=\"140\"  />\r\n                  <el-table-column label=\"商品名称\" v-if=\"isShowMaterialGoodsName===1\" align=\"center\" prop=\"materialGoodsName\" width=\"280\"  />\r\n                  <el-table-column label=\"比例(%)\" width=\"140\" align=\"center\" prop=\"percentage\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"number\" v-model=\"scope.row.percentage\" @input=\"limitDecimal(scope.row)\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"分相\" width=\"100\" align=\"center\"prop=\"subItem\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input v-model=\"scope.row.subItem\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料用途\" width=\"120\" align=\"center\" prop=\"designatedUse\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select @change=\"designateChange(scope.row)\" v-model=\"scope.row.designatedUse\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in useOptions\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.value\"\r\n                          :value=\"item.value\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"指定代码\" width=\"150\" align=\"center\" prop=\"appointCode\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select clearable v-model=\"scope.row.appointCode\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in scope.row.materialCodes\"\r\n                          :key=\"item.materialSubCode\"\r\n                          :label=\"item.materialSubCode\"\r\n                          :value=\"item.materialSubCode\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"使用目的\" width=\"200\" align=\"center\" prop=\"symdInfo\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input style=\"width: 190px\" v-model=\"scope.row.symdInfo\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"周期(天)\" align=\"center\" prop=\"orderingCycle\" />\r\n                  <el-table-column label=\"原料认证\" align=\"center\" prop=\"certification\">\r\n                      <template slot-scope=\"scopoe\">\r\n                        {{selectDictLabel(certificationOptions,scopoe.row.certification)}}\r\n                      </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"进口国家\" align=\"center\" prop=\"importCountry\" />\r\n                  <el-table-column label=\"备注\" align=\"center\" width=\"300\" prop=\"remark\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isRelation==1\"><el-input v-model=\"scope.row.remark\"/></span>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-col>\r\n            </el-row>\r\n            <br />\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"配方图片\">\r\n                  <imageUpload v-model=\"form.formulaImage\" :limit=\"3\"></imageUpload>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"配方搭建思路\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.formulaConstructionIdeas\" placeholder=\"请输入配方搭建思路\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                 <div style=\"text-align: center\">\r\n                   <el-button type=\"primary\" @click=\"submitUploadForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                 </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaFile'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"检测标准\" prop=\"introFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.introFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试结果\" prop=\"wendingxingResult\">\r\n                  <el-select v-model=\"form.wendingxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试备注\" prop=\"wendingxingRemark\">\r\n                   <el-input type=\"textarea\" v-model=\"form.wendingxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"稳定性检测报告\" prop=\"wendingxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.wendingxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"工艺\" prop=\"gongyiFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.gongyiFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性结果\" prop=\"xiangrongxingResult\">\r\n                  <el-select v-model=\"form.xiangrongxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性备注\" prop=\"xiangrongxingRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.xiangrongxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"相容性测试报告\" prop=\"xiangrongxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiangrongxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战结果\" prop=\"weishenwuResult\">\r\n                  <div slot=\"label\">\r\n                    <el-tooltip>\r\n                      <i class=\"el-icon-question\" ></i>\r\n                      <div slot=\"content\">\r\n                        <p>高风险(没有测试过相关防腐体系)</p>\r\n                        <p>中风险(测试进行中,有一定数据量)</p>\r\n                        <p>低风险(有相似配方的测试数据,且测试通过)</p>\r\n                        <p>无风险(测试通过)</p>\r\n                        <p>测试没通过(不能释放)</p>\r\n                      </div>\r\n                    </el-tooltip>\r\n                    防腐挑战结果\r\n                  </div>\r\n                  <el-select v-model=\"form.weishenwuResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in ffjtxfxpgOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战备注\" prop=\"weishenwuRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.weishenwuRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"防腐实验报告\" prop=\"weishenwuFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.weishenwuFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"消费者测试报告\" prop=\"xiaofeizheFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiaofeizheFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"其它\" prop=\"qitaFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.qitaFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                <div style=\"text-align: center\">\r\n                  <el-button type=\"primary\" @click=\"submitUploadFileForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='recipeChangeHistory'\">\r\n            <el-table :data=\"recipeChangeHistoryData\">\r\n              <el-table-column align=\"center\" prop=\"modifiedTime\" label=\"更改日期\" />\r\n              <el-table-column align=\"center\" prop=\"materialCode\" label=\"原料代码\" />\r\n              <el-table-column align=\"center\" prop=\"percentageOld\" label=\"原始比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"percentageNew\" label=\"新比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"remark\" label=\"更改内容\" />\r\n              <el-table-column align=\"center\" prop=\"inciName\" label=\"INCI中文名\" />\r\n              <el-table-column align=\"center\" prop=\"operator\" label=\"编辑人\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='spec'\">\r\n            <el-divider content-position=\"left\">检测标准</el-divider>\r\n            <table class=\"base-table\"   style=\"margin-top: 0 !important;\">\r\n              <tr>\r\n                <td style=\"width:70px\" rowspan=\"5\">执行标准</td>\r\n              </tr>\r\n              <tr>\r\n                <td>标准名称</td>\r\n                <td>\r\n                  <el-select clearable filterable v-model=\"form.execNumberId\" @change=\"zxbzChange\">\r\n                    <el-option\r\n                      v-for=\"item in zxbzList\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.zxbzh+'('+item.bzmc+')'\"\r\n                      :value=\"item.id\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </td>\r\n                <td>执行标准号</td>\r\n                <td id=\"zxbzhTd\">{{zxbzDetail.zxbzh}}({{zxbzDetail.bzmc}})</td>\r\n                <td>定义</td>\r\n                <td id=\"dingyiTd\">{{zxbzDetail.dingyi}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>外观及pH</td>\r\n                <td  id=\"waiguanjiphTd\">{{zxbzDetail.waiguan}}{{zxbzDetail.ph}}</td>\r\n                <td >耐热</td>\r\n                <td  id=\"naireTd\">{{zxbzDetail.naire}}</td>\r\n                <td >耐寒</td>\r\n                <td  id=\"naihanTd\">{{zxbzDetail.naihan}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>归属公司</td>\r\n                <td  id=\"gsgsTd\">\r\n                  {{selectDictLabel(ownershopCompanyOptions,zxbzDetail.ownershopCompany)}}\r\n                </td>\r\n                <td >状态</td>\r\n                <td  id=\"zhtTd\">\r\n                  {{selectDictLabel(statusOptions,zxbzDetail.status)}}\r\n                </td>\r\n                <td >标准性质</td>\r\n                <td  id=\"bzxzTd\">\r\n                  {{selectDictLabel(bzxzOptions,zxbzDetail.bzxz)}}\r\n                </td>\r\n              </tr>\r\n              <!--                <tr>-->\r\n              <!--                  <td colspan=\"7\">-->\r\n              <!--                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>-->\r\n              <!--                  </td>-->\r\n              <!--                </tr>-->\r\n            </table>\r\n            <el-divider content-position=\"left\">检测项目</el-divider>\r\n            <el-tabs v-model=\"currentTab\" >\r\n              <el-tab-pane key=\"base\" label=\"常规检测\" name=\"base\" >\r\n                <div class=\"cell-wrapper\" >\r\n                  <div class=\"label\">选择模板:</div>\r\n                  <div class=\"content\">\r\n                    <el-select @change=\"changeFun\" v-model=\"form.currentTemplateId\" filterable size=\"mini\" >\r\n                      <el-option\r\n                        v-for=\"item in templateList\"\r\n                        :key=\"item.id\"\r\n                        :value=\"item.id\"\r\n                        :label=\"item.name\" />\r\n                    </el-select>\r\n                    <el-button icon=\"el-icon-search\" size=\"mini\" @click=\"changeTemplate(1)\" :loading=\"btnLoading\" />\r\n                  </div>\r\n                </div>\r\n                <div class=\"table-wrapper\" style=\"text-align: center;\" v-if=\"itemArray.length\">\r\n                  <table class=\"base-table small-table\">\r\n                    <tr>\r\n                      <th style=\"width: 50px\">\r\n                        <i @click=\"selectProject\" class=\"el-icon-circle-plus\" />\r\n                      </th>\r\n                      <th style=\"width: 120px\">类型</th>\r\n                      <th style=\"width: 120px\">检测项目</th>\r\n                      <th style=\"width: 320px\">检验标准</th>\r\n                      <th style=\"width: 320px\">检验方法</th>\r\n                      <th style=\"width: 320px\">标准值</th>\r\n                      <th style=\"width: 120px\">检验频次</th>\r\n                    </tr>\r\n                    <tr v-for=\"(item,i) in itemArray\" :key=\"item.id\" >\r\n                      <td><i v-if=\"(form.isLock===1 && isLook)\" class=\"el-icon-remove-outline\" @click=\"delItem(i)\"/></td>\r\n                      <td>{{item.type}}</td>\r\n                      <td>{{item.label}}</td>\r\n                      <td>{{item.standard}}</td>\r\n                      <td>{{item.methodTemplate}}</td>\r\n                      <td>\r\n                        <span v-if=\"isEditStandard(item.id)\"><el-input v-model=\"item.standardVal\" /> </span>\r\n                        <span v-else>\r\n                      <span v-if=\"(form.isLock===1 && isLook)\"><el-input v-model=\"item.standardVal\" /></span>\r\n                      <span v-else>{{item.standardVal}}</span>\r\n                    </span>\r\n                      </td>\r\n                      <td>{{item.frequency}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <br /><br />\r\n                  <div style=\"text-align: center;\">\r\n                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>\r\n                  </div>\r\n                </div>\r\n              </el-tab-pane>\r\n              <el-tab-pane key=\"special\" label=\"微生物检测\" name=\"microbe\" >\r\n                <el-row>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"样品物性\" prop=\"wxId\">\r\n                      <div style=\"cursor: pointer\" @click=\"showWx\" >\r\n                        <span v-if=\"form.wxId\" >{{wxLabel(form.wxId)}}</span>\r\n                        <i v-else style=\"color: #00afff;\">请选择</i>\r\n                      </div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"检验依据\" prop=\"inspectBasis\">\r\n                      <el-input v-model=\"form.inspectBasis\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-tab-pane>\r\n\r\n              <el-form-item label=\"备注\" style=\"margin-top: 20px\" prop=\"microbeRemark\">\r\n                <el-input v-model=\"form.microbeRemark\" type=\"textarea\" autosize />\r\n              </el-form-item>\r\n\r\n\r\n            </el-tabs>\r\n\r\n            <br /><br />\r\n            <el-divider content-position=\"left\">检测记录</el-divider>\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  plain\r\n                  icon=\"el-icon-plus\"\r\n                  size=\"mini\"\r\n                  v-if=\"form.isLock===1 && isLook\"\r\n                  @click=\"handleFormulaSpecAdd\"\r\n                >新增</el-button>\r\n              </el-col>\r\n            </el-row>\r\n            <el-table v-loading=\"loading\" :data=\"softwareFormulaSpecList\" style=\"overflow: scroll\">\r\n              <el-table-column label=\"样品来源\" align=\"center\" prop=\"type\">\r\n                <template slot-scope=\"scope\">\r\n                  {{selectDictLabel(yplyOptions,scope.row.type)}}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"测试样批号\" width=\"130\" align=\"center\" prop=\"ceshiyangpihao\" />\r\n              <el-table-column label=\"外观\" align=\"center\" prop=\"waiguan\" />\r\n              <el-table-column label=\"颜色\" align=\"center\" prop=\"yanse\" />\r\n              <el-table-column label=\"气味\" align=\"center\" prop=\"qiwei\" />\r\n              <el-table-column label=\"PH\" align=\"center\" prop=\"ph\" />\r\n              <el-table-column label=\"耐热\" align=\"center\" prop=\"naire\" />\r\n              <el-table-column label=\"耐寒\" align=\"center\" prop=\"naihan\" />\r\n              <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdTime\" width=\"180\" />\r\n              <el-table-column label=\"操作人\" align=\"center\" prop=\"operator\" />\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template v-slot=\"scope\">\r\n                  <el-tooltip content=\"修改\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      @click=\"handleFormulaSpecEdit(scope.row)\"\r\n                    />\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='workmanship'\">\r\n            <div style=\"width: 100%\">\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>工艺简述</span>\r\n                  <el-tooltip content=\"更新工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"refreshFormulaLegalGy('1')\"\r\n                    />\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(0)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>备案工艺</span>\r\n                  <el-tooltip content=\"复制工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"copyGongyi()\"\r\n                    >\r\n                      复制工艺简述\r\n                    </el-button>\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(1)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"formula.code==='compositionTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\" :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"INCI 中文名\" prop=\"chiName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                    <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">\r\n                       <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                          <div slot=\"content\">\r\n                            <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                          </div>\r\n                          <i class=\"el-icon-question\" ></i>\r\n                        </el-tooltip>\r\n                       <span v-html=\"scope.row.chiNameNew\"></span>\r\n                    </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" prop=\"engName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">{{scope.row.engName}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\" prop=\"percert\" align=\"center\"  width=\"150\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{totalPercent}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?'color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"国家简化版安评要求\" align=\"center\">\r\n                <el-table-column label=\"驻留类产品最高历史使用量\" prop=\"zllzglssyl\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"淋洗类产品最高历史使用量\" prop=\"lxlzglssyl\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"法规要求\" align=\"center\">\r\n                <el-table-column label=\"备案(明细)\" prop=\"bzmx\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"化妆品使用时的最大允许浓度\" prop=\"maxAllowConcentration\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"适用及(或)使用范围\" prop=\"scopeOfApplication\" align=\"center\"  width=\"220\"  />\r\n                <el-table-column label=\"其他限制和要求\" prop=\"otherLimit\" align=\"center\"  width=\"220\" />\r\n                <el-table-column label=\"标签上必须标印的 使用条件和注意事项\" prop=\"labelCondition\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n            </el-table>\r\n            <br />\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以上：</td>\r\n                <td align=\"left\">\r\n                   <span v-html=\"gtNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以下：</td>\r\n                <td align=\"left\">\r\n                  <span v-html=\"ltNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n            </table>\r\n          </template>\r\n          <template v-if=\"formula.code==='productSafetyAssessmentReport'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"产品评估结论\" prop=\"aqpgjl\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.aqpgjl\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n            <br />\r\n            <el-table height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n              <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                         <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI名称/英文民称\" prop=\"engName\" align=\"center\" fixed  width=\"200\" />\r\n              <el-table-column label=\"含量(%)\" prop=\"percert\" align=\"center\"  width=\"120\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?';color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"在《已使用原料目录》中的序号\" prop=\"cppfSyylxh\" align=\"center\"  width=\"220\" />\r\n              <el-table-column label=\"产品配方表备注\" prop=\"cppfRemark\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"《化妆品安全技术规范》要求\" prop=\"gcfJsgf\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"权威机构评估结论\" prop=\"gcfQwjgpgjl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"本企业原料历史使用量(%)\" prop=\"gcfBqyysyl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"评估结论\" prop=\"gcfPgjl\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isColor==1?'color:red':''\">{{scope.row.gcfPgjl}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"参考文献序号\" prop=\"gcfCkwx\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献内容\" prop=\"gcfCkwxnr\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献下载链接\" prop=\"gcfCkwxlj\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"可能含有的风险物质\" prop=\"aqxKnhyfxw\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"风险物质备注\" prop=\"aqxRemark\" align=\"center\"  width=\"200\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessment'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                   <span v-if=\"form.isCiji>0\" style=\"color:red;\">配方需测刺激性</span>\r\n                   <span v-if=\"form.isZhimin>0\" style=\"color:red;\"><span v-if=\"form.isCiji>0\">、</span><span v-else>配方需测</span>致敏性</span>\r\n                   <span v-if=\"form.zmCjTips\" style=\"color:red;\">。({{form.zmCjTips}})</span>\r\n                </el-row>\r\n                <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n                  <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">更新使用目的</el-button>\r\n                </div>\r\n                <br />\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"   :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"dataObj.zl\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"dataObj.newTotal\" align=\"center\" />\r\n                    <el-table-column label=\"适用及(或)使用范围\" :show-overflow-tooltip=\"true\"  width=\"220\" prop=\"dataObj.newRange\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"化妆品安全卫生规范\" align=\"center\">\r\n                    <el-table-column label=\"符合卫生规范\" width=\"120\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.dataObj.bzmx  === '否'\"></span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"备案明细\" width=\"220\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.dataObj.bzmx  === '是'\">\r\n                         {{scope.row.dataObj.bzmxDetail}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dataObj.syjsyfw\"  width=\"260\" :show-overflow-tooltip=\"true\"  label=\"适用及(或)使用范围\" align=\"center\" />\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\"  prop=\"dataObj.zdsynd\" align=\"center\" />\r\n                    <el-table-column prop=\"otherLimit\"  width=\"260\"  :show-overflow-tooltip=\"true\" label=\"其他限制和要求\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" width=\"140\" prop=\"zlxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" width=\"140\" prop=\"lxxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"药食同源\" align=\"center\">\r\n                    <el-table-column label=\"类型\" prop=\"ystyType\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{selectDictLabel(typeOptions,scope.row.dataObj.ystyType)}}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"使用限制\" prop=\"\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{scope.row.dataObj.ystyMax}}\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"CIR安全级别\" prop=\"finding\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      {{scope.row.dataObj.finding}}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"已上市产品原料使用信息2025/《国际化妆品安全评估数据索引》收录的部分原料使用信息\" width=\"800\" align=\"center\">\r\n                    <el-table-column align=\"center\" label=\"驻留\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"淋洗\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"total(中检院)\" prop=\"maxTotals\" />\r\n                    <el-table-column align=\"center\" label=\"total(国际化妆品)\" prop=\"aqpgMaxTotals\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威评估结论\" :show-overflow-tooltip=\"true\"  width=\"400\" prop=\"dataObj.conclusion\" align=\"center\" />\r\n                  <el-table-column label=\"评估结论\"  width=\"400\" prop=\"finalConclusion\" align=\"center\" />\r\n<!--                  <el-table-column label=\"已使用化妆品原料目录(2021年版)\"   align=\"center\">-->\r\n<!--                    <el-table-column label=\"驻留类\" prop=\"dataObj.zllzglssyl\" align=\"center\" />-->\r\n<!--                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lxlzglssyl\" align=\"center\" />-->\r\n<!--                  </el-table-column>-->\r\n                  <el-table-column label=\"EWG\" align=\"center\">\r\n                    <el-table-column label=\"EWG分值\" prop=\"ewgScore\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                        <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致癌性\" prop=\"dataObj.cancer\" align=\"center\" />\r\n                    <el-table-column label=\"过敏/免疫毒性\" width=\"160\" prop=\"dataObj.allergies\" align=\"center\" />\r\n                    <el-table-column label=\"发育/生殖毒性\" width=\"160\" prop=\"dataObj.developmental\" align=\"center\" />\r\n                    <el-table-column label=\"使用限制\" prop=\"dataObj.useRestrictions\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n                  <el-table-column label=\"是否新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"cirzlx\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"cirlxx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"安全技术规范\" align=\"center\">\r\n                    <el-table-column label=\"规范\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.bzmx  === '否'\"></span>\r\n                        <span v-else>\r\n                        {{scope.row.bzmx}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" prop=\"zlxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" prop=\"lxxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData_\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessmentSimple'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"EWG成分安全分\" prop=\"ewgScore\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='pFomula'\">\r\n            <el-row v-for=\"(item, index) in pFormulaMapData\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"配方编码\">\r\n                  {{item.formulaCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"实验室编码\">\r\n                  {{item.laboratoryCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-button  v-hasPermi=\"['software:softwareDevelopingFormula:genBMaterialInfo']\" type=\"primary\" @click=\"generBMaterialInfo(item.id)\">生成B代码</el-button>\r\n                <span v-if=\"item.materialCode\">该配方已生成了B代码,代码为:{{item.materialCode }}</span>\r\n              </el-col>\r\n              <el-table :data=\"item.materialDatas\" show-summary :summary-method=\"getSummariesPFormula\">\r\n                <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n                <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"200\" />\r\n                <el-table-column v-hasPermi=\"['software:softwareDevelopingFormula:lookMaterialGoodsName']\" label=\"商品名称\" prop=\"materialChiName\" align=\"center\"  width=\"400\" />\r\n                <el-table-column label=\"比例\" prop=\"percentage\" align=\"center\"  width=\"200\" />\r\n              </el-table>\r\n              <br /><br />\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"formulaTableDataList\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"分相\" prop=\"subItem\" align=\"center\"  width=\"60\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\" width=\"80\" />\r\n              <el-table-column label=\"INCI 中文名\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciName}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciNameEng}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  比例({{totalPercentVal}}%)\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"复配百分比(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量(%)\"  width=\"150px\" prop=\"subItem\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{sjTotalPercet}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"最低纯度(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\"  width=\"140px\" prop=\"sjProportion\" align=\"center\">\r\n                <template slot=\"header\" slot-scope=\"scope\">\r\n                  <el-tooltip content=\"按原料最低比例计算\" >\r\n                    <i class=\"el-icon-question\" ></i>\r\n                  </el-tooltip>\r\n                  实际成分含量\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"供应商数据\" prop=\"supplieData\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"周期(天)\" prop=\"orderingcycle\" align=\"center\" />\r\n              <el-table-column label=\"备注\"  width=\"220px\" prop=\"inicRemark\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span v-html=\"scope.row.inicRemark\"></span>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='specMaterial'\">\r\n            <el-table :data=\"specMaterialDatas\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"100\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"150\" />\r\n              <el-table-column label=\"原始INCI 中文名\" prop=\"inciName\" align=\"center\" />\r\n              <el-table-column label=\"替换为\" prop=\"replaceInciName\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div v-if=\"scope.row.inciName==='二氧化钛' || scope.row.inciName==='CI 77891'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas1\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                  <div v-else-if=\"scope.row.inciName==='氧化锌' || scope.row.inciName==='CI 77947'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas2\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <div v-if=\"form.isLock===1  && isLook && specMaterialDatas.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button @loading=\"btnLoading\" @click=\"submitTipsMaterialFormulaInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n          </template>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-form>\r\n\r\n    <div v-if=\"(form.isLock===1 || form.isLock===2) && isLook && activeName!=='workmanship'\" slot=\"footer\" class=\"dialog-footer\" style=\"margin-top:10px\">\r\n      <el-button @loading=\"btnLoading\" v-if=\"form.isDraft===1\" type=\"primary\" @click=\"submitForm(1)\">保存草稿</el-button>\r\n      <el-button @loading=\"btnLoading\" type=\"primary\" @click=\"submitForm(0)\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n\r\n    <el-dialog title=\"选择配方\" :visible.sync=\"visible\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n       <selectFormula @selected=\"selected\"/>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择项目\" :visible.sync=\"categoryOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <template v-for=\"category in categoryList\" >\r\n        <el-divider content-position=\"left\">\r\n          {{category.category}}\r\n          <el-tooltip content=\"全选/反选\" >\r\n            <i class=\"el-icon-circle-check\" @click=\"selectCategory(category)\" />\r\n          </el-tooltip>\r\n        </el-divider>\r\n\r\n        <el-row :gutter=\"20\" class=\"select-wrapper\" >\r\n          <el-col v-for=\"item in category.array\" :key=\"item.id\" :span=\"4\" style=\"display: flex;align-items: center\" >\r\n            <div class=\"item\" @click=\"selectXm(item.id)\" :class=\"xmIds.includes(item.id) ? 'selected':''\">\r\n              {{item.title}}\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </template>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 10px\" >\r\n        <el-button type=\"primary\" size=\"mini\" @click=\"confirmXm\" >确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"添加SPEC\" :visible.sync=\"specOpen\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row v-if=\"specId\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n              {{selectDictLabel(yplyOptions,form.type)}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row v-else>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n               <el-select v-model=\"form.type\" clearable>\r\n                 <el-option\r\n                   v-for=\"dict in yplyOptions\"\r\n                   :key=\"dict.dictValue\"\r\n                   :label=\"dict.dictLabel\"\r\n                   :value=\"dict.dictValue\"\r\n                 ></el-option>\r\n               </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <table class=\"base-table small-table\">\r\n            <tr>\r\n              <th style=\"width: 120px\">类型</th>\r\n              <th style=\"width: 120px\">检测项目</th>\r\n              <th style=\"width: 320px\">检验标准</th>\r\n              <th style=\"width: 320px\">标准值</th>\r\n              <th style=\"width: 120px\">检验频次</th>\r\n            </tr>\r\n            <tr v-for=\"(item,i) in userItemArray\" :key=\"item.id\" >\r\n              <td>{{item.type}}</td>\r\n              <td>{{item.label}}</td>\r\n              <td>{{item.standard}}</td>\r\n              <td><el-input v-model=\"item.standardVal\" /></td>\r\n              <td>{{item.frequency}}</td>\r\n            </tr>\r\n          </table>\r\n        </el-row>\r\n         <div style=\"text-align: center;margin-top:10px\">\r\n          <el-button @loading=\"btnLoading\" @click=\"submitUserSpec\" type=\"primary\">提 交</el-button>\r\n         </div>\r\n       </el-form>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"分享配方\" :visible.sync=\"shareOpen\" width=\"800px\" top=\"5vh\" append-to-body>\r\n      <el-checkbox-group v-model=\"shareDeptIds\">\r\n        < <el-checkbox\r\n          v-for=\"dict in shareDeptDatas\"\r\n        :key=\"dict.id\"\r\n        :label=\"dict.id\">\r\n          {{ dict.name }}\r\n      </el-checkbox>\r\n      </el-checkbox-group>\r\n      <div style=\"margin-top: 10px;text-align: center\">\r\n        <el-button  @loading=\"btnLoading\" @click=\"submitShareFormulaInfo\" type=\"primary\">提 交</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <el-dialog title=\"样品物性\" :visible.sync=\"wxOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <el-tree\r\n        :data=\"wxTree\"\r\n        @node-click=\"handleNodeClick\"\r\n        node-key=\"id\"\r\n        default-expand-all\r\n      />\r\n    </el-dialog>\r\n\r\n    <SoftwareMaterialSave ref=\"softwareMaterialSave\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listSoftwareDevelopingFormula,\r\n  getSoftwareDevelopingFormula,\r\n  delSoftwareDevelopingFormula,\r\n  addSoftwareDevelopingFormula,\r\n  updateSoftwareDevelopingFormula,\r\n  exportSoftwareDevelopingFormula,\r\n  querySoftwareDevelopingFormulaDict,\r\n  queryFormulaClassifyData,\r\n  getFormulaCategoryTree,\r\n  queryFormulaMaterialData,\r\n  getFormulaLabNoInfoByCode,\r\n  queryFormualMaterialRecipeChangeHistoryData,\r\n  queryFormulaZxbzDataList,\r\n  queryFormulaZxbzDataDetail,\r\n  getSoftwareDevelopingFormulaDetail,\r\n  addSoftwareDevelopingFormulaSpecZxbz,\r\n  queryCirHistoryData,\r\n  getCirDataTree,\r\n  queryDuliHistoryData,\r\n  getDuliDataTree,\r\n  addFormulaSpecMaterialData,\r\n  addFormulaSymdForm,\r\n  generatePFormulaInfo,\r\n  generateBMaterialInfo,\r\n  generateNewformulaInfo,\r\n  addSoftwareDevelopingUserFormulaSpecZxbz,\r\n  queryMaterialFormulaSpecDataList,\r\n  queryMaterialFormulaSpecDataDetail,\r\n  addFormulaGyjsBeianInfo,\r\n  queryFormulaLegalGy,\r\n  queryFormulaShareDeptDataList,\r\n  queryFormulaShareDeptDataDetail,\r\n  addFormulaShareDataInfo,\r\n  queryLookFormulaTabs,\r\n  updateSoftwareDevelopingFormulaImg,\r\n  queryFormulaStabilityRecordDataList,\r\n  updateSoftwareDevelopingFormulaFileImg, queryFormulaAppointMaterialDataList\r\n} from \"@/api/software/softwareDevelopingFormula\";\r\nimport {formualList, formualProjectDetail} from \"@/api/project/project\";\r\nimport {getFormulaInfoByCode, getRawMaterialInfoByCode} from \"@/api/software/softwareMaterial\";\r\nimport {isArray, isString} from \"@/utils/validate\";\r\nimport selectFormula from \"@/views/software/formula/components/selectFormula\";\r\nimport {allBcpTemplate, getBcpTemplate} from \"@/api/qc/bcpTemplate\";\r\nimport {allJcxm} from \"@/api/qc/jcxm\";\r\nimport {checkPermi} from \"@/utils/permission\";\r\nimport SoftwareMaterialSave from \"@/views/software/softwareMaterial/save\";\r\nimport { Base64 } from 'js-base64'\r\nimport {selectDictLabel} from \"../../../utils/ruoyi\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\n\r\nexport default {\r\n  name: \"SoftwareDevelopingFormulaSave\",\r\n  components:{selectFormula,SoftwareMaterialSave},\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      wxOpen:false,\r\n      activeName: \"base\",\r\n      currentTab: 'base',\r\n      wxOptions: [],\r\n      conclusionOfSafetyAssessmentName: \"first\",\r\n      loading: false,\r\n      visible: false,\r\n      btnLoading: false,\r\n      categoryOpen: false,\r\n      exportLoading: false,\r\n      fullscreenFlag: false,\r\n      shareOpen: false,\r\n      specOpen: false,\r\n      totalPercentVal:0,\r\n      sjTotalPercet:0,\r\n      isShowMaterialGoodsName:0,\r\n      isGenFormula:0,\r\n      isBMformula:0,\r\n      activeNames: [],\r\n      ids: [],\r\n      statusOptions: [],\r\n      shareDeptDatas: [],\r\n      shareDeptIds: [],\r\n      specId: null,\r\n      ownershopCompanyOptions: [],\r\n      bzxzOptions: [],\r\n      projectList: [],\r\n      typeOptions: [],\r\n      categoryList: [],\r\n      zxbzList: [],\r\n      xmIds: [],\r\n      recipeChangeHistoryData: [],\r\n      formulaTableDataList: [],\r\n      formulaTableDataListBack: [],\r\n      compositionTableDataList: [],\r\n      compositionTableDataListBack: [],\r\n      softwareFormulaSpecList: [],\r\n      specMaterialDatas: [],\r\n      itemArray: [],\r\n      userItemArray: [],\r\n      gtNumStr: '',\r\n      ltNumStr: '',\r\n      itemNames: [],\r\n      zxbzDetail:{},\r\n      gyjsData:{},\r\n      gyjsDataList:[],\r\n      zfylDataList:[],\r\n      gyjsBeianDataList:[],\r\n      zfylBeianDataList:[],\r\n      specObj:{},\r\n      single: true,\r\n      showSearch: false,\r\n      total: 0,\r\n      isCopy: 0,\r\n      softwareDevelopingFormulaList: [],\r\n      efficacyOptions: [],\r\n      otherSpecialClaimsOptions: [],\r\n      formulaMaterialDatas: [],\r\n      chooseFormulaMaterialDatas: [],\r\n      pFormulaMapData: [],\r\n      zybwOptions: [],\r\n      templateList: [],\r\n      cpjxOptions: [],\r\n      syrqOptions: [],\r\n      syffOptions: [],\r\n      wxTree: [],\r\n      purposeOptions: [],\r\n      categoryArray: [],\r\n      jcXmList: [],\r\n      stabilityDataList: [],\r\n      relationStabilityDataList: [],\r\n      jlOptions: [\r\n        10,50,100,500,1000,\r\n      ],\r\n      mjOptions: [\r\n        10,50,100,\r\n      ],\r\n      plOptions: [],\r\n      checkRow: {\r\n        id:null,\r\n        deptId:null\r\n      },\r\n      categoryProps: {\r\n        label: 'categoryName',\r\n        value: 'categoryId'\r\n      },\r\n      cirDataArray: [],\r\n      isLook:false,\r\n      cirDataProps: {\r\n        label: 'label',\r\n        value: 'id'\r\n      },\r\n      duliDataArray: [],\r\n      cosmeticCaseFirstOptions: [],\r\n      cosmeticCaseSecondOptions: [],\r\n      duliDataProps: {\r\n        label: 'zhType',\r\n        value: 'id'\r\n      },\r\n      formulaTabs:[{\r\n        title:'基础信息',\r\n        code:'base'\r\n      },{\r\n        title:'配方页面',\r\n        code:'formulaMaterial'\r\n      },{\r\n        title:'附件',\r\n        code:'formulaFile'\r\n      }],\r\n      title: \"\",\r\n      open: false,\r\n      isEdit: true,\r\n      certificationOptions:[],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        laboratoryCode: null,\r\n        formulaName: null,\r\n        ewgColor: null,\r\n        comclusionType:null\r\n      },\r\n      form: {},\r\n      rules: {\r\n        projectNo: [\r\n          { required: true, message: '请选择项目编码'},\r\n        ],\r\n        customerName: [\r\n          { required: true, message: '客户名称不允许为空'},\r\n        ],\r\n        productName: [\r\n          { required: true, message: '产品名称不允许为空'},\r\n        ],\r\n        cirText: [\r\n          { required: true, message: '请选择CIR历史用量'},\r\n        ],\r\n        duliText: [\r\n          { required: true, message: '请选择毒理使用量参考'},\r\n        ],\r\n        laboratoryCode: [\r\n          { required: true, message: '实验室编码不允许为空'},\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: '配方类别不允许为空'},\r\n        ],\r\n        pflx: [\r\n          { required: true, message: '使用方法不允许为空'},\r\n        ],\r\n      },\r\n      rflOptions:[\r\n        {\r\n          dictValue:'3',\r\n          dictLabel:'染发类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'防脱类'\r\n        }, {\r\n          dictValue:'1',\r\n          dictLabel:'烫发类'\r\n        }\r\n      ],\r\n      yplyOptions:[\r\n        {\r\n          dictValue:'0',\r\n          dictLabel:'检测方法'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'实验室小样'\r\n        }, {\r\n          dictValue:'3',\r\n          dictLabel:'中试样品'\r\n        }, {\r\n          dictValue:'4',\r\n          dictLabel:'大货样品'\r\n        }\r\n      ],\r\n      qiOptions:[\r\n        {\r\n          dictValue:'有香味',\r\n         }, {\r\n          dictValue:'有原料特征性气味',\r\n         }, {\r\n          dictValue:'无味',\r\n         }\r\n      ],\r\n      qbmblOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'祛斑美白类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'祛斑美白类(仅具物理遮盖作用)'\r\n        }\r\n      ],\r\n      cosmeticClassificationOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'第一类化妆品'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'第二类化妆品'\r\n        }\r\n      ],\r\n      caseOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'情形一'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'情形二'\r\n        }\r\n      ],\r\n      useOptions:[\r\n        {\r\n          value:'/',\r\n        },  {\r\n          value:'指定',\r\n        }\r\n      ],\r\n      specMaterialDatas1:[\r\n        {\r\n          value:'二氧化钛'\r\n        },\r\n        {\r\n          value:'CI 77891'\r\n        }\r\n      ],\r\n      specMaterialDatas2:[\r\n        {\r\n          value:'氧化锌'\r\n        },\r\n        {\r\n          value:'CI 77947'\r\n        }\r\n      ],\r\n      ffjtxfxpgOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'高风险'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'中风险'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'低风险'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'无风险'\r\n      },{\r\n        dictValue:'4',\r\n        dictLabel:'测试没通过'\r\n      }],\r\n      wdxOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'进行中'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'测试通过'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'测试失败'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'条件接受'\r\n      }],\r\n      ypFromOptions: [\r\n        {label: '实验室',value: 0},\r\n        {label: '中试',value: 1},\r\n        {label: '生产',value: 2},\r\n        {label: '生技复样',value: 3},\r\n      ],\r\n      stabilityStatusOptions: [\r\n        {label: '进行中',value: 0},\r\n        {label: '测试通过',value: 1},\r\n        {label: '测试失败',value: 2},\r\n        {label: '条件接受',value: 3},\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.wxTree = this.toTree(this.wxOptions, 0)\r\n\r\n    //使用目的\r\n    let certificationRes = await this.getDicts(\"SOFTWARE_CERTIFICATION\")\r\n    this.certificationOptions = certificationRes.data\r\n\r\n    this.getDicts(\"qc_jypl\").then(response => {\r\n      this.plOptions = response.data\r\n    })\r\n  },\r\n  watch: {\r\n    \"$route.query.params\": {\r\n      immediate: true,\r\n      handler() {\r\n        let params = this.$route.query.params;\r\n        if(params) {\r\n          let query = Base64.decode(Base64.decode(params));\r\n          if(query){\r\n            query = JSON.parse(query);\r\n            this.reset();\r\n            this.init(1);\r\n            this.handleUpdate(query.id,query.shareType===1?true:false);\r\n            this.btnLoading = true;\r\n            this.title = query.shareType===1?'修改配方':'查看配方';\r\n          }\r\n        }else{\r\n          this.reset();\r\n          this.init(2);\r\n          this.queryProjectList();\r\n          this.handleAdd();\r\n          this.isLook = true;\r\n        }\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    async showWx() {\r\n      this.wxOpen = true\r\n    },\r\n    selectDictLabel,\r\n    async designateChange(row){\r\n       let designatedUse = row.designatedUse;\r\n       if('指定'===designatedUse){  //指定原料\r\n          let materialId = row.materialId;\r\n          let res = await queryFormulaAppointMaterialDataList({id:materialId});\r\n          row.materialCodes = res;\r\n       }else{\r\n          row.appointCode = '';\r\n          row.materialCodes = [];\r\n       }\r\n    },\r\n    async queryProjectList(){\r\n      let projectList  = await formualList();\r\n      this.projectList = projectList;\r\n    },\r\n    async init(type){\r\n      if(checkPermi(['software:softwareDevelopingFormula:lookMaterialGoodsName'])) {\r\n        this.isShowMaterialGoodsName = 1;\r\n      }else{\r\n        this.isShowMaterialGoodsName = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genPformulaInfo'])) {\r\n        this.isGenFormula = 1;\r\n      }else{\r\n        this.isGenFormula = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genBMFormulaInfo'])) {\r\n        this.isBMformula = 1;\r\n      }else{\r\n        this.isBMformula = 0;\r\n      }\r\n      this.getDicts(\"ZXBZ_STATUS\").then(response => {\r\n        this.statusOptions = response.data;\r\n      })\r\n      this.getDicts(\"OWNERSHOP_COMPANY\").then(response => {\r\n        this.ownershopCompanyOptions = response.data;\r\n      })\r\n      this.getDicts(\"BZXZ\").then(response => {\r\n        this.bzxzOptions = response.data;\r\n      })\r\n      this.getDicts(\"rd_ysty_type\").then(response => {\r\n        this.typeOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE1\").then(response => {\r\n        this.cosmeticCaseFirstOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE2\").then(response => {\r\n        this.cosmeticCaseSecondOptions = response.data;\r\n      })\r\n      const categorySet = new Set()\r\n      const jcXmList = await allJcxm({type:1})\r\n      this.jcXmList = jcXmList\r\n      for (const item of jcXmList) {\r\n        categorySet.add(item.category)\r\n      }\r\n      const categoryList = []\r\n      for (const category of categorySet) {\r\n        categoryList.push({\r\n          category,\r\n          array: jcXmList.filter(i=>i.category === category),\r\n        })\r\n      }\r\n      this.categoryList = categoryList.filter(i=>i.category !== '微生物')\r\n      this.templateList = await allBcpTemplate();\r\n      //配方使用用途\r\n      let purposeRes =  await this.getDicts(\"SOFTWARE_FORMULA_PURPOSE\")\r\n      this.purposeOptions = purposeRes.data\r\n      //获取字典数据\r\n      let dictObj = await querySoftwareDevelopingFormulaDict();\r\n      this.efficacyOptions = dictObj.GXXC_DATA_LIST;\r\n      this.otherSpecialClaimsOptions = dictObj.GXXC_DATA_LIST_OTHER;\r\n      this.zybwOptions = dictObj.ZYBW_DATA_LIST;\r\n      this.cpjxOptions = dictObj.CPJX_DATA_LIST;\r\n      this.syrqOptions = dictObj.SYRQ_DATA_LIST;\r\n      this.syffOptions = dictObj.PFLX_DATA_LIST;\r\n      let categoryAllArray = await queryFormulaClassifyData()\r\n      if(type==2){\r\n        let datas = [22,23,24,25,26,27,43,50,61];\r\n        categoryAllArray = categoryAllArray.filter(i=>!datas.includes(i.categoryId));\r\n      }\r\n      this.categoryArray = await getFormulaCategoryTree(categoryAllArray)\r\n      let zxbzList = await queryFormulaZxbzDataList();\r\n      this.zxbzList = zxbzList;\r\n      //获取cir历史使用量\r\n      let cirDataAllArray = await queryCirHistoryData();\r\n      this.cirDataArray = await getCirDataTree(cirDataAllArray);\r\n      //获取毒理/供应商使用量参考\r\n      let duliDataAllArray = await queryDuliHistoryData();\r\n      this.duliDataArray = await getDuliDataTree(duliDataAllArray);\r\n    },\r\n    async itemNameChange(itemName){\r\n      let itemNames = this.itemNames;\r\n      let arr = itemNames.filter(i=> i.id === itemName)\r\n      let itemNameText = '';\r\n      if(arr && arr[0]) {\r\n        itemNameText = arr[0].text;\r\n      }\r\n       this.form.itemNameText = itemNameText;\r\n     },\r\n    async projectChange(projectNo) {\r\n      //获取项目详情\r\n      let projectDetail = await formualProjectDetail({projectNo});\r\n      let isEdit = true;\r\n      if(projectNo.indexOf(\"P\")!=-1 || projectNo=='210002089' ||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n         isEdit = false;\r\n      }\r\n      this.isEdit = isEdit;\r\n      this.form.itemName = '';\r\n      this.form.itemNameText = '';\r\n      this.itemNames = [];\r\n      if(projectDetail!=null && projectDetail.id){\r\n        this.form.customerName = projectDetail.customerName;\r\n        this.form.productName = projectDetail.productName;\r\n        this.form.brandName = projectDetail.brandName;\r\n        this.form.seriesName = projectDetail.seriesName;\r\n        let itemNames = projectDetail.itemNames;\r\n        if(itemNames){\r\n          itemNames = JSON.parse(itemNames);\r\n          this.itemNames = itemNames;\r\n        }\r\n       }else{\r\n        this.form.customerName = '';\r\n        this.form.productName = '';\r\n        this.form.brandName = '';\r\n        this.form.seriesName = '';\r\n       }\r\n    },\r\n    async zxbzChange(id){\r\n      let zxbzDetail = await queryFormulaZxbzDataDetail({id});\r\n      this.form.execNumber = zxbzDetail.zxbzh;\r\n      this.zxbzDetail = zxbzDetail;\r\n    },\r\n    async getList() {\r\n      let params = Object.assign({},this.queryParams)\r\n      this.loading = true\r\n      let res = await listSoftwareDevelopingFormula(params)\r\n      this.loading = false\r\n      this.softwareDevelopingFormulaList = res.rows\r\n      this.total = res.total\r\n    },\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    async reset() {\r\n      this.currentTab = 'base';\r\n      this.stabilityDataList = [];\r\n      this.relationStabilityDataList = [];\r\n      this.activeNames = [];\r\n      this.isCopy = 0;\r\n      this.isLook = false;\r\n      this.activeName = 'base';\r\n      this.itemNames = [];\r\n      this.formulaMaterialDatas = [];\r\n      this.chooseFormulaMaterialDatas = [];\r\n      this.pFormulaMapData = [];\r\n      this.recipeChangeHistoryData = [];\r\n      this.gyjsData = {};\r\n      this.gyjsDataList = [];\r\n      this.zfylDataList = [];\r\n      this.gyjsBeianDataList = [];\r\n      this.zfylBeianDataList = [];\r\n      this.softwareFormulaSpecList = [];\r\n      this.formulaTableDataList = [];\r\n      this.formulaTableDataListBack = [];\r\n      this.compositionTableDataList = [];\r\n      this.compositionTableDataListBack = [];\r\n      this.specMaterialDatas = [];\r\n      this.itemArray = [];\r\n      this.userItemArray = [];\r\n      this.zxbzDetail = {};\r\n      this.gtNumStr = '';\r\n      this.ltNumStr = '';\r\n      this.specObj = {};\r\n      this.totalPercentVal = 0;\r\n      this.specId = null;\r\n      this.sjTotalPercet = 0;\r\n      this.form = {\r\n        id: null,\r\n        currentTemplateId: null,\r\n        relationMaterialCode: null,\r\n        formulaName: null,\r\n        englishName: null,\r\n        materialCode: null,\r\n        formulaCodeParams: null,\r\n        price: null,\r\n        weight: 100,\r\n        isLock: 1,\r\n        isMateral: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        operator: null,\r\n        createdTime: null,\r\n        isDel: null,\r\n        lastModifiedTime: null,\r\n        note: null,\r\n        formulaCode: null,\r\n        duliId: null,\r\n        cirId: null,\r\n        cirText: null,\r\n        duliText: null,\r\n        laboratoryCode: null,\r\n        productName: null,\r\n        brandId: null,\r\n        customerCode: null,\r\n        customerName: null,\r\n        seriesName: null,\r\n        appearance: null,\r\n        colour: null,\r\n        ph: null,\r\n        viscosity: null,\r\n        stabilityresult: null,\r\n        gxgs: null,\r\n        category: null,\r\n        brandName: null,\r\n        standard: null,\r\n        introFile: [],\r\n        organizationId: null,\r\n        oldFormulaCode: null,\r\n        copyFormulaId: null,\r\n        addTips: null,\r\n        wendingxingFile: [],\r\n        gongyiFile: [],\r\n        xiangrongxingFile: [],\r\n        weishenwuFile: [],\r\n        xiaofeizheFile: [],\r\n        qitaFile: [],\r\n        execNumber: null,\r\n        isDraft: 1,\r\n        gxxc: [],\r\n        gxxcOther: [],\r\n        zybw: [],\r\n        syrq: [],\r\n        cpjx: [],\r\n        pflx: [],\r\n        cpfldm: null,\r\n        cosmeticClassification: null,\r\n        cosmeticCase: null,\r\n        execNumberId: null,\r\n        aqpgjl: null,\r\n        gongyijianshu: null,\r\n        gongyijianshuBeian: null,\r\n        ranfalei: [],\r\n        cosmeticCaseFirst: [],\r\n        cosmeticCaseSecond: [],\r\n        qubanmeibailei: [],\r\n        fangshailei: false,\r\n        sfa: null,\r\n        pa: null,\r\n        yushousfa: null,\r\n        xingongxiao: false,\r\n        xingongxiaocontent: null,\r\n        ftlTime: null,\r\n        filCode: null,\r\n        baCode: null,\r\n        baTime: null,\r\n        filCodeNote: null,\r\n        baCodeNote: null,\r\n        waxcName: null,\r\n        waxcOthername: null,\r\n        waxcStatus: null,\r\n        baStatus: null,\r\n        formulaPid: null,\r\n        bpNote: null,\r\n        zsTime: null,\r\n        zsCode: null,\r\n        gongyijianshuZs: null,\r\n        yfFile: null,\r\n        zsFile: null,\r\n        isLove: null,\r\n        upRate: null,\r\n        oriPrice: null,\r\n        levelNum: null,\r\n        purpose: '普通',\r\n        formulaStatus: 0,\r\n        formulaRemark: null,\r\n        projectNo: null,\r\n        itemName: null,\r\n        itemNameText: null,\r\n        formulaImage: null,\r\n        formulaConstructionIdeas: null,\r\n        isRealse: null,\r\n        materialStatusInfo: null,\r\n        importCountryInfo: null,\r\n        operatorName: null,\r\n        stabilityStatus: null,\r\n        isResult: null,\r\n        isGt: null,\r\n        type: null,\r\n        weishenwuResult: null,\r\n        weishenwuRemark: null,\r\n        xiangrongxingResult: null,\r\n        xiangrongxingRemark: null,\r\n        wendingxingResult: null,\r\n        wendingxingRemark: null,\r\n        materialCycle: null\r\n      };\r\n      this.resetForm(\"form\");\r\n\r\n      if (!this.wxOptions.length) {\r\n        this.wxOptions = await allTreeData({type: 9})\r\n      }\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n    },\r\n    handleFormulaMaterialSelectionChange(selection) {\r\n      this.chooseFormulaMaterialDatas = selection;\r\n    },\r\n    async genPformulaInfo() {\r\n      let chooseFormulaMaterialDatas = this.chooseFormulaMaterialDatas;\r\n      if (chooseFormulaMaterialDatas && chooseFormulaMaterialDatas.length > 0) {\r\n        for (let item of chooseFormulaMaterialDatas) {\r\n          let type = item.type;\r\n          if (type == 1) {\r\n            this.msgError('生成错误,请选择原料编码信息');\r\n            return;\r\n          }\r\n        }\r\n        let id = this.form.id;\r\n        let data = await generatePFormulaInfo({id,formulaMaterialDatas:JSON.stringify(chooseFormulaMaterialDatas)});\r\n        this.msgSuccess(\"操作成功\");\r\n      } else {\r\n        this.msgError('请选择原料信息!');\r\n      }\r\n    },\r\n    limitDecimal(row){\r\n       const inputValue = row.percentage;\r\n      // 正则表达式匹配最多6位小数的数字\r\n      const regex = /^\\d*\\.?\\d{0,6}$/;\r\n      if (regex.test(inputValue)) {\r\n        this.lastValue = inputValue;\r\n      } else {\r\n        // 如果不符合条件，回退到上一个有效值\r\n        row.percentage = this.lastValue;\r\n      }\r\n    },\r\n    async generBMaterialInfo(id) {\r\n       this.$confirm('您确定要生成B代码吗?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"info\"\r\n      }).then(async function () {\r\n         let data = await generateBMaterialInfo({id});\r\n       }).then(() => {\r\n         this.msgSuccess(\"操作成功\");\r\n      }).catch(() => {});\r\n    },\r\n    async genNewformulaInfo() {\r\n      let id = this.form.id;\r\n      let res = await generateNewformulaInfo({id});\r\n      this.msgSuccess(\"操作成功\");\r\n    },\r\n    handleAdd() {\r\n      this.reset();\r\n      this.formulaTabs = [{\r\n        title: '基础信息',\r\n        code: 'base'\r\n      }, {\r\n        title: '配方页面',\r\n        code: 'formulaMaterial'\r\n      }, {\r\n        title: '附件',\r\n        code: 'formulaFile'\r\n      }];\r\n      this.open = true;\r\n      this.title = \"创建配方\";\r\n    },\r\n    async handleUpdate(id,isLook) {\r\n      this.reset();\r\n      let formulaTabs = await queryLookFormulaTabs();\r\n      this.formulaTabs = formulaTabs;\r\n      this.btnLoading = true;\r\n      getSoftwareDevelopingFormulaDetail(id).then(async response => {\r\n        let form = response.data;\r\n        let isEdit = true;\r\n        let projectNo = form.projectNo;\r\n        if(projectNo.indexOf(\"P\")!=-1 ||projectNo=='210002089'||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n          isEdit = false;\r\n        }\r\n        if(form.isLock===2){\r\n          isEdit = false;\r\n        }\r\n        this.isEdit = isEdit;\r\n        if(form.fangshailei){\r\n          form.fangshailei = form.fangshailei==1?true:false;\r\n        }else{\r\n          form.fangshailei = false;\r\n        }\r\n        if(form.xingongxiao){\r\n          form.xingongxiao =form.xingongxiao==1?true:false;\r\n        }else{\r\n          form.xingongxiao = false;\r\n        }\r\n        let specObj = form.specObj;\r\n        if(specObj){\r\n          this.specObj = JSON.parse(specObj);\r\n        }else{\r\n          this.specObj = {};\r\n        }\r\n        let formulaObj = form.formulaObj;\r\n        if(formulaObj){\r\n          formulaObj = JSON.parse(formulaObj);\r\n          this.totalPercentVal = formulaObj.totalPercentVal;\r\n          this.sjTotalPercet = formulaObj.sjTotalPercet;\r\n          if(formulaObj.dataList){\r\n            this.formulaTableDataList = formulaObj.dataList;\r\n            this.formulaTableDataListBack = formulaObj.dataList;\r\n          }else{\r\n            this.formulaTableDataList = [];\r\n            this.formulaTableDataListBack = [];\r\n          }\r\n          if(formulaObj.allList){\r\n            this.compositionTableDataList = formulaObj.allList;\r\n            this.compositionTableDataListBack = formulaObj.allList;\r\n          }else{\r\n            this.compositionTableDataList = [];\r\n            this.compositionTableDataListBack = [];\r\n          }\r\n          if(formulaObj.specMaterialData){\r\n            this.specMaterialDatas = formulaObj.specMaterialData;\r\n          }else{\r\n            this.specMaterialDatas = [];\r\n          }\r\n          if(formulaObj.ltNumStr){\r\n            this.ltNumStr = formulaObj.ltNumStr;\r\n          }else{\r\n            this.ltNumStr = '';\r\n          }\r\n          if(formulaObj.gtNumStr){\r\n            this.gtNumStr = formulaObj.gtNumStr;\r\n          }else{\r\n            this.gtNumStr = '';\r\n          }\r\n        }\r\n        //获取配方原料信息\r\n        let formulaMaterialDatas = form.formulaMaterialDatas;\r\n        if(formulaMaterialDatas){\r\n           this.formulaMaterialDatas = JSON.parse(formulaMaterialDatas);\r\n        }else{\r\n          this.formulaMaterialDatas = [];\r\n        }\r\n        if (form.categoryText) {\r\n          let categoryTextList = form.categoryText.split(',');\r\n          let categoryIds = []\r\n          for (let t of categoryTextList) {\r\n            categoryIds.push(parseInt(t))\r\n          }\r\n          form.categoryText = categoryIds\r\n        } else {\r\n          form.categoryText = [];\r\n        }\r\n        if (form.cirText) {\r\n          let cirTextList = form.cirText.split(',');\r\n          let cirId = []\r\n          for (let t of cirTextList) {\r\n            cirId.push(parseInt(t))\r\n          }\r\n          form.cirText = cirId\r\n        } else {\r\n          form.cirText = [];\r\n        }\r\n        if (form.duliText) {\r\n          let duliTextList = form.duliText.split(',');\r\n          let duliId = []\r\n          for (let t of duliTextList) {\r\n            duliId.push(parseInt(t))\r\n          }\r\n          form.duliText = duliId\r\n        } else {\r\n          form.duliText = [];\r\n        }\r\n        if (form.gxxc) {\r\n          form.gxxc = form.gxxc.split(\",\");\r\n        } else {\r\n          form.gxxc = [];\r\n        }\r\n        if (form.gxxcOther) {\r\n          form.gxxcOther = form.gxxcOther.split(\",\");\r\n        } else {\r\n          form.gxxcOther = [];\r\n        }\r\n        if (form.zybw) {\r\n          form.zybw = form.zybw.split(\",\");\r\n        } else {\r\n          form.zybw = [];\r\n        }\r\n        if (form.syrq) {\r\n          form.syrq = form.syrq.split(\",\");\r\n        } else {\r\n          form.syrq = [];\r\n        }\r\n        if (form.cpjx) {\r\n          form.cpjx = form.cpjx.split(\",\");\r\n        } else {\r\n          form.cpjx = [];\r\n        }\r\n        if (form.pflx) {\r\n          form.pflx = form.pflx.split(\",\");\r\n        } else {\r\n          form.pflx = [];\r\n        }\r\n        if (form.ranfalei) {\r\n          form.ranfalei = form.ranfalei.split(\",\");\r\n        } else {\r\n          form.ranfalei = [];\r\n        }\r\n        if (form.cosmeticCaseFirst) {\r\n          form.cosmeticCaseFirst = form.cosmeticCaseFirst.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseFirst = [];\r\n        }\r\n        if (form.cosmeticCaseSecond) {\r\n          form.cosmeticCaseSecond = form.cosmeticCaseSecond.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseSecond = [];\r\n        }\r\n        if (form.qubanmeibailei) {\r\n          form.qubanmeibailei = form.qubanmeibailei.split(\",\");\r\n        } else {\r\n          form.qubanmeibailei = [];\r\n        }\r\n        if (form.introFile) {\r\n          form.introFile = JSON.parse(form.introFile);\r\n        } else {\r\n          form.introFile = [];\r\n        }\r\n        if (form.wendingxingFile) {\r\n          form.wendingxingFile = JSON.parse(form.wendingxingFile);\r\n        } else {\r\n          form.wendingxingFile = [];\r\n        }\r\n        if (form.gongyiFile) {\r\n          form.gongyiFile = JSON.parse(form.gongyiFile);\r\n        } else {\r\n          form.gongyiFile = [];\r\n        }\r\n        if (form.xiangrongxingFile) {\r\n          form.xiangrongxingFile = JSON.parse(form.xiangrongxingFile);\r\n        } else {\r\n          form.xiangrongxingFile = [];\r\n        }\r\n        if (form.weishenwuFile) {\r\n          form.weishenwuFile = JSON.parse(form.weishenwuFile);\r\n        } else {\r\n          form.weishenwuFile = [];\r\n        }\r\n        if (form.xiaofeizheFile) {\r\n          form.xiaofeizheFile = JSON.parse(form.xiaofeizheFile);\r\n        } else {\r\n          form.xiaofeizheFile = [];\r\n        }\r\n        if (form.qitaFile) {\r\n          form.qitaFile = JSON.parse(form.qitaFile);\r\n        } else {\r\n          form.qitaFile = [];\r\n        }\r\n        let itemNames = [];\r\n        if(form.itemArr){\r\n          itemNames = form.itemArr;\r\n        }\r\n        this.itemNames = itemNames;\r\n\r\n        let pFormulaMapData = [];\r\n        if(form.pFormulaMapData){\r\n          pFormulaMapData = form.pFormulaMapData;\r\n        }\r\n        this.pFormulaMapData = pFormulaMapData;\r\n        //获取信息数据\r\n        let execNumberId = form.execNumberId;\r\n        if(execNumberId){\r\n          this.zxbzChange(execNumberId);\r\n        }else{\r\n          this.zxbzDetail = {};\r\n        }\r\n        let jcxmJson = form.jcxmJson;\r\n        if(jcxmJson){\r\n           this.itemArray = JSON.parse(jcxmJson);\r\n        }else{\r\n          this.itemArray = [];\r\n        }\r\n        this.form = form;\r\n        let gongyijianshu = form.gongyijianshu;\r\n        if(gongyijianshu){\r\n          let gyjsData = JSON.parse(gongyijianshu);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.refreshFormulaLegalGy('0');\r\n            this.gyjsDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsDataList = [];\r\n          this.zfylDataList = [];\r\n          this.refreshFormulaLegalGy('0');\r\n        }\r\n        let gongyijianshuBeian = form.gongyijianshuBeian;\r\n        if(gongyijianshuBeian){\r\n          let gyjsData = JSON.parse(gongyijianshuBeian);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsBeianDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.gyjsBeianDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylBeianDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylBeianDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsBeianDataList = [];\r\n          this.zfylBeianDataList = [];\r\n        }\r\n        this.open = true;\r\n        if(isLook){\r\n          this.title = \"修改配方\";\r\n        }else{\r\n          this.title = \"查看配方\";\r\n        }\r\n        this.isLook = isLook;\r\n        this.btnLoading = false;\r\n      });\r\n      let recipeChangeHistoryData = await queryFormualMaterialRecipeChangeHistoryData({id});\r\n      this.recipeChangeHistoryData = recipeChangeHistoryData;\r\n      //获取spec内容\r\n      this.queryMaterialFormulaSpecDataList(id);\r\n      //获取关联稳定性记录内容\r\n      this.queryFormulaStabilityRecordDataList(id);\r\n    },\r\n    stabilityStatusFormat(row) {\r\n      const arr = this.stabilityStatusOptions.filter(i=> i.value === row.stabilityStatus)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    ypFormat(row) {\r\n      const arr = this.ypFromOptions.filter(i=> i.value === row.ypFrom)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    async queryMaterialFormulaSpecDataList(id) {\r\n      let softwareFormulaSpecList = await queryMaterialFormulaSpecDataList({id});\r\n      this.softwareFormulaSpecList = softwareFormulaSpecList;\r\n    },\r\n    async queryFormulaStabilityRecordDataList(id) {\r\n      let formulaStabilityObj = await queryFormulaStabilityRecordDataList({id});\r\n      let relationStabilityDataList = formulaStabilityObj.relationStabilityDataList;\r\n      let stabilityDataList = formulaStabilityObj.stabilityDataList;\r\n      this.stabilityDataList = stabilityDataList\r\n      this.relationStabilityDataList = relationStabilityDataList\r\n    },\r\n    async copyGongyi() {\r\n      await this.$confirm('是否确认复制工艺数据,会清空已填数据!')\r\n      this.gyjsBeianDataList = JSON.parse(JSON.stringify(this.gyjsDataList));\r\n      this.zfylBeianDataList = JSON.parse(JSON.stringify(this.zfylDataList));\r\n    },\r\n    async submitUploadForm() {\r\n      this.btnLoading = true;\r\n      let formulaImage = this.form.formulaImage;\r\n      let formulaConstructionIdeas = this.form.formulaConstructionIdeas;\r\n      let id = this.form.id;\r\n      let remark = this.form.remark;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n      let params = {\r\n        id,formulaImage,formulaMaterialDatas,remark,formulaConstructionIdeas\r\n      };\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaImg(params);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitUploadFileForm() {\r\n      this.btnLoading = true;\r\n      let form = this.form;\r\n      let param = {};\r\n      param.id = form.id;\r\n      if (form.introFile) {\r\n        param.introFile = JSON.stringify(form.introFile);\r\n      } else {\r\n        param.introFile = \"\";\r\n      }\r\n      if (form.wendingxingFile) {\r\n        param.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      } else {\r\n        param.wendingxingFile = \"\";\r\n      }\r\n      if (form.gongyiFile) {\r\n        param.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      } else {\r\n        param.gongyiFile = \"\";\r\n      }\r\n      if (form.xiangrongxingFile) {\r\n        param.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      } else {\r\n        param.xiangrongxingFile = \"\";\r\n      }\r\n      if (form.weishenwuFile) {\r\n        param.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      } else {\r\n        param.weishenwuFile = \"\";\r\n      }\r\n      if (form.xiaofeizheFile) {\r\n        param.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      } else {\r\n        param.xiaofeizheFile = \"\";\r\n      }\r\n      if (form.qitaFile) {\r\n        param.qitaFile = JSON.stringify(form.qitaFile);\r\n      } else {\r\n        param.qitaFile = \"\";\r\n      }\r\n      param.wendingxingResult = form.wendingxingResult;\r\n      param.wendingxingRemark = form.wendingxingRemark;\r\n      param.xiangrongxingResult = form.xiangrongxingResult;\r\n      param.xiangrongxingRemark = form.xiangrongxingRemark;\r\n      param.weishenwuResult = form.weishenwuResult;\r\n      param.weishenwuRemark = form.weishenwuRemark;\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaFileImg(param);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitForm(isDraft) {\r\n      if(isDraft===0){\r\n        await this.$refs[\"form\"].validate()\r\n      }\r\n      let form = Object.assign({},this.form);\r\n      let projectNo = form.projectNo;\r\n      if(!projectNo || projectNo.length==0){\r\n        this.msgError('请选择项目');\r\n        return;\r\n      }\r\n      let categoryText = form.categoryText;\r\n      if(!categoryText || categoryText.length==0){\r\n        this.msgError('请选择配方类别');\r\n        return;\r\n      }\r\n      form.isDraft = isDraft;\r\n      if(form.categoryText && form.categoryText.length > 0 && isArray(form.categoryText)) {\r\n        form.categoryText = form.categoryText.join(',')\r\n      }else{\r\n        form.categoryText = '';\r\n      }\r\n      if(form.cirText && form.cirText.length > 0 && isArray(form.cirText)) {\r\n        form.cirText = form.cirText.join(',')\r\n      }else{\r\n        form.cirText = '';\r\n      }\r\n      if(form.duliText && form.duliText.length > 0 && isArray(form.duliText)) {\r\n        form.duliText = form.duliText.join(',')\r\n      }else{\r\n        form.duliText = '';\r\n      }\r\n      if(form.gxxc){\r\n        form.gxxc = form.gxxc.join(',');\r\n      }else{\r\n        form.gxxc = '';\r\n      }\r\n      if(form.gxxcOther){\r\n        form.gxxcOther = form.gxxcOther.join(',');\r\n      }else{\r\n        form.gxxcOther = '';\r\n      }\r\n      if(form.zybw){\r\n        form.zybw = form.zybw.join(',');\r\n      }else{\r\n        form.zybw = '';\r\n      }\r\n      if(form.syrq){\r\n        form.syrq = form.syrq.join(',');\r\n      }else{\r\n        form.syrq = '';\r\n      }\r\n      if(form.cpjx){\r\n        form.cpjx = form.cpjx.join(',');\r\n      }else{\r\n        form.cpjx = '';\r\n      }\r\n      if(form.pflx){\r\n        form.pflx = form.pflx.join(',');\r\n      }else{\r\n        form.pflx = '';\r\n      }\r\n      if(form.ranfalei){\r\n        form.ranfalei = form.ranfalei.join(',');\r\n      }else{\r\n        form.ranfalei = '';\r\n      }\r\n      if(form.cosmeticCaseFirst){\r\n        form.cosmeticCaseFirst = form.cosmeticCaseFirst.join(',');\r\n      }else{\r\n        form.cosmeticCaseFirst = '';\r\n      }\r\n      if(form.cosmeticCaseSecond){\r\n        form.cosmeticCaseSecond = form.cosmeticCaseSecond.join(',');\r\n      }else{\r\n        form.cosmeticCaseSecond = '';\r\n      }\r\n      if(form.qubanmeibailei){\r\n        form.qubanmeibailei = form.qubanmeibailei.join(',');\r\n      }else{\r\n        form.qubanmeibailei = '';\r\n      }\r\n      if(form.introFile){\r\n        form.introFile = JSON.stringify(form.introFile);\r\n      }else{\r\n        form.introFile = '';\r\n      }\r\n      if(form.wendingxingFile){\r\n        form.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      }else{\r\n        form.wendingxingFile = '';\r\n      }\r\n      if(form.gongyiFile){\r\n        form.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      }else{\r\n        form.gongyiFile = '';\r\n      }\r\n      if(form.xiangrongxingFile){\r\n        form.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      }else{\r\n        form.xiangrongxingFile = '';\r\n      }\r\n      if(form.weishenwuFile){\r\n        form.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      }else{\r\n        form.weishenwuFile = '';\r\n      }\r\n      if(form.xiaofeizheFile){\r\n        form.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      }else{\r\n        form.xiaofeizheFile = '';\r\n      }\r\n      if(form.qitaFile){\r\n        form.qitaFile = JSON.stringify(form.qitaFile);\r\n      }else{\r\n        form.qitaFile = '';\r\n      }\r\n      if(form.fangshailei){\r\n        form.fangshailei = 1;\r\n      }else{\r\n        form.fangshailei = 0;\r\n      }\r\n      if(form.xingongxiao){\r\n        form.xingongxiao = 1;\r\n      }else{\r\n        form.xingongxiao = 0;\r\n      }\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n        for(let item of formulaMaterialDatas){\r\n          let designatedUse = item.designatedUse;\r\n          let isRelation = item.isRelation;\r\n          let isFx = item.isFx;\r\n          let remark = item.remark;\r\n          let relationCode = item.relationCode;\r\n          let materialCode = item.materialCode;\r\n          let appointCode = item.appointCode;\r\n          if(designatedUse==='指定' && !appointCode){\r\n            this.msgError('请选择指定原料!');\r\n            return;\r\n          }\r\n          if(isRelation==1 && !remark){\r\n            this.msgError('请输入使用代码['+materialCode+']的备注,存在推荐原料['+relationCode+']');\r\n            return;\r\n          }\r\n          if(isFx==1){\r\n            let msg = materialCode + \"为护肤风险原料，请核实!\";\r\n            await this.$confirm(msg, \"警告\", {\r\n              confirmButtonText: \"确定\",\r\n              cancelButtonText: \"取消\",\r\n              type: \"warning\"\r\n            })\r\n          }\r\n        }\r\n        form.formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n        let returnObj = this.isRepeat(formulaMaterialDatas);\r\n        let num = returnObj.num;\r\n        if(num>0){\r\n          let repeatCode = returnObj.repeatCode;\r\n          await this.$confirm('存在重复原料'+repeatCode+',是否确认添加!')\r\n        }\r\n      }else{\r\n        await this.$confirm('您还没有选择原料，确定添加配方？');\r\n        form.formulaMaterialDatas = '';\r\n      }\r\n      if(!form.pflx && isDraft===0){\r\n         this.msgError('请选择使用方法!');\r\n         return;\r\n      }\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.form.currentVersion = parseFloat(this.form.currentVersion) + 1;\r\n          this.msgSuccess(\"修改成功\")\r\n          //this.close();\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        try {\r\n          this.btnLoading = true\r\n          await addSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"新增成功\")\r\n          this.close();\r\n         } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    close() {\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      let view = {\r\n        fullPath : '/rd/softwareDevelopingFormula',\r\n        name:\"SoftwareDevelopingFormula\",\r\n        path:\"/rd/softwareDevelopingFormula\",\r\n        title:\"研发配方\"\r\n      };\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    //判断是否重复\r\n    isRepeat(datas){\r\n       let returnObj = {num:0,repeatCode:''};\r\n       let repeatCodesSet = new Set();\r\n      if(datas && datas.length>0){\r\n         let codes = [];\r\n         for(let item of datas){\r\n            codes.push(item.materialCode);\r\n         }\r\n         for(let code of codes){\r\n             let index = 0;\r\n             for(let item of datas){\r\n                 let materialCode = item.materialCode;\r\n                 if(code === materialCode){\r\n                   index++;\r\n                 }\r\n             }\r\n             if(index>1){\r\n               repeatCodesSet.add(code);\r\n             }\r\n         }\r\n       }\r\n       if(repeatCodesSet && repeatCodesSet.size>0){\r\n         let str = JSON.stringify(Array.from(repeatCodesSet));\r\n         returnObj = {num:1,repeatCode:str};\r\n       }\r\n       return returnObj;\r\n    },\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除研发配方编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delSoftwareDevelopingFormula(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有研发配方数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.exportLoading = true;\r\n          return exportSoftwareDevelopingFormula(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    async queryMaterialCode() {\r\n      let materialCode = this.form.materialCode;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (materialCode) {\r\n        let res = await getRawMaterialInfoByCode({materialCode});\r\n        if(res.data){\r\n          let isRelation = res.data.isRelation;\r\n           if(isRelation==1){\r\n             let tipsInfo = res.data.tipsInfo;\r\n             this.msgInfo(tipsInfo);\r\n          }\r\n          formulaMaterialDatas.unshift(res.data);\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        this.codeChange(1);\r\n      } else {\r\n        this.msgError('请输入原料代码!');\r\n      }\r\n    },\r\n    async queryFormulaCode() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaInfoByCode({formulaCode:formulaCodeParams});\r\n         if(res.data){\r\n          formulaMaterialDatas.unshift(res.data);\r\n          }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入配方编码!');\r\n      }\r\n    },\r\n    async confirmSelectGoods() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaLabNoInfoByCode({laboratoryCode:formulaCodeParams});\r\n         if(res.data){\r\n           formulaMaterialDatas.push(res.data);\r\n         }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入实验室编码!');\r\n      }\r\n    },\r\n    async submitSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.itemArray;\r\n      if(!form.execNumberId){\r\n        this.msgError('请选择执行标准/标准名称');\r\n        return;\r\n      }\r\n      if(!(itemArray &&itemArray.length>0)){\r\n        this.msgError('请选择标准模板');\r\n        return;\r\n      }\r\n      specObj.execNumberId = form.execNumberId;\r\n      specObj.execNumber = form.execNumber;\r\n      specObj.currentTemplateId = this.form.currentTemplateId;\r\n      specObj.formulaId = form.id;\r\n      specObj.itemArray = itemArray;\r\n      specObj.isLock = form.isLock;\r\n      specObj.formulaCode = form.formulaCode;\r\n      specObj.laboratoryCode = form.laboratoryCode;\r\n      specObj.productName = form.productName;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async submitUserSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.userItemArray;\r\n      if(!form.type){\r\n        this.msgError('请选择样品来源');\r\n        return;\r\n      }\r\n      specObj.formulaId = form.id;\r\n      specObj.specId = this.specId;\r\n      specObj.itemArray = itemArray;\r\n      specObj.type = form.type;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingUserFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n        this.specOpen = false;\r\n        this.queryMaterialFormulaSpecDataList(form.id);\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async delFormulaMaterial(row){\r\n      this.formulaMaterialDatas = this.formulaMaterialDatas.filter(x => {\r\n        return x.key != row.key;\r\n      });\r\n    },\r\n    categoryChange(id){\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n      if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        let cosmeticCaseFirst = [];\r\n        if(!cosmeticCaseFirst.includes('1')){\r\n          cosmeticCaseFirst.push('1');\r\n          this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        }\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        this.codeChange(1);\r\n      }\r\n    },\r\n    categoryChangeNew(id){\r\n       let res = 1;\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n       if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        res = 3;\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        res = 2;\r\n      }\r\n      return res;\r\n    },\r\n    async codeChange(type) {\r\n      let code = []\r\n      let form = this.form\r\n      if (form.gxxc.length > 0) {\r\n        code.push(this.efficacyOptions.filter(i => form.gxxc.includes(i.id))\r\n          .sort((n1, n2) => n1.id - n2.id).map(i => i.id).join('/'))\r\n      }\r\n      if (form.zybw.length > 0) {\r\n        code.push(this.zybwOptions.filter(i => form.zybw.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.cpjx.length > 0) {\r\n        code.push(this.cpjxOptions.filter(i => form.cpjx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.syrq.length > 0) {\r\n        code.push(this.syrqOptions.filter(i => form.syrq.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.pflx.length > 0) {\r\n        code.push(this.syffOptions.filter(i => form.pflx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      this.form.cpfldm = code.join('~')\r\n\r\n      if (type == 1) {\r\n        let cosmeticClassification = \"\";\r\n        let cosmeticCase = \"\";\r\n        let gxxc = form.gxxc;\r\n        let gxxc1 = ['A', '3', '4', '1', '2', '5'];\r\n        let gxxc2 = ['14', '15', '19', '6', '23', '25'];\r\n\r\n        let zybw = form.zybw;\r\n        let zybw1 = ['B'];\r\n\r\n        let cpjx = form.cpjx;\r\n        let cpjx2 = ['9', '10'];\r\n\r\n        let syrq = form.syrq;\r\n        let syrq1 = ['C', '1', '2'];\r\n        let syrq2 = ['1', '2'];\r\n        let isProcess = true;\r\n        let cosmeticCaseFirst = [];\r\n        let cosmeticCaseSecond = [];\r\n        if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          if(this.arrayContainsAnother(syrq,syrq2)){\r\n            if(!cosmeticCaseFirst.includes('2')){\r\n              cosmeticCaseFirst.push('2');\r\n            }\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc2)){\r\n          if(!cosmeticCaseSecond.includes('3')){\r\n            cosmeticCaseSecond.push('3');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(cpjx, cpjx2)){\r\n          if(!cosmeticCaseSecond.includes('4')){\r\n            cosmeticCaseSecond.push('4');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(zybw, zybw1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(gxxc, gxxc2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else if (this.arrayContainsAnother(cpjx, cpjx2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '2';\r\n          let res = await this.categoryChangeNew(1);\r\n          if(res===1 || res ===3){\r\n            isProcess = false;\r\n          }else{\r\n            let formulaMaterialDatas = this.formulaMaterialDatas;\r\n            let isFirst = false;\r\n            let isSecond = false;\r\n            for(let item of formulaMaterialDatas){\r\n                let isNewMaterial = item.isNewMaterial;\r\n                let inicNmjyl = item.inicNmjyl;\r\n                if(isNewMaterial=='是'){\r\n                  isFirst = true;\r\n                }else if(inicNmjyl=='是'){\r\n                  isSecond = true;\r\n                }\r\n            }\r\n            if(isFirst){\r\n              cosmeticClassification = '1';\r\n              isProcess = true;\r\n            }else if(isSecond){\r\n              cosmeticClassification = '2';\r\n              cosmeticCase = '1';\r\n              isProcess = true;\r\n            }\r\n          }\r\n        }\r\n        let res1 = await this.categoryChangeNew(1);\r\n        if(res1==3){\r\n          if(!cosmeticCaseFirst.includes('1')){\r\n            cosmeticCaseFirst.push('1');\r\n          }\r\n        }\r\n        let formulaMaterialDatas = this.formulaMaterialDatas;\r\n        for(let item of formulaMaterialDatas){\r\n          let isNewMaterial = item.isNewMaterial;\r\n          let inicNmjyl = item.inicNmjyl;\r\n          let symdInfo = item.symdInfo;\r\n          if(isNewMaterial=='是'){\r\n            if(!cosmeticCaseFirst.includes('3')){\r\n              cosmeticCaseFirst.push('3');\r\n            }\r\n          }\r\n          if(inicNmjyl=='是'){\r\n            if(!cosmeticCaseSecond.includes('1')){\r\n              cosmeticCaseSecond.push('1');\r\n            }\r\n          }\r\n          if(symdInfo.indexOf('防晒剂')!=-1 ||symdInfo.indexOf('光稳定剂')!=-1 ){\r\n            if(!cosmeticCaseSecond.includes('2')){\r\n              cosmeticCaseSecond.push('2');\r\n            }\r\n          }\r\n        }\r\n        this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        this.form.cosmeticCaseSecond = cosmeticCaseSecond;\r\n        if(isProcess){\r\n          this.form.cosmeticClassification = cosmeticClassification;\r\n          this.form.cosmeticCase = cosmeticCase;\r\n        }\r\n\r\n      }\r\n    },\r\n    arrayContainsAnother(arr1, arr2) {\r\n      return arr1.some(item => arr2.includes(item));\r\n    },\r\n    toChoose(){\r\n       this.visible = true;\r\n    },\r\n    async selected(formulaId, laboratoryCode) {\r\n      this.form.oldFormulaCode = laboratoryCode;\r\n      this.form.copyFormulaId = formulaId;\r\n      this.visible = false;\r\n      let formulaMaterialDatas = [];\r\n      if (laboratoryCode && formulaId) {\r\n        let res = await getFormulaLabNoInfoByCode({id: formulaId});\r\n        if (res.data) {\r\n          if(res.data.dataList){\r\n            formulaMaterialDatas = res.data.dataList;\r\n          }\r\n          if(res.data.tips){\r\n            this.form.addTips = res.data.tips;\r\n          }\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n          this.codeChange(1);\r\n        }\r\n      } else {\r\n        this.msgError('请选择配方!');\r\n      }\r\n    },\r\n    handleFormulaSpecAdd(){\r\n       this.specOpen = true;\r\n       this.userItemArray = [];\r\n       this.specId = null;\r\n       this.userItemArray = this.itemArray;\r\n    },\r\n    async handleFormulaSpecEdit(row) {\r\n      this.specOpen = true;\r\n      let dataObj = await queryMaterialFormulaSpecDataDetail({id:row.id});\r\n      let jcxmJson = dataObj.jcxmJson;\r\n      if(jcxmJson){\r\n        this.userItemArray = JSON.parse(jcxmJson);\r\n      }else{\r\n        this.userItemArray = [];\r\n      }\r\n      this.form.type = dataObj.type+'';\r\n      this.specId = dataObj.id;\r\n    },\r\n    async refreshFormulaLegalGy(type){\r\n      if(type==='1'){\r\n        await this.$confirm('是否刷新工艺数据,会清空已填数据!')\r\n      }\r\n       let id = this.form.id;\r\n       this.btnLoading = true;\r\n       let data = await queryFormulaLegalGy({id});\r\n       let gyjsData = data.data;\r\n        let gyjs = gyjsData.gyjs;\r\n        if(gyjs){\r\n          this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n        }else{\r\n          this.gyjsDataList = [];\r\n        }\r\n        let zfyl = gyjsData.zfyl;\r\n        if(gyjs){\r\n          this.zfylDataList = zfyl.map(name => ({ name }));;\r\n        }else{\r\n          this.zfylDataList = [];\r\n        }\r\n       this.btnLoading = false;\r\n    },\r\n    //提交特殊原料信息\r\n    async submitTipsMaterialFormulaInfo() {\r\n      let formulaId = this.form.id;\r\n      let specMaterialDatas = this.specMaterialDatas;\r\n      this.btnLoading = true;\r\n      try {\r\n        let res = await addFormulaSpecMaterialData({id:formulaId,specMaterialDatas: JSON.stringify(specMaterialDatas)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    //提交配方使用目的\r\n    async submitSymdInfo() {\r\n      let formulaId = this.form.id;\r\n      this.btnLoading = true;\r\n      try {\r\n        let formulaSymd = [];\r\n        let compositionTableDataList = this.compositionTableDataList;\r\n        for(let item of compositionTableDataList){\r\n          formulaSymd.push({\r\n            chiName:item.chiName,\r\n            cppfSymd:item.cppfSymd,\r\n          });\r\n        }\r\n        let res = await addFormulaSymdForm({id:formulaId,formulaSymd: JSON.stringify(compositionTableDataList)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    getSummaries(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例(%)'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    getSummariesPFormula(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    formulaMaterialBack(o) {\r\n      let status = o.row.status;\r\n      if(status){\r\n        if(status==3){\r\n          return {\r\n            background: 'orange'\r\n          }\r\n        }else if(status==4 || status==5){\r\n          return {\r\n            background: '#8cc0a8'\r\n            // background: '#9999ff'\r\n          }\r\n        }else if(status>0){\r\n          return {\r\n            background: 'red'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionTableStyle(o) {\r\n      let percert = o.row.percert;\r\n      let isColor = o.row.isColor;\r\n      if(isColor==1){\r\n        return {\r\n          color: 'red'\r\n        }\r\n      }else{\r\n        if(percert<=0.1){\r\n          return {\r\n            color: 'blue'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionCellTableStyle({ row, column }) {\r\n      if (column.label === '中文名称' || column.label === 'INCI 中文名') {\r\n        if (row.isTips===1) {\r\n          return \"background:#9966FF\";\r\n        }\r\n      }\r\n    },\r\n    materialDetails(row){\r\n      if(row.type==0){\r\n        this.$nextTick(async () => {\r\n          this.$refs.softwareMaterialSave.reset();\r\n          this.$refs.softwareMaterialSave.init();\r\n          let dataRow = {id:row.materialId};\r\n          this.$refs.softwareMaterialSave.handleUpdate(dataRow,'查看原料',0);\r\n          this.$refs.softwareMaterialSave.open = true;\r\n        });\r\n      }\r\n    },\r\n    selectable(row,index){\r\n      if (row.isUse == 1) {\r\n        return false\r\n      } else {\r\n        return true\r\n      }\r\n    },\r\n    async addFormulaGyjsBeianInfo(type) {\r\n      let gongyijianshuBeian = {};\r\n      gongyijianshuBeian.gyjs = this.gyjsDataList.map(item => item.name);\r\n      gongyijianshuBeian.zfyl = this.zfylDataList.map(item => item.name);\r\n      let param = {\r\n        id: this.form.id,\r\n        gongyijianshu: JSON.stringify(gongyijianshuBeian),\r\n        type\r\n      };\r\n      if(type===1){\r\n        gongyijianshuBeian.gyjs = this.gyjsBeianDataList.map(item => item.name);\r\n        gongyijianshuBeian.zfyl = this.zfylBeianDataList.map(item => item.name);\r\n        param = {\r\n          id: this.form.id,\r\n          gongyijianshuBeian: JSON.stringify(gongyijianshuBeian),\r\n          type\r\n        };\r\n      }\r\n      let res = await addFormulaGyjsBeianInfo(param);\r\n      this.msgSuccess('保存成功!');\r\n    },\r\n    async changeFun(){\r\n      this.changeTemplate(0);\r\n    },\r\n    async changeTemplate(type) {\r\n      if(this.form.currentTemplateId) {\r\n        try {\r\n          if(type===1){\r\n            await this.$confirm('是否确认带入,会清空已填数据!')\r\n          }\r\n          this.btnLoading = true\r\n          const res = await getBcpTemplate(this.form.currentTemplateId)\r\n          if (res.code === 200 && res.data && res.data.itemArray) {\r\n            let itemArray = JSON.parse(res.data.itemArray);\r\n            for(let item of itemArray){\r\n              item.standardVal = '';\r\n            }\r\n            this.itemArray = itemArray;\r\n          }\r\n          this.btnLoading = false\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    delItem(i) {\r\n      this.itemArray.splice(i,1)\r\n    },\r\n    selectProject() {\r\n      this.categoryOpen = true\r\n      this.xmIds = []\r\n    },\r\n    selectCategory(category) {\r\n      for (const item of category.array) {\r\n        this.selectXm(item.id)\r\n      }\r\n    },\r\n    selectXm(id) {\r\n      if(this.xmIds.includes(id)) {\r\n        this.xmIds = this.xmIds.filter(i=>i !== id)\r\n      } else {\r\n        this.xmIds.push(id)\r\n      }\r\n    },\r\n    confirmXm() {\r\n      if(this.xmIds.length) {\r\n        let arr = this.jcXmList.filter(i=> this.xmIds.includes(i.id))\r\n        const itemArray = this.itemArray\r\n        for (const a of arr) {\r\n          if(!itemArray.map(i=>i.id).includes(a.id)) {\r\n            const o = {\r\n              id: a.id,\r\n              label: a.title,\r\n              type: a.category,\r\n              serialNo: a.seq,\r\n              standard: a.standard,\r\n              frequency: a.frequency,\r\n              standardVal: a.standardVal,\r\n              yqIds: a.yqIds?a.yqIds.split(',').map(i=>Number(i)):[],\r\n            }\r\n            let methodArray = []\r\n            const dataArray = []\r\n            if(a.methodDesc) {\r\n              const methodTemplate = a.methodDesc.replace(/（/g,'(').replace(/）/g,')').replace(/\\s/g,\"\")\r\n              o.methodTemplate = methodTemplate.replace(/\\(\\{param}\\)/g,'_____')\r\n              methodArray = o.methodTemplate.split('_____')\r\n              for (let i = 0; i < methodArray.length -1; i++) {\r\n                const o = {}\r\n                o['param_ava_' + i] = ''\r\n                dataArray.push(o)\r\n              }\r\n            }\r\n            o.methodArray = methodArray\r\n            o.dataArray = dataArray\r\n            itemArray.push(o)\r\n          }\r\n        }\r\n        itemArray.sort((a,b)=>a.serialNo - b.serialNo)\r\n        this.categoryOpen = false\r\n      }\r\n    },\r\n    async handleShare(row) {\r\n      this.shareOpen = true;\r\n      let shareDeptDatas = await queryFormulaShareDeptDataList({id:row.deptId});\r\n      this.shareDeptDatas = shareDeptDatas;\r\n      let shareDeptIds = await queryFormulaShareDeptDataDetail({id:row.id});\r\n      this.shareDeptIds = shareDeptIds;\r\n      let checkRow = {\r\n        id:row.id,\r\n        deptId:row.deptId\r\n      };\r\n      this.checkRow = checkRow;\r\n     },\r\n    async submitShareFormulaInfo(){\r\n       let shareDeptIds = this.shareDeptIds;\r\n       if(shareDeptIds && shareDeptIds.length>0){\r\n          this.btnLoading = true;\r\n          let checkRow = this.checkRow;\r\n          checkRow.shareDeptIds = shareDeptIds.join(\",\");\r\n          let res = await addFormulaShareDataInfo(checkRow);\r\n          this.shareOpen = false;\r\n          this.btnLoading = false;\r\n          this.msgSuccess('操作成功');\r\n       }else {\r\n          this.msgError('请选择要分享的部门!');\r\n       }\r\n    },\r\n    handleCompositionQuery(){\r\n       let ewgColor = this.queryParams.ewgColor;\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let compositionTableDataList = this.compositionTableDataListBack;\r\n       if(ewgColor){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.dataObj.ewgColor === ewgColor);\r\n       }\r\n       if(comclusionType){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.compositionTableDataList = compositionTableDataList;\r\n    },\r\n    resetCompositionQuery(){\r\n      this.compositionTableDataList = this.compositionTableDataListBack;\r\n      this.queryParams.ewgColor = null;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    handleMaterialQuery(){\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let formulaTableDataList = this.formulaTableDataListBack;\r\n       if(comclusionType){\r\n         formulaTableDataList = formulaTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.formulaTableDataList = formulaTableDataList;\r\n    },\r\n    resetMaterialQuery(){\r\n      this.formulaTableDataList = this.formulaTableDataListBack;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    isEditStandard(id){\r\n       let isOpr = true;\r\n       let arr = [1,2,7];\r\n       if(arr.includes(id)){\r\n         isOpr = false;\r\n       }\r\n       return isOpr;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.select-wrapper {\r\n  .item {\r\n    height: 24px;\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n    border: 1px solid #DCDFE6;\r\n    border-radius: 2px;\r\n    box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n    margin-top: 5px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .selected {\r\n    color: #00afff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA60DA,IAAAA,0BAAA,GAAAC,OAAA;AAuCA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAH,OAAA;AACA,IAAAI,cAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,YAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAJ,sBAAA,CAAAL,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AACA,IAAAW,MAAA,GAAAX,OAAA;AACA,IAAAY,SAAA,GAAAZ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAa,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA,sBAAA;IAAAC,oBAAA,EAAAA;EAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,UAAA;MACAC,UAAA;MACAC,SAAA;MACAC,gCAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,YAAA;MACAC,aAAA;MACAC,cAAA;MACAC,SAAA;MACAC,QAAA;MACAC,eAAA;MACAC,aAAA;MACAC,uBAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,GAAA;MACAC,aAAA;MACAC,cAAA;MACAC,YAAA;MACAC,MAAA;MACAC,uBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,YAAA;MACAC,QAAA;MACAC,KAAA;MACAC,uBAAA;MACAC,oBAAA;MACAC,wBAAA;MACAC,wBAAA;MACAC,4BAAA;MACAC,uBAAA;MACAC,iBAAA;MACAC,SAAA;MACAC,aAAA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,UAAA;MACAC,QAAA;MACAC,YAAA;MACAC,YAAA;MACAC,iBAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,MAAA;MACAC,UAAA;MACAC,KAAA;MACAC,MAAA;MACAC,6BAAA;MACAC,eAAA;MACAC,yBAAA;MACAC,oBAAA;MACAC,0BAAA;MACAC,eAAA;MACAC,WAAA;MACAC,YAAA;MACAC,WAAA;MACAC,WAAA;MACAC,WAAA;MACAC,MAAA;MACAC,cAAA;MACAC,aAAA;MACAC,QAAA;MACAC,iBAAA;MACAC,yBAAA;MACAC,SAAA,GACA,uBACA;MACAC,SAAA,GACA,YACA;MACAC,SAAA;MACAC,QAAA;QACAC,EAAA;QACAC,MAAA;MACA;MACAC,aAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,YAAA;MACAC,MAAA;MACAC,YAAA;QACAJ,KAAA;QACAC,KAAA;MACA;MACAI,aAAA;MACAC,wBAAA;MACAC,yBAAA;MACAC,aAAA;QACAR,KAAA;QACAC,KAAA;MACA;MACAQ,WAAA;QACAC,KAAA;QACAC,IAAA;MACA;QACAD,KAAA;QACAC,IAAA;MACA;QACAD,KAAA;QACAC,IAAA;MACA;MACAD,KAAA;MACAE,IAAA;MACAC,MAAA;MACAC,oBAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,WAAA;QACAC,QAAA;QACAC,cAAA;MACA;MACAC,IAAA;MACAC,KAAA;QACAC,SAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACAC,YAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;QAAA,EACA;QACAE,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;QAAA,EACA;QACAG,OAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;QAAA,EACA;QACAI,QAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;QAAA,EACA;QACAR,cAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;QAAA,EACA;QACAK,YAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;QAAA,EACA;QACAM,IAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;QAAA;MAEA;MACAO,UAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAC,WAAA,GACA;QACAF,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAE,SAAA,GACA;QACAH,SAAA;MACA;QACAA,SAAA;MACA;QACAA,SAAA;MACA,EACA;MACAI,YAAA,GACA;QACAJ,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAI,6BAAA,GACA;QACAL,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAK,WAAA,GACA;QACAN,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAM,UAAA,GACA;QACAxC,KAAA;MACA;QACAA,KAAA;MACA,EACA;MACAyC,kBAAA,GACA;QACAzC,KAAA;MACA,GACA;QACAA,KAAA;MACA,EACA;MACA0C,kBAAA,GACA;QACA1C,KAAA;MACA,GACA;QACAA,KAAA;MACA,EACA;MACA2C,gBAAA;QACAV,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;MACAU,UAAA;QACAX,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;QACAD,SAAA;QACAC,SAAA;MACA;MACAW,aAAA,GACA;QAAA9C,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACA8C,sBAAA,GACA;QAAA/C,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACA+C,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IAAA,WAAAC,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAC,QAAA;MAAA,IAAAC,gBAAA;MAAA,WAAAH,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YACAV,KAAA,CAAA9D,MAAA,GAAA8D,KAAA,CAAAW,MAAA,CAAAX,KAAA,CAAA5H,SAAA;;YAEA;YAAAoI,QAAA,CAAAE,IAAA;YAAA,OACAV,KAAA,CAAAY,QAAA;UAAA;YAAAP,gBAAA,GAAAG,QAAA,CAAAK,IAAA;YACAb,KAAA,CAAAnC,oBAAA,GAAAwC,gBAAA,CAAArI,IAAA;YAEAgI,KAAA,CAAAY,QAAA,YAAAE,IAAA,WAAAC,QAAA;cACAf,KAAA,CAAAtD,SAAA,GAAAqE,QAAA,CAAA/I,IAAA;YACA;UAAA;UAAA;YAAA,OAAAwI,QAAA,CAAAQ,IAAA;QAAA;MAAA,GAAAZ,OAAA;IAAA;EACA;EACAa,KAAA;IACA;MACAC,SAAA;MACAC,OAAA,WAAAA,QAAA;QACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,MAAA;QACA,IAAAA,MAAA;UACA,IAAAE,KAAA,GAAAC,cAAA,CAAAC,MAAA,CAAAD,cAAA,CAAAC,MAAA,CAAAJ,MAAA;UACA,IAAAE,KAAA;YACAA,KAAA,GAAAG,IAAA,CAAAC,KAAA,CAAAJ,KAAA;YACA,KAAAK,KAAA;YACA,KAAAC,IAAA;YACA,KAAAC,YAAA,CAAAP,KAAA,CAAA1E,EAAA,EAAA0E,KAAA,CAAAQ,SAAA;YACA,KAAAtJ,UAAA;YACA,KAAAiF,KAAA,GAAA6D,KAAA,CAAAQ,SAAA;UACA;QACA;UACA,KAAAH,KAAA;UACA,KAAAC,IAAA;UACA,KAAAG,gBAAA;UACA,KAAAC,SAAA;UACA,KAAA9E,MAAA;QACA;MACA;IACA;EACA;EACA+E,OAAA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlC,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAiC,SAAA;QAAA,WAAAlC,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cACAyB,MAAA,CAAAlK,MAAA;YAAA;YAAA;cAAA,OAAAqK,SAAA,CAAAtB,IAAA;UAAA;QAAA,GAAAoB,QAAA;MAAA;IACA;IACAG,eAAA,EAAAA,sBAAA;IACAC,eAAA,WAAAA,gBAAAC,GAAA;MAAA,WAAAxC,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAuC,SAAA;QAAA,IAAAC,aAAA,EAAAC,UAAA,EAAAC,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAwC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtC,IAAA,GAAAsC,SAAA,CAAArC,IAAA;YAAA;cACAiC,aAAA,GAAAF,GAAA,CAAAE,aAAA;cAAA,MACA,SAAAA,aAAA;gBAAAI,SAAA,CAAArC,IAAA;gBAAA;cAAA;cAAA;cACAkC,UAAA,GAAAH,GAAA,CAAAG,UAAA;cAAAG,SAAA,CAAArC,IAAA;cAAA,OACA,IAAAsC,8DAAA;gBAAApG,EAAA,EAAAgG;cAAA;YAAA;cAAAC,GAAA,GAAAE,SAAA,CAAAlC,IAAA;cACA4B,GAAA,CAAAQ,aAAA,GAAAJ,GAAA;cAAAE,SAAA,CAAArC,IAAA;cAAA;YAAA;cAEA+B,GAAA,CAAAS,WAAA;cACAT,GAAA,CAAAQ,aAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA/B,IAAA;UAAA;QAAA,GAAA0B,QAAA;MAAA;IAEA;IACAX,gBAAA,WAAAA,iBAAA;MAAA,IAAAoB,MAAA;MAAA,WAAAlD,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAiD,SAAA;QAAA,IAAAzJ,WAAA;QAAA,WAAAuG,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;YAAA;cAAA4C,SAAA,CAAA5C,IAAA;cAAA,OACA,IAAA6C,oBAAA;YAAA;cAAA5J,WAAA,GAAA2J,SAAA,CAAAzC,IAAA;cACAsC,MAAA,CAAAxJ,WAAA,GAAAA,WAAA;YAAA;YAAA;cAAA,OAAA2J,SAAA,CAAAtC,IAAA;UAAA;QAAA,GAAAoC,QAAA;MAAA;IACA;IACAxB,IAAA,WAAAA,KAAA/J,IAAA;MAAA,IAAA2L,MAAA;MAAA,WAAAvD,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAsD,SAAA;QAAA,IAAAC,WAAA,EAAArH,QAAA,EAAAsH,SAAA,EAAAC,KAAA,EAAAC,IAAA,EAAAhK,YAAA,EAAAiK,UAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,gBAAA,EAAAC,KAAA,EAAAtK,QAAA,EAAAuK,eAAA,EAAAC,gBAAA;QAAA,WAAApE,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAiE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/D,IAAA,GAAA+D,SAAA,CAAA9D,IAAA;YAAA;cACA,QAAA+D,sBAAA;gBACAjB,MAAA,CAAAxK,uBAAA;cACA;gBACAwK,MAAA,CAAAxK,uBAAA;cACA;cACA,QAAAyL,sBAAA;gBACAjB,MAAA,CAAAvK,YAAA;cACA;gBACAuK,MAAA,CAAAvK,YAAA;cACA;cACA,QAAAwL,sBAAA;gBACAjB,MAAA,CAAAtK,WAAA;cACA;gBACAsK,MAAA,CAAAtK,WAAA;cACA;cACAsK,MAAA,CAAA5C,QAAA,gBAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAAnK,aAAA,GAAA0H,QAAA,CAAA/I,IAAA;cACA;cACAwL,MAAA,CAAA5C,QAAA,sBAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAA/J,uBAAA,GAAAsH,QAAA,CAAA/I,IAAA;cACA;cACAwL,MAAA,CAAA5C,QAAA,SAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAA9J,WAAA,GAAAqH,QAAA,CAAA/I,IAAA;cACA;cACAwL,MAAA,CAAA5C,QAAA,iBAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAA5J,WAAA,GAAAmH,QAAA,CAAA/I,IAAA;cACA;cACAwL,MAAA,CAAA5C,QAAA,2BAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAAnG,wBAAA,GAAA0D,QAAA,CAAA/I,IAAA;cACA;cACAwL,MAAA,CAAA5C,QAAA,2BAAAE,IAAA,WAAAC,QAAA;gBACAyC,MAAA,CAAAlG,yBAAA,GAAAyD,QAAA,CAAA/I,IAAA;cACA;cACA0L,WAAA,OAAAgB,GAAA;cAAAF,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAiE,aAAA;gBAAA9M,IAAA;cAAA;YAAA;cAAAwE,QAAA,GAAAmI,SAAA,CAAA3D,IAAA;cACA2C,MAAA,CAAAnH,QAAA,GAAAA,QAAA;cAAAsH,SAAA,OAAAiB,2BAAA,CAAA7M,OAAA,EACAsE,QAAA;cAAA;gBAAA,KAAAsH,SAAA,CAAAkB,CAAA,MAAAjB,KAAA,GAAAD,SAAA,CAAAmB,CAAA,IAAAC,IAAA;kBAAAlB,IAAA,GAAAD,KAAA,CAAA5G,KAAA;kBACA0G,WAAA,CAAAsB,GAAA,CAAAnB,IAAA,CAAAoB,QAAA;gBACA;cAAA,SAAAC,GAAA;gBAAAvB,SAAA,CAAAwB,CAAA,CAAAD,GAAA;cAAA;gBAAAvB,SAAA,CAAAyB,CAAA;cAAA;cACAvL,YAAA;cAAAiK,UAAA,OAAAc,2BAAA,CAAA7M,OAAA,EACA2L,WAAA;cAAAc,SAAA,CAAA/D,IAAA;cAAAuD,KAAA,oBAAA9D,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA6D,MAAA;gBAAA,IAAAiB,QAAA;gBAAA,WAAA/E,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+E,OAAAC,SAAA;kBAAA,kBAAAA,SAAA,CAAA7E,IAAA,GAAA6E,SAAA,CAAA5E,IAAA;oBAAA;sBAAAuE,QAAA,GAAAlB,MAAA,CAAA/G,KAAA;sBACAnD,YAAA,CAAA0L,IAAA;wBACAN,QAAA,EAAAA,QAAA;wBACAO,KAAA,EAAAnJ,QAAA,CAAAoJ,MAAA,WAAAC,CAAA;0BAAA,OAAAA,CAAA,CAAAT,QAAA,KAAAA,QAAA;wBAAA;sBACA;oBAAA;oBAAA;sBAAA,OAAAK,SAAA,CAAAtE,IAAA;kBAAA;gBAAA,GAAAgD,KAAA;cAAA;cAAAF,UAAA,CAAAe,CAAA;YAAA;cAAA,KAAAd,MAAA,GAAAD,UAAA,CAAAgB,CAAA,IAAAC,IAAA;gBAAAP,SAAA,CAAA9D,IAAA;gBAAA;cAAA;cAAA,OAAA8D,SAAA,CAAAmB,aAAA,CAAA3B,KAAA;YAAA;cAAAQ,SAAA,CAAA9D,IAAA;cAAA;YAAA;cAAA8D,SAAA,CAAA9D,IAAA;cAAA;YAAA;cAAA8D,SAAA,CAAA/D,IAAA;cAAA+D,SAAA,CAAAoB,EAAA,GAAApB,SAAA;cAAAV,UAAA,CAAAqB,CAAA,CAAAX,SAAA,CAAAoB,EAAA;YAAA;cAAApB,SAAA,CAAA/D,IAAA;cAAAqD,UAAA,CAAAsB,CAAA;cAAA,OAAAZ,SAAA,CAAAqB,MAAA;YAAA;cAEArC,MAAA,CAAA3J,YAAA,GAAAA,YAAA,CAAA4L,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAAT,QAAA;cAAA;cAAAT,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAoF,2BAAA;YAAA;cAAAtC,MAAA,CAAA1H,YAAA,GAAA0I,SAAA,CAAA3D,IAAA;cAAA2D,SAAA,CAAA9D,IAAA;cAAA,OAEA8C,MAAA,CAAA5C,QAAA;YAAA;cAAAqD,UAAA,GAAAO,SAAA,CAAA3D,IAAA;cACA2C,MAAA,CAAArH,cAAA,GAAA8H,UAAA,CAAAjM,IAAA;cACA;cAAAwM,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAqF,6DAAA;YAAA;cAAA7B,OAAA,GAAAM,SAAA,CAAA3D,IAAA;cACA2C,MAAA,CAAAhI,eAAA,GAAA0I,OAAA,CAAA8B,cAAA;cACAxC,MAAA,CAAA/H,yBAAA,GAAAyI,OAAA,CAAA+B,oBAAA;cACAzC,MAAA,CAAA3H,WAAA,GAAAqI,OAAA,CAAAgC,cAAA;cACA1C,MAAA,CAAAzH,WAAA,GAAAmI,OAAA,CAAAiC,cAAA;cACA3C,MAAA,CAAAxH,WAAA,GAAAkI,OAAA,CAAAkC,cAAA;cACA5C,MAAA,CAAAvH,WAAA,GAAAiI,OAAA,CAAAmC,cAAA;cAAA7B,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAA4F,mDAAA;YAAA;cAAAnC,gBAAA,GAAAK,SAAA,CAAA3D,IAAA;cACA,IAAAhJ,IAAA;gBACAuM,KAAA;gBACAD,gBAAA,GAAAA,gBAAA,CAAAsB,MAAA,WAAAC,CAAA;kBAAA,QAAAtB,KAAA,CAAAmC,QAAA,CAAAb,CAAA,CAAAc,UAAA;gBAAA;cACA;cAAAhC,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAA+F,iDAAA,EAAAtC,gBAAA;YAAA;cAAAX,MAAA,CAAApH,aAAA,GAAAoI,SAAA,CAAA3D,IAAA;cAAA2D,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAgG,mDAAA;YAAA;cAAA5M,QAAA,GAAA0K,SAAA,CAAA3D,IAAA;cACA2C,MAAA,CAAA1J,QAAA,GAAAA,QAAA;cACA;cAAA0K,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAiG,8CAAA;YAAA;cAAAtC,eAAA,GAAAG,SAAA,CAAA3D,IAAA;cAAA2D,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAkG,yCAAA,EAAAvC,eAAA;YAAA;cAAAb,MAAA,CAAAvG,YAAA,GAAAuH,SAAA,CAAA3D,IAAA;cAAA2D,SAAA,CAAA9D,IAAA;cAAA,OAEA,IAAAmG,+CAAA;YAAA;cAAAvC,gBAAA,GAAAE,SAAA,CAAA3D,IAAA;cAAA2D,SAAA,CAAA9D,IAAA;cAAA,OACA,IAAAoG,0CAAA,EAAAxC,gBAAA;YAAA;cAAAd,MAAA,CAAApG,aAAA,GAAAoH,SAAA,CAAA3D,IAAA;YAAA;YAAA;cAAA,OAAA2D,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IACA;IACAsD,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAhH,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA+G,SAAA;QAAA,IAAAvM,SAAA,EAAAwM,GAAA,EAAAC,YAAA;QAAA,WAAAlH,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7G,IAAA,GAAA6G,SAAA,CAAA5G,IAAA;YAAA;cACA/F,SAAA,GAAAsM,MAAA,CAAAtM,SAAA;cACAwM,GAAA,GAAAxM,SAAA,CAAA8K,MAAA,WAAAC,CAAA;gBAAA,OAAAA,CAAA,CAAA9I,EAAA,KAAAoK,QAAA;cAAA;cACAI,YAAA;cACA,IAAAD,GAAA,IAAAA,GAAA;gBACAC,YAAA,GAAAD,GAAA,IAAAI,IAAA;cACA;cACAN,MAAA,CAAA5I,IAAA,CAAA+I,YAAA,GAAAA,YAAA;YAAA;YAAA;cAAA,OAAAE,SAAA,CAAAtG,IAAA;UAAA;QAAA,GAAAkG,QAAA;MAAA;IACA;IACAM,aAAA,WAAAA,cAAAjJ,SAAA;MAAA,IAAAkJ,MAAA;MAAA,WAAAxH,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAuH,SAAA;QAAA,IAAAC,aAAA,EAAA/J,MAAA,EAAAjD,SAAA;QAAA,WAAAuF,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAsH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApH,IAAA,GAAAoH,SAAA,CAAAnH,IAAA;YAAA;cAAAmH,SAAA,CAAAnH,IAAA;cAAA,OAEA,IAAAoH,6BAAA;gBAAAvJ,SAAA,EAAAA;cAAA;YAAA;cAAAoJ,aAAA,GAAAE,SAAA,CAAAhH,IAAA;cACAjD,MAAA;cACA,IAAAW,SAAA,CAAAwJ,OAAA,eAAAxJ,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA;gBACAX,MAAA;cACA;cACA6J,MAAA,CAAA7J,MAAA,GAAAA,MAAA;cACA6J,MAAA,CAAApJ,IAAA,CAAA2I,QAAA;cACAS,MAAA,CAAApJ,IAAA,CAAA+I,YAAA;cACAK,MAAA,CAAA9M,SAAA;cACA,IAAAgN,aAAA,YAAAA,aAAA,CAAA/K,EAAA;gBACA6K,MAAA,CAAApJ,IAAA,CAAAK,YAAA,GAAAiJ,aAAA,CAAAjJ,YAAA;gBACA+I,MAAA,CAAApJ,IAAA,CAAAM,WAAA,GAAAgJ,aAAA,CAAAhJ,WAAA;gBACA8I,MAAA,CAAApJ,IAAA,CAAA2J,SAAA,GAAAL,aAAA,CAAAK,SAAA;gBACAP,MAAA,CAAApJ,IAAA,CAAA4J,UAAA,GAAAN,aAAA,CAAAM,UAAA;gBACAtN,SAAA,GAAAgN,aAAA,CAAAhN,SAAA;gBACA,IAAAA,SAAA;kBACAA,SAAA,GAAA8G,IAAA,CAAAC,KAAA,CAAA/G,SAAA;kBACA8M,MAAA,CAAA9M,SAAA,GAAAA,SAAA;gBACA;cACA;gBACA8M,MAAA,CAAApJ,IAAA,CAAAK,YAAA;gBACA+I,MAAA,CAAApJ,IAAA,CAAAM,WAAA;gBACA8I,MAAA,CAAApJ,IAAA,CAAA2J,SAAA;gBACAP,MAAA,CAAApJ,IAAA,CAAA4J,UAAA;cACA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAA7G,IAAA;UAAA;QAAA,GAAA0G,QAAA;MAAA;IACA;IACAQ,UAAA,WAAAA,WAAAtL,EAAA;MAAA,IAAAuL,MAAA;MAAA,WAAAlI,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAiI,SAAA;QAAA,IAAAxN,UAAA;QAAA,WAAAsF,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAA5H,IAAA;YAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OACA,IAAA6H,qDAAA;gBAAA3L,EAAA,EAAAA;cAAA;YAAA;cAAAhC,UAAA,GAAA0N,SAAA,CAAAzH,IAAA;cACAsH,MAAA,CAAA9J,IAAA,CAAAmK,UAAA,GAAA5N,UAAA,CAAA6N,KAAA;cACAN,MAAA,CAAAvN,UAAA,GAAAA,UAAA;YAAA;YAAA;cAAA,OAAA0N,SAAA,CAAAtH,IAAA;UAAA;QAAA,GAAAoH,QAAA;MAAA;IACA;IACAM,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAA1I,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAyI,SAAA;QAAA,IAAAxH,MAAA,EAAAyB,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAuI,UAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArI,IAAA,GAAAqI,UAAA,CAAApI,IAAA;YAAA;cACAU,MAAA,GAAA2H,MAAA,CAAAC,MAAA,KAAAL,MAAA,CAAA7K,WAAA;cACA6K,MAAA,CAAArQ,OAAA;cAAAwQ,UAAA,CAAApI,IAAA;cAAA,OACA,IAAAuI,wDAAA,EAAA7H,MAAA;YAAA;cAAAyB,GAAA,GAAAiG,UAAA,CAAAjI,IAAA;cACA8H,MAAA,CAAArQ,OAAA;cACAqQ,MAAA,CAAApN,6BAAA,GAAAsH,GAAA,CAAAqG,IAAA;cACAP,MAAA,CAAAtN,KAAA,GAAAwH,GAAA,CAAAxH,KAAA;YAAA;YAAA;cAAA,OAAAyN,UAAA,CAAA9H,IAAA;UAAA;QAAA,GAAA4H,QAAA;MAAA;IACA;IACAO,MAAA,WAAAA,OAAA;MACA,KAAAxL,IAAA;MACA,KAAAgE,KAAA;IACA;IACAA,KAAA,WAAAA,MAAA;MAAA,IAAAyH,MAAA;MAAA,WAAAnJ,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAkJ,UAAA;QAAA,WAAAnJ,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAgJ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA9I,IAAA,GAAA8I,UAAA,CAAA7I,IAAA;YAAA;cACA0I,MAAA,CAAAjR,UAAA;cACAiR,MAAA,CAAA9M,iBAAA;cACA8M,MAAA,CAAA7M,yBAAA;cACA6M,MAAA,CAAAjQ,WAAA;cACAiQ,MAAA,CAAA9N,MAAA;cACA8N,MAAA,CAAAlM,MAAA;cACAkM,MAAA,CAAAlR,UAAA;cACAkR,MAAA,CAAAzO,SAAA;cACAyO,MAAA,CAAA1N,oBAAA;cACA0N,MAAA,CAAAzN,0BAAA;cACAyN,MAAA,CAAAxN,eAAA;cACAwN,MAAA,CAAApP,uBAAA;cACAoP,MAAA,CAAAvO,QAAA;cACAuO,MAAA,CAAAtO,YAAA;cACAsO,MAAA,CAAArO,YAAA;cACAqO,MAAA,CAAApO,iBAAA;cACAoO,MAAA,CAAAnO,iBAAA;cACAmO,MAAA,CAAA/O,uBAAA;cACA+O,MAAA,CAAAnP,oBAAA;cACAmP,MAAA,CAAAlP,wBAAA;cACAkP,MAAA,CAAAjP,wBAAA;cACAiP,MAAA,CAAAhP,4BAAA;cACAgP,MAAA,CAAA9O,iBAAA;cACA8O,MAAA,CAAA7O,SAAA;cACA6O,MAAA,CAAA5O,aAAA;cACA4O,MAAA,CAAAxO,UAAA;cACAwO,MAAA,CAAA3O,QAAA;cACA2O,MAAA,CAAA1O,QAAA;cACA0O,MAAA,CAAAlO,OAAA;cACAkO,MAAA,CAAAtQ,eAAA;cACAsQ,MAAA,CAAA5P,MAAA;cACA4P,MAAA,CAAArQ,aAAA;cACAqQ,MAAA,CAAA/K,IAAA;gBACAzB,EAAA;gBACA4M,iBAAA;gBACAC,oBAAA;gBACAvL,WAAA;gBACAwL,WAAA;gBACAC,YAAA;gBACAC,iBAAA;gBACAC,KAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,SAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,QAAA;gBACAC,WAAA;gBACAC,KAAA;gBACAC,gBAAA;gBACAC,IAAA;gBACAC,WAAA;gBACAC,MAAA;gBACAC,KAAA;gBACA9L,OAAA;gBACAC,QAAA;gBACAZ,cAAA;gBACAU,WAAA;gBACAgM,OAAA;gBACAC,YAAA;gBACAlM,YAAA;gBACAuJ,UAAA;gBACA4C,UAAA;gBACAC,MAAA;gBACAC,EAAA;gBACAC,SAAA;gBACAC,eAAA;gBACAC,IAAA;gBACAjG,QAAA;gBACA+C,SAAA;gBACAmD,QAAA;gBACAC,SAAA;gBACAC,cAAA;gBACAC,cAAA;gBACAC,aAAA;gBACAC,OAAA;gBACAC,eAAA;gBACAC,UAAA;gBACAC,iBAAA;gBACAC,aAAA;gBACAC,cAAA;gBACAC,QAAA;gBACAtD,UAAA;gBACAuD,OAAA;gBACAC,IAAA;gBACAC,SAAA;gBACAC,IAAA;gBACAC,IAAA;gBACAC,IAAA;gBACArN,IAAA;gBACAsN,MAAA;gBACAC,sBAAA;gBACAC,YAAA;gBACAC,YAAA;gBACAC,MAAA;gBACAC,aAAA;gBACAC,kBAAA;gBACAC,QAAA;gBACAC,iBAAA;gBACAC,kBAAA;gBACAC,cAAA;gBACAC,WAAA;gBACAC,GAAA;gBACAC,EAAA;gBACAC,SAAA;gBACAC,WAAA;gBACAC,kBAAA;gBACAC,OAAA;gBACAC,OAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,WAAA;gBACAC,UAAA;gBACAC,QAAA;gBACAC,aAAA;gBACAC,UAAA;gBACAC,QAAA;gBACAC,UAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,eAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,MAAA;gBACAC,QAAA;gBACAC,QAAA;gBACAC,OAAA;gBACAC,aAAA;gBACAC,aAAA;gBACAtQ,SAAA;gBACAyI,QAAA;gBACAI,YAAA;gBACA0H,YAAA;gBACAC,wBAAA;gBACAC,QAAA;gBACAC,kBAAA;gBACAC,iBAAA;gBACAC,YAAA;gBACAC,eAAA;gBACAC,QAAA;gBACAC,IAAA;gBACAzX,IAAA;gBACA0X,eAAA;gBACAC,eAAA;gBACAC,mBAAA;gBACAC,mBAAA;gBACAC,iBAAA;gBACAC,iBAAA;gBACAC,aAAA;cACA;cACAzG,MAAA,CAAA0G,SAAA;cAAA,IAEA1G,MAAA,CAAAhR,SAAA,CAAA2X,MAAA;gBAAAxG,UAAA,CAAA7I,IAAA;gBAAA;cAAA;cAAA6I,UAAA,CAAA7I,IAAA;cAAA,OACA,IAAAsP,qBAAA;gBAAAnY,IAAA;cAAA;YAAA;cAAAuR,MAAA,CAAAhR,SAAA,GAAAmR,UAAA,CAAA1I,IAAA;YAAA;YAAA;cAAA,OAAA0I,UAAA,CAAAvI,IAAA;UAAA;QAAA,GAAAqI,SAAA;MAAA;IAEA;IACA4G,WAAA,WAAAA,YAAA;MACA,KAAAnS,WAAA,CAAAC,OAAA;MACA,KAAA2K,OAAA;IACA;IACAwH,UAAA,WAAAA,WAAA;MACA,KAAAJ,SAAA;MACA,KAAAG,WAAA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAhX,GAAA,GAAAgX,SAAA,CAAAC,GAAA,WAAAxM,IAAA;QAAA,OAAAA,IAAA,CAAAjH,EAAA;MAAA;IACA;IACA0T,oCAAA,WAAAA,qCAAAF,SAAA;MACA,KAAAzU,0BAAA,GAAAyU,SAAA;IACA;IACAG,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvQ,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAsQ,UAAA;QAAA,IAAA9U,0BAAA,EAAA+U,UAAA,EAAAC,MAAA,EAAA9M,IAAA,EAAAhM,IAAA,EAAA+E,EAAA,EAAA5E,IAAA;QAAA,WAAAkI,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAsQ,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApQ,IAAA,GAAAoQ,UAAA,CAAAnQ,IAAA;YAAA;cACA/E,0BAAA,GAAA6U,OAAA,CAAA7U,0BAAA;cAAA,MACAA,0BAAA,IAAAA,0BAAA,CAAAoU,MAAA;gBAAAc,UAAA,CAAAnQ,IAAA;gBAAA;cAAA;cAAAgQ,UAAA,OAAA9L,2BAAA,CAAA7M,OAAA,EACA4D,0BAAA;cAAAkV,UAAA,CAAApQ,IAAA;cAAAiQ,UAAA,CAAA7L,CAAA;YAAA;cAAA,KAAA8L,MAAA,GAAAD,UAAA,CAAA5L,CAAA,IAAAC,IAAA;gBAAA8L,UAAA,CAAAnQ,IAAA;gBAAA;cAAA;cAAAmD,IAAA,GAAA8M,MAAA,CAAA3T,KAAA;cACAnF,IAAA,GAAAgM,IAAA,CAAAhM,IAAA;cAAA,MACAA,IAAA;gBAAAgZ,UAAA,CAAAnQ,IAAA;gBAAA;cAAA;cACA8P,OAAA,CAAAM,QAAA;cAAA,OAAAD,UAAA,CAAAE,MAAA;YAAA;cAAAF,UAAA,CAAAnQ,IAAA;cAAA;YAAA;cAAAmQ,UAAA,CAAAnQ,IAAA;cAAA;YAAA;cAAAmQ,UAAA,CAAApQ,IAAA;cAAAoQ,UAAA,CAAAG,EAAA,GAAAH,UAAA;cAAAH,UAAA,CAAAvL,CAAA,CAAA0L,UAAA,CAAAG,EAAA;YAAA;cAAAH,UAAA,CAAApQ,IAAA;cAAAiQ,UAAA,CAAAtL,CAAA;cAAA,OAAAyL,UAAA,CAAAhL,MAAA;YAAA;cAIAjJ,EAAA,GAAA4T,OAAA,CAAAnS,IAAA,CAAAzB,EAAA;cAAAiU,UAAA,CAAAnQ,IAAA;cAAA,OACA,IAAAuQ,+CAAA;gBAAArU,EAAA,EAAAA,EAAA;gBAAAlB,oBAAA,EAAA+F,IAAA,CAAAyP,SAAA,CAAAvV,0BAAA;cAAA;YAAA;cAAA3D,IAAA,GAAA6Y,UAAA,CAAAhQ,IAAA;cACA2P,OAAA,CAAAW,UAAA;cAAAN,UAAA,CAAAnQ,IAAA;cAAA;YAAA;cAEA8P,OAAA,CAAAM,QAAA;YAAA;YAAA;cAAA,OAAAD,UAAA,CAAA7P,IAAA;UAAA;QAAA,GAAAyP,SAAA;MAAA;IAEA;IACAW,YAAA,WAAAA,aAAA3O,GAAA;MACA,IAAA4O,UAAA,GAAA5O,GAAA,CAAA6O,UAAA;MACA;MACA,IAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,IAAA,CAAAH,UAAA;QACA,KAAAI,SAAA,GAAAJ,UAAA;MACA;QACA;QACA5O,GAAA,CAAA6O,UAAA,QAAAG,SAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA9U,EAAA;MAAA,IAAA+U,OAAA;MAAA,WAAA1R,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAyR,UAAA;QAAA,WAAA1R,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAuR,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArR,IAAA,GAAAqR,UAAA,CAAApR,IAAA;YAAA;cACAiR,OAAA,CAAAI,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACApa,IAAA;cACA,GAAAiJ,IAAA,kBAAAb,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA+R,UAAA;gBAAA,IAAAla,IAAA;gBAAA,WAAAkI,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA6R,WAAAC,UAAA;kBAAA,kBAAAA,UAAA,CAAA3R,IAAA,GAAA2R,UAAA,CAAA1R,IAAA;oBAAA;sBAAA0R,UAAA,CAAA1R,IAAA;sBAAA,OACA,IAAA2R,gDAAA;wBAAAzV,EAAA,EAAAA;sBAAA;oBAAA;sBAAA5E,IAAA,GAAAoa,UAAA,CAAAvR,IAAA;oBAAA;oBAAA;sBAAA,OAAAuR,UAAA,CAAApR,IAAA;kBAAA;gBAAA,GAAAkR,SAAA;cAAA,CACA,IAAApR,IAAA;gBACA6Q,OAAA,CAAAR,UAAA;cACA,GAAAmB,KAAA;YAAA;YAAA;cAAA,OAAAR,UAAA,CAAA9Q,IAAA;UAAA;QAAA,GAAA4Q,SAAA;MAAA;IACA;IACAW,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAvS,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAsS,UAAA;QAAA,IAAA7V,EAAA,EAAAiG,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAoS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAlS,IAAA,GAAAkS,UAAA,CAAAjS,IAAA;YAAA;cACA9D,EAAA,GAAA4V,OAAA,CAAAnU,IAAA,CAAAzB,EAAA;cAAA+V,UAAA,CAAAjS,IAAA;cAAA,OACA,IAAAkS,iDAAA;gBAAAhW,EAAA,EAAAA;cAAA;YAAA;cAAAiG,GAAA,GAAA8P,UAAA,CAAA9R,IAAA;cACA2R,OAAA,CAAArB,UAAA;YAAA;YAAA;cAAA,OAAAwB,UAAA,CAAA3R,IAAA;UAAA;QAAA,GAAAyR,SAAA;MAAA;IACA;IACAzQ,SAAA,WAAAA,UAAA;MACA,KAAAL,KAAA;MACA,KAAAnE,WAAA;QACAC,KAAA;QACAC,IAAA;MACA;QACAD,KAAA;QACAC,IAAA;MACA;QACAD,KAAA;QACAC,IAAA;MACA;MACA,KAAAC,IAAA;MACA,KAAAF,KAAA;IACA;IACAoE,YAAA,WAAAA,aAAAjF,EAAA,EAAAM,MAAA;MAAA,IAAA2V,OAAA;MAAA,WAAA5S,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA2S,UAAA;QAAA,IAAAtV,WAAA,EAAAxD,uBAAA;QAAA,WAAAkG,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAyS,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvS,IAAA,GAAAuS,UAAA,CAAAtS,IAAA;YAAA;cACAmS,OAAA,CAAAlR,KAAA;cAAAqR,UAAA,CAAAtS,IAAA;cAAA,OACA,IAAAuS,+CAAA;YAAA;cAAAzV,WAAA,GAAAwV,UAAA,CAAAnS,IAAA;cACAgS,OAAA,CAAArV,WAAA,GAAAA,WAAA;cACAqV,OAAA,CAAAra,UAAA;cACA,IAAA0a,6DAAA,EAAAtW,EAAA,EAAAkE,IAAA;gBAAA,IAAAqS,KAAA,OAAAlT,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAiT,UAAArS,QAAA;kBAAA,IAAA1C,IAAA,EAAAT,MAAA,EAAAW,SAAA,EAAArD,OAAA,EAAAmY,UAAA,EAAA3X,oBAAA,EAAA4X,gBAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,MAAA,EAAAC,CAAA,EAAAC,WAAA,EAAAjJ,KAAA,EAAAkJ,UAAA,EAAAC,MAAA,EAAAC,EAAA,EAAAC,YAAA,EAAAtJ,MAAA,EAAAuJ,UAAA,EAAAC,MAAA,EAAAC,GAAA,EAAAvZ,SAAA,EAAAiB,eAAA,EAAA4Q,YAAA,EAAA2H,QAAA,EAAAzH,aAAA,EAAA7R,QAAA,EAAAuZ,IAAA,EAAAC,IAAA,EAAA1H,kBAAA,EAAA2H,SAAA,EAAAC,KAAA,EAAAC,KAAA;kBAAA,WAAAtU,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAmU,WAAAC,UAAA;oBAAA,kBAAAA,UAAA,CAAAjU,IAAA,GAAAiU,UAAA,CAAAhU,IAAA;sBAAA;wBACArC,IAAA,GAAA0C,QAAA,CAAA/I,IAAA;wBACA4F,MAAA;wBACAW,SAAA,GAAAF,IAAA,CAAAE,SAAA;wBACA,IAAAA,SAAA,CAAAwJ,OAAA,eAAAxJ,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA,mBAAAA,SAAA;0BACAX,MAAA;wBACA;wBACA,IAAAS,IAAA,CAAA0L,MAAA;0BACAnM,MAAA;wBACA;wBACAiV,OAAA,CAAAjV,MAAA,GAAAA,MAAA;wBACA,IAAAS,IAAA,CAAA2O,WAAA;0BACA3O,IAAA,CAAA2O,WAAA,GAAA3O,IAAA,CAAA2O,WAAA;wBACA;0BACA3O,IAAA,CAAA2O,WAAA;wBACA;wBACA,IAAA3O,IAAA,CAAA+O,WAAA;0BACA/O,IAAA,CAAA+O,WAAA,GAAA/O,IAAA,CAAA+O,WAAA;wBACA;0BACA/O,IAAA,CAAA+O,WAAA;wBACA;wBACAlS,OAAA,GAAAmD,IAAA,CAAAnD,OAAA;wBACA,IAAAA,OAAA;0BACA2X,OAAA,CAAA3X,OAAA,GAAAuG,IAAA,CAAAC,KAAA,CAAAxG,OAAA;wBACA;0BACA2X,OAAA,CAAA3X,OAAA;wBACA;wBACAmY,UAAA,GAAAhV,IAAA,CAAAgV,UAAA;wBACA,IAAAA,UAAA;0BACAA,UAAA,GAAA5R,IAAA,CAAAC,KAAA,CAAA2R,UAAA;0BACAR,OAAA,CAAA/Z,eAAA,GAAAua,UAAA,CAAAva,eAAA;0BACA+Z,OAAA,CAAA9Z,aAAA,GAAAsa,UAAA,CAAAta,aAAA;0BACA,IAAAsa,UAAA,CAAAsB,QAAA;4BACA9B,OAAA,CAAA5Y,oBAAA,GAAAoZ,UAAA,CAAAsB,QAAA;4BACA9B,OAAA,CAAA3Y,wBAAA,GAAAmZ,UAAA,CAAAsB,QAAA;0BACA;4BACA9B,OAAA,CAAA5Y,oBAAA;4BACA4Y,OAAA,CAAA3Y,wBAAA;0BACA;0BACA,IAAAmZ,UAAA,CAAAuB,OAAA;4BACA/B,OAAA,CAAA1Y,wBAAA,GAAAkZ,UAAA,CAAAuB,OAAA;4BACA/B,OAAA,CAAAzY,4BAAA,GAAAiZ,UAAA,CAAAuB,OAAA;0BACA;4BACA/B,OAAA,CAAA1Y,wBAAA;4BACA0Y,OAAA,CAAAzY,4BAAA;0BACA;0BACA,IAAAiZ,UAAA,CAAAwB,gBAAA;4BACAhC,OAAA,CAAAvY,iBAAA,GAAA+Y,UAAA,CAAAwB,gBAAA;0BACA;4BACAhC,OAAA,CAAAvY,iBAAA;0BACA;0BACA,IAAA+Y,UAAA,CAAA3Y,QAAA;4BACAmY,OAAA,CAAAnY,QAAA,GAAA2Y,UAAA,CAAA3Y,QAAA;0BACA;4BACAmY,OAAA,CAAAnY,QAAA;0BACA;0BACA,IAAA2Y,UAAA,CAAA5Y,QAAA;4BACAoY,OAAA,CAAApY,QAAA,GAAA4Y,UAAA,CAAA5Y,QAAA;0BACA;4BACAoY,OAAA,CAAApY,QAAA;0BACA;wBACA;wBACA;wBACAiB,oBAAA,GAAA2C,IAAA,CAAA3C,oBAAA;wBACA,IAAAA,oBAAA;0BACAmX,OAAA,CAAAnX,oBAAA,GAAA+F,IAAA,CAAAC,KAAA,CAAAhG,oBAAA;wBACA;0BACAmX,OAAA,CAAAnX,oBAAA;wBACA;wBACA,IAAA2C,IAAA,CAAAS,YAAA;0BACAwU,gBAAA,GAAAjV,IAAA,CAAAS,YAAA,CAAAgW,KAAA;0BACAvB,WAAA;0BAAAC,UAAA,OAAA5O,2BAAA,CAAA7M,OAAA,EACAub,gBAAA;0BAAA;4BAAA,KAAAE,UAAA,CAAA3O,CAAA,MAAA4O,MAAA,GAAAD,UAAA,CAAA1O,CAAA,IAAAC,IAAA;8BAAA2O,CAAA,GAAAD,MAAA,CAAAzW,KAAA;8BACAuW,WAAA,CAAAhO,IAAA,CAAAwP,QAAA,CAAArB,CAAA;4BACA;0BAAA,SAAAxO,GAAA;4BAAAsO,UAAA,CAAArO,CAAA,CAAAD,GAAA;0BAAA;4BAAAsO,UAAA,CAAApO,CAAA;0BAAA;0BACA/G,IAAA,CAAAS,YAAA,GAAAyU,WAAA;wBACA;0BACAlV,IAAA,CAAAS,YAAA;wBACA;wBACA,IAAAT,IAAA,CAAAO,OAAA;0BACA+U,WAAA,GAAAtV,IAAA,CAAAO,OAAA,CAAAkW,KAAA;0BACApK,KAAA;0BAAAkJ,UAAA,OAAAhP,2BAAA,CAAA7M,OAAA,EACA4b,WAAA;0BAAA;4BAAA,KAAAC,UAAA,CAAA/O,CAAA,MAAAgP,MAAA,GAAAD,UAAA,CAAA9O,CAAA,IAAAC,IAAA;8BAAA2O,EAAA,GAAAG,MAAA,CAAA7W,KAAA;8BACA0N,KAAA,CAAAnF,IAAA,CAAAwP,QAAA,CAAArB,EAAA;4BACA;0BAAA,SAAAxO,GAAA;4BAAA0O,UAAA,CAAAzO,CAAA,CAAAD,GAAA;0BAAA;4BAAA0O,UAAA,CAAAxO,CAAA;0BAAA;0BACA/G,IAAA,CAAAO,OAAA,GAAA8L,KAAA;wBACA;0BACArM,IAAA,CAAAO,OAAA;wBACA;wBACA,IAAAP,IAAA,CAAAQ,QAAA;0BACAkV,YAAA,GAAA1V,IAAA,CAAAQ,QAAA,CAAAiW,KAAA;0BACArK,MAAA;0BAAAuJ,UAAA,OAAApP,2BAAA,CAAA7M,OAAA,EACAgc,YAAA;0BAAA;4BAAA,KAAAC,UAAA,CAAAnP,CAAA,MAAAoP,MAAA,GAAAD,UAAA,CAAAlP,CAAA,IAAAC,IAAA;8BAAA2O,GAAA,GAAAO,MAAA,CAAAjX,KAAA;8BACAyN,MAAA,CAAAlF,IAAA,CAAAwP,QAAA,CAAArB,GAAA;4BACA;0BAAA,SAAAxO,GAAA;4BAAA8O,UAAA,CAAA7O,CAAA,CAAAD,GAAA;0BAAA;4BAAA8O,UAAA,CAAA5O,CAAA;0BAAA;0BACA/G,IAAA,CAAAQ,QAAA,GAAA4L,MAAA;wBACA;0BACApM,IAAA,CAAAQ,QAAA;wBACA;wBACA,IAAAR,IAAA,CAAA2N,IAAA;0BACA3N,IAAA,CAAA2N,IAAA,GAAA3N,IAAA,CAAA2N,IAAA,CAAA8I,KAAA;wBACA;0BACAzW,IAAA,CAAA2N,IAAA;wBACA;wBACA,IAAA3N,IAAA,CAAA4N,SAAA;0BACA5N,IAAA,CAAA4N,SAAA,GAAA5N,IAAA,CAAA4N,SAAA,CAAA6I,KAAA;wBACA;0BACAzW,IAAA,CAAA4N,SAAA;wBACA;wBACA,IAAA5N,IAAA,CAAA6N,IAAA;0BACA7N,IAAA,CAAA6N,IAAA,GAAA7N,IAAA,CAAA6N,IAAA,CAAA4I,KAAA;wBACA;0BACAzW,IAAA,CAAA6N,IAAA;wBACA;wBACA,IAAA7N,IAAA,CAAA8N,IAAA;0BACA9N,IAAA,CAAA8N,IAAA,GAAA9N,IAAA,CAAA8N,IAAA,CAAA2I,KAAA;wBACA;0BACAzW,IAAA,CAAA8N,IAAA;wBACA;wBACA,IAAA9N,IAAA,CAAA+N,IAAA;0BACA/N,IAAA,CAAA+N,IAAA,GAAA/N,IAAA,CAAA+N,IAAA,CAAA0I,KAAA;wBACA;0BACAzW,IAAA,CAAA+N,IAAA;wBACA;wBACA,IAAA/N,IAAA,CAAAU,IAAA;0BACAV,IAAA,CAAAU,IAAA,GAAAV,IAAA,CAAAU,IAAA,CAAA+V,KAAA;wBACA;0BACAzW,IAAA,CAAAU,IAAA;wBACA;wBACA,IAAAV,IAAA,CAAAuO,QAAA;0BACAvO,IAAA,CAAAuO,QAAA,GAAAvO,IAAA,CAAAuO,QAAA,CAAAkI,KAAA;wBACA;0BACAzW,IAAA,CAAAuO,QAAA;wBACA;wBACA,IAAAvO,IAAA,CAAAwO,iBAAA;0BACAxO,IAAA,CAAAwO,iBAAA,GAAAxO,IAAA,CAAAwO,iBAAA,CAAAiI,KAAA;wBACA;0BACAzW,IAAA,CAAAwO,iBAAA;wBACA;wBACA,IAAAxO,IAAA,CAAAyO,kBAAA;0BACAzO,IAAA,CAAAyO,kBAAA,GAAAzO,IAAA,CAAAyO,kBAAA,CAAAgI,KAAA;wBACA;0BACAzW,IAAA,CAAAyO,kBAAA;wBACA;wBACA,IAAAzO,IAAA,CAAA0O,cAAA;0BACA1O,IAAA,CAAA0O,cAAA,GAAA1O,IAAA,CAAA0O,cAAA,CAAA+H,KAAA;wBACA;0BACAzW,IAAA,CAAA0O,cAAA;wBACA;wBACA,IAAA1O,IAAA,CAAA+M,SAAA;0BACA/M,IAAA,CAAA+M,SAAA,GAAA3J,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAA+M,SAAA;wBACA;0BACA/M,IAAA,CAAA+M,SAAA;wBACA;wBACA,IAAA/M,IAAA,CAAAoN,eAAA;0BACApN,IAAA,CAAAoN,eAAA,GAAAhK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAoN,eAAA;wBACA;0BACApN,IAAA,CAAAoN,eAAA;wBACA;wBACA,IAAApN,IAAA,CAAAqN,UAAA;0BACArN,IAAA,CAAAqN,UAAA,GAAAjK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAqN,UAAA;wBACA;0BACArN,IAAA,CAAAqN,UAAA;wBACA;wBACA,IAAArN,IAAA,CAAAsN,iBAAA;0BACAtN,IAAA,CAAAsN,iBAAA,GAAAlK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAsN,iBAAA;wBACA;0BACAtN,IAAA,CAAAsN,iBAAA;wBACA;wBACA,IAAAtN,IAAA,CAAAuN,aAAA;0BACAvN,IAAA,CAAAuN,aAAA,GAAAnK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAuN,aAAA;wBACA;0BACAvN,IAAA,CAAAuN,aAAA;wBACA;wBACA,IAAAvN,IAAA,CAAAwN,cAAA;0BACAxN,IAAA,CAAAwN,cAAA,GAAApK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAwN,cAAA;wBACA;0BACAxN,IAAA,CAAAwN,cAAA;wBACA;wBACA,IAAAxN,IAAA,CAAAyN,QAAA;0BACAzN,IAAA,CAAAyN,QAAA,GAAArK,IAAA,CAAAC,KAAA,CAAArD,IAAA,CAAAyN,QAAA;wBACA;0BACAzN,IAAA,CAAAyN,QAAA;wBACA;wBACAnR,SAAA;wBACA,IAAA0D,IAAA,CAAA2W,OAAA;0BACAra,SAAA,GAAA0D,IAAA,CAAA2W,OAAA;wBACA;wBACAnC,OAAA,CAAAlY,SAAA,GAAAA,SAAA;wBAEAiB,eAAA;wBACA,IAAAyC,IAAA,CAAAzC,eAAA;0BACAA,eAAA,GAAAyC,IAAA,CAAAzC,eAAA;wBACA;wBACAiX,OAAA,CAAAjX,eAAA,GAAAA,eAAA;wBACA;wBACA4Q,YAAA,GAAAnO,IAAA,CAAAmO,YAAA;wBACA,IAAAA,YAAA;0BACAqG,OAAA,CAAA3K,UAAA,CAAAsE,YAAA;wBACA;0BACAqG,OAAA,CAAAjY,UAAA;wBACA;wBACAuZ,QAAA,GAAA9V,IAAA,CAAA8V,QAAA;wBACA,IAAAA,QAAA;0BACAtB,OAAA,CAAAtY,SAAA,GAAAkH,IAAA,CAAAC,KAAA,CAAAyS,QAAA;wBACA;0BACAtB,OAAA,CAAAtY,SAAA;wBACA;wBACAsY,OAAA,CAAAxU,IAAA,GAAAA,IAAA;wBACAqO,aAAA,GAAArO,IAAA,CAAAqO,aAAA;wBACA,IAAAA,aAAA;0BACA7R,QAAA,GAAA4G,IAAA,CAAAC,KAAA,CAAAgL,aAAA;0BACA0H,IAAA,GAAAvZ,QAAA,CAAAuZ,IAAA;0BACA,IAAAA,IAAA;4BACAvB,OAAA,CAAA/X,YAAA,GAAAsZ,IAAA,CAAA/D,GAAA,WAAA9Y,IAAA;8BAAA;gCAAAA,IAAA,EAAAA;8BAAA;4BAAA;4BAAA;0BACA;4BACAsb,OAAA,CAAAoC,qBAAA;4BACApC,OAAA,CAAA/X,YAAA;0BACA;0BACAuZ,IAAA,GAAAxZ,QAAA,CAAAwZ,IAAA;0BACA,IAAAD,IAAA;4BACAvB,OAAA,CAAA9X,YAAA,GAAAsZ,IAAA,CAAAhE,GAAA,WAAA9Y,IAAA;8BAAA;gCAAAA,IAAA,EAAAA;8BAAA;4BAAA;4BAAA;0BACA;4BACAsb,OAAA,CAAA9X,YAAA;0BACA;wBACA;0BACA8X,OAAA,CAAA/X,YAAA;0BACA+X,OAAA,CAAA9X,YAAA;0BACA8X,OAAA,CAAAoC,qBAAA;wBACA;wBACAtI,kBAAA,GAAAtO,IAAA,CAAAsO,kBAAA;wBACA,IAAAA,kBAAA;0BACA9R,SAAA,GAAA4G,IAAA,CAAAC,KAAA,CAAAiL,kBAAA;0BACAyH,KAAA,GAAAvZ,SAAA,CAAAuZ,IAAA;0BACA,IAAAA,KAAA;4BACAvB,OAAA,CAAA7X,iBAAA,GAAAoZ,KAAA,CAAA/D,GAAA,WAAA9Y,IAAA;8BAAA;gCAAAA,IAAA,EAAAA;8BAAA;4BAAA;4BAAA;0BACA;4BACAsb,OAAA,CAAA7X,iBAAA;0BACA;0BACAqZ,KAAA,GAAAxZ,SAAA,CAAAwZ,IAAA;0BACA,IAAAD,KAAA;4BACAvB,OAAA,CAAA5X,iBAAA,GAAAoZ,KAAA,CAAAhE,GAAA,WAAA9Y,IAAA;8BAAA;gCAAAA,IAAA,EAAAA;8BAAA;4BAAA;4BAAA;0BACA;4BACAsb,OAAA,CAAA5X,iBAAA;0BACA;wBACA;0BACA4X,OAAA,CAAA7X,iBAAA;0BACA6X,OAAA,CAAA5X,iBAAA;wBACA;wBACA4X,OAAA,CAAAlV,IAAA;wBACA,IAAAT,MAAA;0BACA2V,OAAA,CAAApV,KAAA;wBACA;0BACAoV,OAAA,CAAApV,KAAA;wBACA;wBACAoV,OAAA,CAAA3V,MAAA,GAAAA,MAAA;wBACA2V,OAAA,CAAAra,UAAA;sBAAA;sBAAA;wBAAA,OAAAkc,UAAA,CAAA1T,IAAA;oBAAA;kBAAA,GAAAoS,SAAA;gBAAA,CACA;gBAAA,iBAAA8B,EAAA;kBAAA,OAAA/B,KAAA,CAAAgC,KAAA,OAAAC,SAAA;gBAAA;cAAA;cAAApC,UAAA,CAAAtS,IAAA;cAAA,OACA,IAAA2U,sEAAA;gBAAAzY,EAAA,EAAAA;cAAA;YAAA;cAAA5C,uBAAA,GAAAgZ,UAAA,CAAAnS,IAAA;cACAgS,OAAA,CAAA7Y,uBAAA,GAAAA,uBAAA;cACA;cACA6Y,OAAA,CAAAyC,gCAAA,CAAA1Y,EAAA;cACA;cACAiW,OAAA,CAAA0C,mCAAA,CAAA3Y,EAAA;YAAA;YAAA;cAAA,OAAAoW,UAAA,CAAAhS,IAAA;UAAA;QAAA,GAAA8R,SAAA;MAAA;IACA;IACA0C,qBAAA,WAAAA,sBAAA/S,GAAA;MACA,IAAA0E,GAAA,QAAArH,sBAAA,CAAA2F,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA1I,KAAA,KAAAyF,GAAA,CAAA2M,eAAA;MAAA;MACA,IAAAjI,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAApK,KAAA;MACA;IACA;IACA0Y,QAAA,WAAAA,SAAAhT,GAAA;MACA,IAAA0E,GAAA,QAAAtH,aAAA,CAAA4F,MAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAA1I,KAAA,KAAAyF,GAAA,CAAAiT,MAAA;MAAA;MACA,IAAAvO,GAAA,IAAAA,GAAA;QACA,OAAAA,GAAA,IAAApK,KAAA;MACA;IACA;IACAuY,gCAAA,WAAAA,iCAAA1Y,EAAA;MAAA,IAAA+Y,OAAA;MAAA,WAAA1V,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAyV,UAAA;QAAA,IAAAvb,uBAAA;QAAA,WAAA6F,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAuV,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAArV,IAAA,GAAAqV,UAAA,CAAApV,IAAA;YAAA;cAAAoV,UAAA,CAAApV,IAAA;cAAA,OACA,IAAA4U,2DAAA;gBAAA1Y,EAAA,EAAAA;cAAA;YAAA;cAAAvC,uBAAA,GAAAyb,UAAA,CAAAjV,IAAA;cACA8U,OAAA,CAAAtb,uBAAA,GAAAA,uBAAA;YAAA;YAAA;cAAA,OAAAyb,UAAA,CAAA9U,IAAA;UAAA;QAAA,GAAA4U,SAAA;MAAA;IACA;IACAL,mCAAA,WAAAA,oCAAA3Y,EAAA;MAAA,IAAAmZ,OAAA;MAAA,WAAA9V,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA6V,UAAA;QAAA,IAAAC,mBAAA,EAAA1Z,yBAAA,EAAAD,iBAAA;QAAA,WAAA4D,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA4V,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1V,IAAA,GAAA0V,UAAA,CAAAzV,IAAA;YAAA;cAAAyV,UAAA,CAAAzV,IAAA;cAAA,OACA,IAAA6U,8DAAA;gBAAA3Y,EAAA,EAAAA;cAAA;YAAA;cAAAqZ,mBAAA,GAAAE,UAAA,CAAAtV,IAAA;cACAtE,yBAAA,GAAA0Z,mBAAA,CAAA1Z,yBAAA;cACAD,iBAAA,GAAA2Z,mBAAA,CAAA3Z,iBAAA;cACAyZ,OAAA,CAAAzZ,iBAAA,GAAAA,iBAAA;cACAyZ,OAAA,CAAAxZ,yBAAA,GAAAA,yBAAA;YAAA;YAAA;cAAA,OAAA4Z,UAAA,CAAAnV,IAAA;UAAA;QAAA,GAAAgV,SAAA;MAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAApW,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAmW,UAAA;QAAA,WAAApW,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAiW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA/V,IAAA,GAAA+V,UAAA,CAAA9V,IAAA;YAAA;cAAA8V,UAAA,CAAA9V,IAAA;cAAA,OACA2V,OAAA,CAAAtE,QAAA;YAAA;cACAsE,OAAA,CAAArb,iBAAA,GAAAyG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyP,SAAA,CAAAmF,OAAA,CAAAvb,YAAA;cACAub,OAAA,CAAApb,iBAAA,GAAAwG,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAyP,SAAA,CAAAmF,OAAA,CAAAtb,YAAA;YAAA;YAAA;cAAA,OAAAyb,UAAA,CAAAxV,IAAA;UAAA;QAAA,GAAAsV,SAAA;MAAA;IACA;IACAG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzW,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAwW,UAAA;QAAA,IAAA7H,YAAA,EAAAC,wBAAA,EAAAnS,EAAA,EAAAsN,MAAA,EAAAxO,oBAAA,EAAA0F,MAAA,EAAAyB,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAsW,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApW,IAAA,GAAAoW,UAAA,CAAAnW,IAAA;YAAA;cACAgW,OAAA,CAAAle,UAAA;cACAsW,YAAA,GAAA4H,OAAA,CAAArY,IAAA,CAAAyQ,YAAA;cACAC,wBAAA,GAAA2H,OAAA,CAAArY,IAAA,CAAA0Q,wBAAA;cACAnS,EAAA,GAAA8Z,OAAA,CAAArY,IAAA,CAAAzB,EAAA;cACAsN,MAAA,GAAAwM,OAAA,CAAArY,IAAA,CAAA6L,MAAA;cACAxO,oBAAA,GAAAgb,OAAA,CAAAhb,oBAAA;cACAA,oBAAA,GAAA+F,IAAA,CAAAyP,SAAA,CAAAxV,oBAAA;cACA0F,MAAA;gBACAxE,EAAA,EAAAA,EAAA;gBAAAkS,YAAA,EAAAA,YAAA;gBAAApT,oBAAA,EAAAA,oBAAA;gBAAAwO,MAAA,EAAAA,MAAA;gBAAA6E,wBAAA,EAAAA;cACA;cAAA8H,UAAA,CAAApW,IAAA;cAAAoW,UAAA,CAAAnW,IAAA;cAAA,OAEA,IAAAoW,6DAAA,EAAA1V,MAAA;YAAA;cAAAyB,GAAA,GAAAgU,UAAA,CAAAhW,IAAA;cACA6V,OAAA,CAAAvF,UAAA;cACAuF,OAAA,CAAAle,UAAA;cAAAqe,UAAA,CAAAnW,IAAA;cAAA;YAAA;cAAAmW,UAAA,CAAApW,IAAA;cAAAoW,UAAA,CAAA7F,EAAA,GAAA6F,UAAA;cAEAH,OAAA,CAAAle,UAAA;YAAA;YAAA;cAAA,OAAAqe,UAAA,CAAA7V,IAAA;UAAA;QAAA,GAAA2V,SAAA;MAAA;IAEA;IACAI,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/W,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA8W,UAAA;QAAA,IAAA5Y,IAAA,EAAA6Y,KAAA,EAAArU,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA6W,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3W,IAAA,GAAA2W,UAAA,CAAA1W,IAAA;YAAA;cACAsW,OAAA,CAAAxe,UAAA;cACA6F,IAAA,GAAA2Y,OAAA,CAAA3Y,IAAA;cACA6Y,KAAA;cACAA,KAAA,CAAAta,EAAA,GAAAyB,IAAA,CAAAzB,EAAA;cACA,IAAAyB,IAAA,CAAA+M,SAAA;gBACA8L,KAAA,CAAA9L,SAAA,GAAA3J,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAA+M,SAAA;cACA;gBACA8L,KAAA,CAAA9L,SAAA;cACA;cACA,IAAA/M,IAAA,CAAAoN,eAAA;gBACAyL,KAAA,CAAAzL,eAAA,GAAAhK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAoN,eAAA;cACA;gBACAyL,KAAA,CAAAzL,eAAA;cACA;cACA,IAAApN,IAAA,CAAAqN,UAAA;gBACAwL,KAAA,CAAAxL,UAAA,GAAAjK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAqN,UAAA;cACA;gBACAwL,KAAA,CAAAxL,UAAA;cACA;cACA,IAAArN,IAAA,CAAAsN,iBAAA;gBACAuL,KAAA,CAAAvL,iBAAA,GAAAlK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAsN,iBAAA;cACA;gBACAuL,KAAA,CAAAvL,iBAAA;cACA;cACA,IAAAtN,IAAA,CAAAuN,aAAA;gBACAsL,KAAA,CAAAtL,aAAA,GAAAnK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAuN,aAAA;cACA;gBACAsL,KAAA,CAAAtL,aAAA;cACA;cACA,IAAAvN,IAAA,CAAAwN,cAAA;gBACAqL,KAAA,CAAArL,cAAA,GAAApK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAwN,cAAA;cACA;gBACAqL,KAAA,CAAArL,cAAA;cACA;cACA,IAAAxN,IAAA,CAAAyN,QAAA;gBACAoL,KAAA,CAAApL,QAAA,GAAArK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAyN,QAAA;cACA;gBACAoL,KAAA,CAAApL,QAAA;cACA;cACAoL,KAAA,CAAAvH,iBAAA,GAAAtR,IAAA,CAAAsR,iBAAA;cACAuH,KAAA,CAAAtH,iBAAA,GAAAvR,IAAA,CAAAuR,iBAAA;cACAsH,KAAA,CAAAzH,mBAAA,GAAApR,IAAA,CAAAoR,mBAAA;cACAyH,KAAA,CAAAxH,mBAAA,GAAArR,IAAA,CAAAqR,mBAAA;cACAwH,KAAA,CAAA3H,eAAA,GAAAlR,IAAA,CAAAkR,eAAA;cACA2H,KAAA,CAAA1H,eAAA,GAAAnR,IAAA,CAAAmR,eAAA;cAAA4H,UAAA,CAAA3W,IAAA;cAAA2W,UAAA,CAAA1W,IAAA;cAAA,OAEA,IAAA2W,iEAAA,EAAAH,KAAA;YAAA;cAAArU,GAAA,GAAAuU,UAAA,CAAAvW,IAAA;cACAmW,OAAA,CAAA7F,UAAA;cACA6F,OAAA,CAAAxe,UAAA;cAAA4e,UAAA,CAAA1W,IAAA;cAAA;YAAA;cAAA0W,UAAA,CAAA3W,IAAA;cAAA2W,UAAA,CAAApG,EAAA,GAAAoG,UAAA;cAEAJ,OAAA,CAAAxe,UAAA;YAAA;YAAA;cAAA,OAAA4e,UAAA,CAAApW,IAAA;UAAA;QAAA,GAAAiW,SAAA;MAAA;IAEA;IACAK,UAAA,WAAAA,WAAAvL,OAAA;MAAA,IAAAwL,OAAA;MAAA,WAAAtX,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAqX,UAAA;QAAA,IAAAnZ,IAAA,EAAAE,SAAA,EAAAO,YAAA,EAAApD,oBAAA,EAAA+b,UAAA,EAAAC,MAAA,EAAA7T,IAAA,EAAAlB,aAAA,EAAAgV,UAAA,EAAAC,IAAA,EAAA1N,MAAA,EAAA2N,YAAA,EAAAlO,YAAA,EAAAzG,WAAA,EAAA4U,GAAA,EAAAC,SAAA,EAAAC,GAAA,EAAAC,UAAA;QAAA,WAAA/X,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA4X,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1X,IAAA,GAAA0X,UAAA,CAAAzX,IAAA;YAAA;cAAA,MACAqL,OAAA;gBAAAoM,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAAyX,UAAA,CAAAzX,IAAA;cAAA,OACA6W,OAAA,CAAAa,KAAA,SAAAC,QAAA;YAAA;cAEAha,IAAA,GAAA0K,MAAA,CAAAC,MAAA,KAAAuO,OAAA,CAAAlZ,IAAA;cACAE,SAAA,GAAAF,IAAA,CAAAE,SAAA;cAAA,MACA,CAAAA,SAAA,IAAAA,SAAA,CAAAwR,MAAA;gBAAAoI,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAAzG,QAAA;cAAA,OAAAqH,UAAA,CAAApH,MAAA;YAAA;cAGAjS,YAAA,GAAAT,IAAA,CAAAS,YAAA;cAAA,MACA,CAAAA,YAAA,IAAAA,YAAA,CAAAiR,MAAA;gBAAAoI,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAAzG,QAAA;cAAA,OAAAqH,UAAA,CAAApH,MAAA;YAAA;cAGA1S,IAAA,CAAA0N,OAAA,GAAAA,OAAA;cACA,IAAA1N,IAAA,CAAAS,YAAA,IAAAT,IAAA,CAAAS,YAAA,CAAAiR,MAAA,YAAAuI,iBAAA,EAAAja,IAAA,CAAAS,YAAA;gBACAT,IAAA,CAAAS,YAAA,GAAAT,IAAA,CAAAS,YAAA,CAAAyZ,IAAA;cACA;gBACAla,IAAA,CAAAS,YAAA;cACA;cACA,IAAAT,IAAA,CAAAO,OAAA,IAAAP,IAAA,CAAAO,OAAA,CAAAmR,MAAA,YAAAuI,iBAAA,EAAAja,IAAA,CAAAO,OAAA;gBACAP,IAAA,CAAAO,OAAA,GAAAP,IAAA,CAAAO,OAAA,CAAA2Z,IAAA;cACA;gBACAla,IAAA,CAAAO,OAAA;cACA;cACA,IAAAP,IAAA,CAAAQ,QAAA,IAAAR,IAAA,CAAAQ,QAAA,CAAAkR,MAAA,YAAAuI,iBAAA,EAAAja,IAAA,CAAAQ,QAAA;gBACAR,IAAA,CAAAQ,QAAA,GAAAR,IAAA,CAAAQ,QAAA,CAAA0Z,IAAA;cACA;gBACAla,IAAA,CAAAQ,QAAA;cACA;cACA,IAAAR,IAAA,CAAA2N,IAAA;gBACA3N,IAAA,CAAA2N,IAAA,GAAA3N,IAAA,CAAA2N,IAAA,CAAAuM,IAAA;cACA;gBACAla,IAAA,CAAA2N,IAAA;cACA;cACA,IAAA3N,IAAA,CAAA4N,SAAA;gBACA5N,IAAA,CAAA4N,SAAA,GAAA5N,IAAA,CAAA4N,SAAA,CAAAsM,IAAA;cACA;gBACAla,IAAA,CAAA4N,SAAA;cACA;cACA,IAAA5N,IAAA,CAAA6N,IAAA;gBACA7N,IAAA,CAAA6N,IAAA,GAAA7N,IAAA,CAAA6N,IAAA,CAAAqM,IAAA;cACA;gBACAla,IAAA,CAAA6N,IAAA;cACA;cACA,IAAA7N,IAAA,CAAA8N,IAAA;gBACA9N,IAAA,CAAA8N,IAAA,GAAA9N,IAAA,CAAA8N,IAAA,CAAAoM,IAAA;cACA;gBACAla,IAAA,CAAA8N,IAAA;cACA;cACA,IAAA9N,IAAA,CAAA+N,IAAA;gBACA/N,IAAA,CAAA+N,IAAA,GAAA/N,IAAA,CAAA+N,IAAA,CAAAmM,IAAA;cACA;gBACAla,IAAA,CAAA+N,IAAA;cACA;cACA,IAAA/N,IAAA,CAAAU,IAAA;gBACAV,IAAA,CAAAU,IAAA,GAAAV,IAAA,CAAAU,IAAA,CAAAwZ,IAAA;cACA;gBACAla,IAAA,CAAAU,IAAA;cACA;cACA,IAAAV,IAAA,CAAAuO,QAAA;gBACAvO,IAAA,CAAAuO,QAAA,GAAAvO,IAAA,CAAAuO,QAAA,CAAA2L,IAAA;cACA;gBACAla,IAAA,CAAAuO,QAAA;cACA;cACA,IAAAvO,IAAA,CAAAwO,iBAAA;gBACAxO,IAAA,CAAAwO,iBAAA,GAAAxO,IAAA,CAAAwO,iBAAA,CAAA0L,IAAA;cACA;gBACAla,IAAA,CAAAwO,iBAAA;cACA;cACA,IAAAxO,IAAA,CAAAyO,kBAAA;gBACAzO,IAAA,CAAAyO,kBAAA,GAAAzO,IAAA,CAAAyO,kBAAA,CAAAyL,IAAA;cACA;gBACAla,IAAA,CAAAyO,kBAAA;cACA;cACA,IAAAzO,IAAA,CAAA0O,cAAA;gBACA1O,IAAA,CAAA0O,cAAA,GAAA1O,IAAA,CAAA0O,cAAA,CAAAwL,IAAA;cACA;gBACAla,IAAA,CAAA0O,cAAA;cACA;cACA,IAAA1O,IAAA,CAAA+M,SAAA;gBACA/M,IAAA,CAAA+M,SAAA,GAAA3J,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAA+M,SAAA;cACA;gBACA/M,IAAA,CAAA+M,SAAA;cACA;cACA,IAAA/M,IAAA,CAAAoN,eAAA;gBACApN,IAAA,CAAAoN,eAAA,GAAAhK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAoN,eAAA;cACA;gBACApN,IAAA,CAAAoN,eAAA;cACA;cACA,IAAApN,IAAA,CAAAqN,UAAA;gBACArN,IAAA,CAAAqN,UAAA,GAAAjK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAqN,UAAA;cACA;gBACArN,IAAA,CAAAqN,UAAA;cACA;cACA,IAAArN,IAAA,CAAAsN,iBAAA;gBACAtN,IAAA,CAAAsN,iBAAA,GAAAlK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAsN,iBAAA;cACA;gBACAtN,IAAA,CAAAsN,iBAAA;cACA;cACA,IAAAtN,IAAA,CAAAuN,aAAA;gBACAvN,IAAA,CAAAuN,aAAA,GAAAnK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAuN,aAAA;cACA;gBACAvN,IAAA,CAAAuN,aAAA;cACA;cACA,IAAAvN,IAAA,CAAAwN,cAAA;gBACAxN,IAAA,CAAAwN,cAAA,GAAApK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAwN,cAAA;cACA;gBACAxN,IAAA,CAAAwN,cAAA;cACA;cACA,IAAAxN,IAAA,CAAAyN,QAAA;gBACAzN,IAAA,CAAAyN,QAAA,GAAArK,IAAA,CAAAyP,SAAA,CAAA7S,IAAA,CAAAyN,QAAA;cACA;gBACAzN,IAAA,CAAAyN,QAAA;cACA;cACA,IAAAzN,IAAA,CAAA2O,WAAA;gBACA3O,IAAA,CAAA2O,WAAA;cACA;gBACA3O,IAAA,CAAA2O,WAAA;cACA;cACA,IAAA3O,IAAA,CAAA+O,WAAA;gBACA/O,IAAA,CAAA+O,WAAA;cACA;gBACA/O,IAAA,CAAA+O,WAAA;cACA;cACA1R,oBAAA,GAAA6b,OAAA,CAAA7b,oBAAA;cAAA,MACAA,oBAAA,IAAAA,oBAAA,CAAAqU,MAAA;gBAAAoI,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAA+W,UAAA,OAAA7S,2BAAA,CAAA7M,OAAA,EACA2D,oBAAA;cAAAyc,UAAA,CAAA1X,IAAA;cAAAgX,UAAA,CAAA5S,CAAA;YAAA;cAAA,KAAA6S,MAAA,GAAAD,UAAA,CAAA3S,CAAA,IAAAC,IAAA;gBAAAoT,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAAmD,IAAA,GAAA6T,MAAA,CAAA1a,KAAA;cACA2F,aAAA,GAAAkB,IAAA,CAAAlB,aAAA;cACAgV,UAAA,GAAA9T,IAAA,CAAA8T,UAAA;cACAC,IAAA,GAAA/T,IAAA,CAAA+T,IAAA;cACA1N,MAAA,GAAArG,IAAA,CAAAqG,MAAA;cACA2N,YAAA,GAAAhU,IAAA,CAAAgU,YAAA;cACAlO,YAAA,GAAA9F,IAAA,CAAA8F,YAAA;cACAzG,WAAA,GAAAW,IAAA,CAAAX,WAAA;cAAA,MACAP,aAAA,cAAAO,WAAA;gBAAAiV,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAAzG,QAAA;cAAA,OAAAqH,UAAA,CAAApH,MAAA;YAAA;cAAA,MAGA4G,UAAA,UAAAzN,MAAA;gBAAAiO,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAAzG,QAAA,cAAAnH,YAAA,oBAAAkO,YAAA;cAAA,OAAAM,UAAA,CAAApH,MAAA;YAAA;cAAA,MAGA6G,IAAA;gBAAAO,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACAoX,GAAA,GAAAnO,YAAA;cAAAwO,UAAA,CAAAzX,IAAA;cAAA,OACA6W,OAAA,CAAAxF,QAAA,CAAA+F,GAAA;gBACA9F,iBAAA;gBACAC,gBAAA;gBACApa,IAAA;cACA;YAAA;cAAAsgB,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAAnH,EAAA,GAAAmH,UAAA;cAAAV,UAAA,CAAAtS,CAAA,CAAAgT,UAAA,CAAAnH,EAAA;YAAA;cAAAmH,UAAA,CAAA1X,IAAA;cAAAgX,UAAA,CAAArS,CAAA;cAAA,OAAA+S,UAAA,CAAAtS,MAAA;YAAA;cAGAxH,IAAA,CAAA3C,oBAAA,GAAA+F,IAAA,CAAAyP,SAAA,CAAAxV,oBAAA;cACAqc,SAAA,GAAAR,OAAA,CAAAiB,QAAA,CAAA9c,oBAAA;cACAsc,GAAA,GAAAD,SAAA,CAAAC,GAAA;cAAA,MACAA,GAAA;gBAAAG,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACAuX,UAAA,GAAAF,SAAA,CAAAE,UAAA;cAAAE,UAAA,CAAAzX,IAAA;cAAA,OACA6W,OAAA,CAAAxF,QAAA,YAAAkG,UAAA;YAAA;cAAAE,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAAzX,IAAA;cAAA,OAGA6W,OAAA,CAAAxF,QAAA;YAAA;cACA1T,IAAA,CAAA3C,oBAAA;YAAA;cAAA,MAEA,CAAA2C,IAAA,CAAAU,IAAA,IAAAgN,OAAA;gBAAAoM,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cACA6W,OAAA,CAAAzG,QAAA;cAAA,OAAAqH,UAAA,CAAApH,MAAA;YAAA;cAAA,MAGA1S,IAAA,CAAAzB,EAAA;gBAAAub,UAAA,CAAAzX,IAAA;gBAAA;cAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAEA8W,OAAA,CAAA/e,UAAA;cAAA2f,UAAA,CAAAzX,IAAA;cAAA,OACA,IAAA+X,0DAAA,EAAApa,IAAA;YAAA;cACAkZ,OAAA,CAAA/e,UAAA;cACA+e,OAAA,CAAAlZ,IAAA,CAAAqa,cAAA,GAAAC,UAAA,CAAApB,OAAA,CAAAlZ,IAAA,CAAAqa,cAAA;cACAnB,OAAA,CAAApG,UAAA;cACA;cAAAgH,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAAvS,EAAA,GAAAuS,UAAA;cAEAZ,OAAA,CAAA/e,UAAA;YAAA;cAAA2f,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAIA8W,OAAA,CAAA/e,UAAA;cAAA2f,UAAA,CAAAzX,IAAA;cAAA,OACA,IAAAkY,uDAAA,EAAAva,IAAA;YAAA;cACAkZ,OAAA,CAAA/e,UAAA;cACA+e,OAAA,CAAApG,UAAA;cACAoG,OAAA,CAAAsB,KAAA;cAAAV,UAAA,CAAAzX,IAAA;cAAA;YAAA;cAAAyX,UAAA,CAAA1X,IAAA;cAAA0X,UAAA,CAAAW,EAAA,GAAAX,UAAA;cAEAZ,OAAA,CAAA/e,UAAA;YAAA;YAAA;cAAA,OAAA2f,UAAA,CAAAnX,IAAA;UAAA;QAAA,GAAAwW,SAAA;MAAA;IAGA;IACAqB,KAAA,WAAAA,MAAA;MAAA,IAAAE,OAAA;MACA,KAAAC,MAAA,CAAAC,QAAA,0BAAA5X,MAAA;MACA,IAAA6X,IAAA;QACAC,QAAA;QACA5hB,IAAA;QACA6hB,IAAA;QACA3b,KAAA;MACA;MACA,KAAAub,MAAA,CAAAC,QAAA,2BAAAC,IAAA,EAAApY,IAAA;QACA,IAAAqY,QAAA,GAAAD,IAAA,CAAAC,QAAA;QACAJ,OAAA,CAAAM,SAAA;UACAN,OAAA,CAAAO,OAAA,CAAAC,OAAA;YACAH,IAAA,gBAAAD;UACA;QACA;MACA;IACA;IACA;IACAX,QAAA,WAAAA,SAAApU,KAAA;MACA,IAAA2T,SAAA;QAAAC,GAAA;QAAAC,UAAA;MAAA;MACA,IAAAuB,cAAA,OAAA9U,GAAA;MACA,IAAAN,KAAA,IAAAA,KAAA,CAAA2L,MAAA;QACA,IAAA0J,KAAA;QAAA,IAAAC,UAAA,OAAA9U,2BAAA,CAAA7M,OAAA,EACAqM,KAAA;UAAAuV,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAA7U,CAAA,MAAA8U,MAAA,GAAAD,UAAA,CAAA5U,CAAA,IAAAC,IAAA;YAAA,IAAAlB,KAAA,GAAA8V,MAAA,CAAA3c,KAAA;YACAyc,KAAA,CAAAlU,IAAA,CAAA1B,KAAA,CAAA8F,YAAA;UACA;QAAA,SAAAzE,GAAA;UAAAwU,UAAA,CAAAvU,CAAA,CAAAD,GAAA;QAAA;UAAAwU,UAAA,CAAAtU,CAAA;QAAA;QACA,SAAAwU,EAAA,MAAAC,MAAA,GAAAJ,KAAA,EAAAG,EAAA,GAAAC,MAAA,CAAA9J,MAAA,EAAA6J,EAAA;UAAA,IAAAlc,IAAA,GAAAmc,MAAA,CAAAD,EAAA;UACA,IAAAE,KAAA;UAAA,IAAAC,UAAA,OAAAnV,2BAAA,CAAA7M,OAAA,EACAqM,KAAA;YAAA4V,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAAlV,CAAA,MAAAmV,MAAA,GAAAD,UAAA,CAAAjV,CAAA,IAAAC,IAAA;cAAA,IAAAlB,IAAA,GAAAmW,MAAA,CAAAhd,KAAA;cACA,IAAA2M,YAAA,GAAA9F,IAAA,CAAA8F,YAAA;cACA,IAAAjM,IAAA,KAAAiM,YAAA;gBACAmQ,KAAA;cACA;YACA;UAAA,SAAA5U,GAAA;YAAA6U,UAAA,CAAA5U,CAAA,CAAAD,GAAA;UAAA;YAAA6U,UAAA,CAAA3U,CAAA;UAAA;UACA,IAAA0U,KAAA;YACAN,cAAA,CAAAxU,GAAA,CAAAtH,IAAA;UACA;QACA;MACA;MACA,IAAA8b,cAAA,IAAAA,cAAA,CAAAS,IAAA;QACA,IAAAC,GAAA,GAAAzY,IAAA,CAAAyP,SAAA,CAAAiJ,KAAA,CAAAC,IAAA,CAAAZ,cAAA;QACAzB,SAAA;UAAAC,GAAA;UAAAC,UAAA,EAAAiC;QAAA;MACA;MACA,OAAAnC,SAAA;IACA;IACAsC,YAAA,WAAAA,aAAA5X,GAAA;MAAA,IAAA6X,OAAA;MACA,IAAAlhB,GAAA,GAAAqJ,GAAA,CAAA7F,EAAA,SAAAxD,GAAA;MACA,KAAA2Y,QAAA,oBAAA3Y,GAAA;QACA4Y,iBAAA;QACAC,gBAAA;QACApa,IAAA;MACA,GAAAiJ,IAAA;QACA,WAAAyZ,uDAAA,EAAAnhB,GAAA;MACA,GAAA0H,IAAA;QACAwZ,OAAA,CAAA5R,OAAA;QACA4R,OAAA,CAAAnJ,UAAA;MACA,GAAAmB,KAAA;IACA;IACAkI,YAAA,WAAAA,aAAA;MAAA,IAAAC,OAAA;MACA,IAAA3c,WAAA,QAAAA,WAAA;MACA,KAAAiU,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACApa,IAAA;MACA,GAAAiJ,IAAA;QACA2Z,OAAA,CAAA/hB,aAAA;QACA,WAAAgiB,0DAAA,EAAA5c,WAAA;MACA,GAAAgD,IAAA,WAAAC,QAAA;QACA0Z,OAAA,CAAAE,QAAA,CAAA5Z,QAAA,CAAA+W,GAAA;QACA2C,OAAA,CAAA/hB,aAAA;MACA,GAAA4Z,KAAA;IACA;IACAsI,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5a,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA2a,UAAA;QAAA,IAAAnR,YAAA,EAAAjO,oBAAA,EAAAmH,GAAA,EAAA8U,UAAA,EAAAoD,QAAA;QAAA,WAAA7a,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA0a,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAxa,IAAA,GAAAwa,UAAA,CAAAva,IAAA;YAAA;cACAiJ,YAAA,GAAAkR,OAAA,CAAAxc,IAAA,CAAAsL,YAAA;cACAjO,oBAAA,GAAAmf,OAAA,CAAAnf,oBAAA;cAAA,KACAiO,YAAA;gBAAAsR,UAAA,CAAAva,IAAA;gBAAA;cAAA;cAAAua,UAAA,CAAAva,IAAA;cAAA,OACA,IAAAwa,0CAAA;gBAAAvR,YAAA,EAAAA;cAAA;YAAA;cAAA9G,GAAA,GAAAoY,UAAA,CAAApa,IAAA;cACA,IAAAgC,GAAA,CAAA7K,IAAA;gBACA2f,UAAA,GAAA9U,GAAA,CAAA7K,IAAA,CAAA2f,UAAA;gBACA,IAAAA,UAAA;kBACAoD,QAAA,GAAAlY,GAAA,CAAA7K,IAAA,CAAA+iB,QAAA;kBACAF,OAAA,CAAAM,OAAA,CAAAJ,QAAA;gBACA;gBACArf,oBAAA,CAAA0f,OAAA,CAAAvY,GAAA,CAAA7K,IAAA;cACA;cACA6iB,OAAA,CAAAnf,oBAAA,GAAAA,oBAAA;cACAmf,OAAA,CAAAQ,UAAA;cAAAJ,UAAA,CAAAva,IAAA;cAAA;YAAA;cAEAma,OAAA,CAAA/J,QAAA;YAAA;YAAA;cAAA,OAAAmK,UAAA,CAAAja,IAAA;UAAA;QAAA,GAAA8Z,SAAA;MAAA;IAEA;IACAQ,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAtb,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAqb,UAAA;QAAA,IAAA5R,iBAAA,EAAAlO,oBAAA,EAAAmH,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAmb,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjb,IAAA,GAAAib,UAAA,CAAAhb,IAAA;YAAA;cACAkJ,iBAAA,GAAA2R,OAAA,CAAAld,IAAA,CAAAuL,iBAAA;cACAlO,oBAAA,GAAA6f,OAAA,CAAA7f,oBAAA;cAAA,KACAkO,iBAAA;gBAAA8R,UAAA,CAAAhb,IAAA;gBAAA;cAAA;cAAAgb,UAAA,CAAAhb,IAAA;cAAA,OACA,IAAAib,sCAAA;gBAAAnR,WAAA,EAAAZ;cAAA;YAAA;cAAA/G,GAAA,GAAA6Y,UAAA,CAAA7a,IAAA;cACA,IAAAgC,GAAA,CAAA7K,IAAA;gBACA0D,oBAAA,CAAA0f,OAAA,CAAAvY,GAAA,CAAA7K,IAAA;cACA;cACAujB,OAAA,CAAA7f,oBAAA,GAAAA,oBAAA;cAAAggB,UAAA,CAAAhb,IAAA;cAAA;YAAA;cAEA6a,OAAA,CAAAzK,QAAA;YAAA;YAAA;cAAA,OAAA4K,UAAA,CAAA1a,IAAA;UAAA;QAAA,GAAAwa,SAAA;MAAA;IAEA;IACAI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5b,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA2b,UAAA;QAAA,IAAAlS,iBAAA,EAAAlO,oBAAA,EAAAmH,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAyb,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvb,IAAA,GAAAub,UAAA,CAAAtb,IAAA;YAAA;cACAkJ,iBAAA,GAAAiS,OAAA,CAAAxd,IAAA,CAAAuL,iBAAA;cACAlO,oBAAA,GAAAmgB,OAAA,CAAAngB,oBAAA;cAAA,KACAkO,iBAAA;gBAAAoS,UAAA,CAAAtb,IAAA;gBAAA;cAAA;cAAAsb,UAAA,CAAAtb,IAAA;cAAA,OACA,IAAAub,oDAAA;gBAAAhe,cAAA,EAAA2L;cAAA;YAAA;cAAA/G,GAAA,GAAAmZ,UAAA,CAAAnb,IAAA;cACA,IAAAgC,GAAA,CAAA7K,IAAA;gBACA0D,oBAAA,CAAA6J,IAAA,CAAA1C,GAAA,CAAA7K,IAAA;cACA;cACA6jB,OAAA,CAAAngB,oBAAA,GAAAA,oBAAA;cAAAsgB,UAAA,CAAAtb,IAAA;cAAA;YAAA;cAEAmb,OAAA,CAAA/K,QAAA;YAAA;YAAA;cAAA,OAAAkL,UAAA,CAAAhb,IAAA;UAAA;QAAA,GAAA8a,SAAA;MAAA;IAEA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlc,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAic,UAAA;QAAA,IAAAlhB,OAAA,EAAAmD,IAAA,EAAA9D,SAAA,EAAAsI,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA+b,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA7b,IAAA,GAAA6b,UAAA,CAAA5b,IAAA;YAAA;cACAxF,OAAA;cACAmD,IAAA,GAAA8d,OAAA,CAAA9d,IAAA;cACA9D,SAAA,GAAA4hB,OAAA,CAAA5hB,SAAA;cAAA,IACA8D,IAAA,CAAAmO,YAAA;gBAAA8P,UAAA,CAAA5b,IAAA;gBAAA;cAAA;cACAyb,OAAA,CAAArL,QAAA;cAAA,OAAAwL,UAAA,CAAAvL,MAAA;YAAA;cAAA,IAGAxW,SAAA,IAAAA,SAAA,CAAAwV,MAAA;gBAAAuM,UAAA,CAAA5b,IAAA;gBAAA;cAAA;cACAyb,OAAA,CAAArL,QAAA;cAAA,OAAAwL,UAAA,CAAAvL,MAAA;YAAA;cAGA7V,OAAA,CAAAsR,YAAA,GAAAnO,IAAA,CAAAmO,YAAA;cACAtR,OAAA,CAAAsN,UAAA,GAAAnK,IAAA,CAAAmK,UAAA;cACAtN,OAAA,CAAAsO,iBAAA,GAAA2S,OAAA,CAAA9d,IAAA,CAAAmL,iBAAA;cACAtO,OAAA,CAAAqhB,SAAA,GAAAle,IAAA,CAAAzB,EAAA;cACA1B,OAAA,CAAAX,SAAA,GAAAA,SAAA;cACAW,OAAA,CAAA6O,MAAA,GAAA1L,IAAA,CAAA0L,MAAA;cACA7O,OAAA,CAAAsP,WAAA,GAAAnM,IAAA,CAAAmM,WAAA;cACAtP,OAAA,CAAA+C,cAAA,GAAAI,IAAA,CAAAJ,cAAA;cACA/C,OAAA,CAAAyD,WAAA,GAAAN,IAAA,CAAAM,WAAA;cACAwd,OAAA,CAAA3jB,UAAA;cAAA8jB,UAAA,CAAA7b,IAAA;cAAA6b,UAAA,CAAA5b,IAAA;cAAA,OAEA,IAAA8b,+DAAA;gBAAAthB,OAAA,EAAAuG,IAAA,CAAAyP,SAAA,CAAAhW,OAAA;cAAA;YAAA;cAAA2H,GAAA,GAAAyZ,UAAA,CAAAzb,IAAA;cACAsb,OAAA,CAAAhL,UAAA;cACAgL,OAAA,CAAA3jB,UAAA;cAAA8jB,UAAA,CAAA5b,IAAA;cAAA;YAAA;cAAA4b,UAAA,CAAA7b,IAAA;cAAA6b,UAAA,CAAAtL,EAAA,GAAAsL,UAAA;cAEAH,OAAA,CAAA3jB,UAAA;YAAA;YAAA;cAAA,OAAA8jB,UAAA,CAAAtb,IAAA;UAAA;QAAA,GAAAob,SAAA;MAAA;IAEA;IACAK,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzc,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAwc,UAAA;QAAA,IAAAzhB,OAAA,EAAAmD,IAAA,EAAA9D,SAAA,EAAAsI,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAsc,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApc,IAAA,GAAAoc,UAAA,CAAAnc,IAAA;YAAA;cACAxF,OAAA;cACAmD,IAAA,GAAAqe,OAAA,CAAAre,IAAA;cACA9D,SAAA,GAAAmiB,OAAA,CAAAliB,aAAA;cAAA,IACA6D,IAAA,CAAAxG,IAAA;gBAAAglB,UAAA,CAAAnc,IAAA;gBAAA;cAAA;cACAgc,OAAA,CAAA5L,QAAA;cAAA,OAAA+L,UAAA,CAAA9L,MAAA;YAAA;cAGA7V,OAAA,CAAAqhB,SAAA,GAAAle,IAAA,CAAAzB,EAAA;cACA1B,OAAA,CAAA1B,MAAA,GAAAkjB,OAAA,CAAAljB,MAAA;cACA0B,OAAA,CAAAX,SAAA,GAAAA,SAAA;cACAW,OAAA,CAAArD,IAAA,GAAAwG,IAAA,CAAAxG,IAAA;cACA6kB,OAAA,CAAAlkB,UAAA;cAAAqkB,UAAA,CAAApc,IAAA;cAAAoc,UAAA,CAAAnc,IAAA;cAAA,OAEA,IAAAoc,mEAAA;gBAAA5hB,OAAA,EAAAuG,IAAA,CAAAyP,SAAA,CAAAhW,OAAA;cAAA;YAAA;cAAA2H,GAAA,GAAAga,UAAA,CAAAhc,IAAA;cACA6b,OAAA,CAAAvL,UAAA;cACAuL,OAAA,CAAAlkB,UAAA;cACAkkB,OAAA,CAAA7jB,QAAA;cACA6jB,OAAA,CAAApH,gCAAA,CAAAjX,IAAA,CAAAzB,EAAA;cAAAigB,UAAA,CAAAnc,IAAA;cAAA;YAAA;cAAAmc,UAAA,CAAApc,IAAA;cAAAoc,UAAA,CAAA7L,EAAA,GAAA6L,UAAA;cAEAH,OAAA,CAAAlkB,UAAA;YAAA;YAAA;cAAA,OAAAqkB,UAAA,CAAA7b,IAAA;UAAA;QAAA,GAAA2b,SAAA;MAAA;IAEA;IACAI,kBAAA,WAAAA,mBAAAta,GAAA;MAAA,IAAAua,OAAA;MAAA,WAAA/c,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA8c,UAAA;QAAA,WAAA/c,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA4c,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1c,IAAA,GAAA0c,UAAA,CAAAzc,IAAA;YAAA;cACAsc,OAAA,CAAAthB,oBAAA,GAAAshB,OAAA,CAAAthB,oBAAA,CAAA+J,MAAA,WAAA2X,CAAA;gBACA,OAAAA,CAAA,CAAAC,GAAA,IAAA5a,GAAA,CAAA4a,GAAA;cACA;YAAA;YAAA;cAAA,OAAAF,UAAA,CAAAnc,IAAA;UAAA;QAAA,GAAAic,SAAA;MAAA;IACA;IACAK,cAAA,WAAAA,eAAA1gB,EAAA;MACA,IAAAyB,IAAA,QAAAA,IAAA;MACA,IAAAuO,QAAA,GAAAvO,IAAA,CAAAuO,QAAA;MACA,IAAAG,cAAA,GAAA1O,IAAA,CAAA0O,cAAA;MACA,IAAAC,WAAA,GAAA3O,IAAA,CAAA2O,WAAA;MACA,IAAAC,GAAA,GAAA5O,IAAA,CAAA4O,GAAA;MACA,IAAAC,EAAA,GAAA7O,IAAA,CAAA6O,EAAA;MACA,IAAAC,SAAA,GAAA9O,IAAA,CAAA8O,SAAA;MACA,IAAAC,WAAA,GAAA/O,IAAA,CAAA+O,WAAA;MACA,IAAAC,kBAAA,GAAAhP,IAAA,CAAAgP,kBAAA;MACA,IAAAT,QAAA,CAAAmD,MAAA,QACAhD,cAAA,CAAAgD,MAAA,QACA/C,WAAA,IACAC,GAAA,IAAAC,EAAA,IAAAC,SAAA,IAAAC,WAAA,IAAAC,kBAAA;QACA,KAAAhP,IAAA,CAAAiO,sBAAA;QACA,KAAAjO,IAAA,CAAAkO,YAAA;QACA,IAAAM,iBAAA;QACA,KAAAA,iBAAA,CAAAtG,QAAA;UACAsG,iBAAA,CAAAtH,IAAA;UACA,KAAAlH,IAAA,CAAAwO,iBAAA,GAAAA,iBAAA;QACA;MACA;QACA,KAAAxO,IAAA,CAAAiO,sBAAA;QACA,KAAAjO,IAAA,CAAAkO,YAAA;QACA,KAAA8O,UAAA;MACA;IACA;IACAkC,iBAAA,WAAAA,kBAAA3gB,EAAA;MACA,IAAAiG,GAAA;MACA,IAAAxE,IAAA,QAAAA,IAAA;MACA,IAAAuO,QAAA,GAAAvO,IAAA,CAAAuO,QAAA;MACA,IAAAG,cAAA,GAAA1O,IAAA,CAAA0O,cAAA;MACA,IAAAC,WAAA,GAAA3O,IAAA,CAAA2O,WAAA;MACA,IAAAC,GAAA,GAAA5O,IAAA,CAAA4O,GAAA;MACA,IAAAC,EAAA,GAAA7O,IAAA,CAAA6O,EAAA;MACA,IAAAC,SAAA,GAAA9O,IAAA,CAAA8O,SAAA;MACA,IAAAC,WAAA,GAAA/O,IAAA,CAAA+O,WAAA;MACA,IAAAC,kBAAA,GAAAhP,IAAA,CAAAgP,kBAAA;MACA,IAAAT,QAAA,CAAAmD,MAAA,QACAhD,cAAA,CAAAgD,MAAA,QACA/C,WAAA,IACAC,GAAA,IAAAC,EAAA,IAAAC,SAAA,IAAAC,WAAA,IAAAC,kBAAA;QACA,KAAAhP,IAAA,CAAAiO,sBAAA;QACA,KAAAjO,IAAA,CAAAkO,YAAA;QACA1J,GAAA;MACA;QACA,KAAAxE,IAAA,CAAAiO,sBAAA;QACA,KAAAjO,IAAA,CAAAkO,YAAA;QACA1J,GAAA;MACA;MACA,OAAAA,GAAA;IACA;IACAwY,UAAA,WAAAA,WAAAxjB,IAAA;MAAA,IAAA2lB,OAAA;MAAA,WAAAvd,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAsd,UAAA;QAAA,IAAA/f,IAAA,EAAAW,IAAA,EAAAiO,sBAAA,EAAAC,YAAA,EAAAP,IAAA,EAAA0R,KAAA,EAAAC,KAAA,EAAAzR,IAAA,EAAA0R,KAAA,EAAAxR,IAAA,EAAAyR,KAAA,EAAA1R,IAAA,EAAA2R,KAAA,EAAAC,KAAA,EAAAC,SAAA,EAAAnR,iBAAA,EAAAC,kBAAA,EAAAjK,GAAA,EAAAob,qBAAA,EAAAC,OAAA,EAAAC,QAAA,EAAAC,WAAA,EAAAC,OAAA,EAAAxa,IAAA,EAAAya,aAAA,EAAAC,SAAA,EAAAC,IAAA,EAAA9iB,oBAAA,EAAA+iB,WAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,cAAA,EAAAC,UAAA,EAAAC,QAAA;QAAA,WAAA5e,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAye,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAve,IAAA,GAAAue,UAAA,CAAAte,IAAA;YAAA;cACAhD,IAAA;cACAW,IAAA,GAAAmf,OAAA,CAAAnf,IAAA;cACA,IAAAA,IAAA,CAAA2N,IAAA,CAAA+D,MAAA;gBACArS,IAAA,CAAA6H,IAAA,CAAAiY,OAAA,CAAAhiB,eAAA,CAAAiK,MAAA,WAAAC,CAAA;kBAAA,OAAArH,IAAA,CAAA2N,IAAA,CAAAzF,QAAA,CAAAb,CAAA,CAAA9I,EAAA;gBAAA,GACAqiB,IAAA,WAAAC,EAAA,EAAAC,EAAA;kBAAA,OAAAD,EAAA,CAAAtiB,EAAA,GAAAuiB,EAAA,CAAAviB,EAAA;gBAAA,GAAAyT,GAAA,WAAA3K,CAAA;kBAAA,OAAAA,CAAA,CAAA9I,EAAA;gBAAA,GAAA2b,IAAA;cACA;cACA,IAAAla,IAAA,CAAA6N,IAAA,CAAA6D,MAAA;gBACArS,IAAA,CAAA6H,IAAA,CAAAiY,OAAA,CAAA3hB,WAAA,CAAA4J,MAAA,WAAAC,CAAA;kBAAA,OAAArH,IAAA,CAAA6N,IAAA,CAAA3F,QAAA,CAAAb,CAAA,CAAA9I,EAAA;gBAAA,GAAAqiB,IAAA,WAAAC,EAAA,EAAAC,EAAA;kBAAA,OAAAD,EAAA,CAAAtiB,EAAA,GAAAuiB,EAAA,CAAAviB,EAAA;gBAAA,GACAyT,GAAA,WAAA3K,CAAA;kBAAA,OAAAA,CAAA,CAAA9I,EAAA;gBAAA,GAAA2b,IAAA;cACA;cACA,IAAAla,IAAA,CAAA+N,IAAA,CAAA2D,MAAA;gBACArS,IAAA,CAAA6H,IAAA,CAAAiY,OAAA,CAAAzhB,WAAA,CAAA0J,MAAA,WAAAC,CAAA;kBAAA,OAAArH,IAAA,CAAA+N,IAAA,CAAA7F,QAAA,CAAAb,CAAA,CAAA9I,EAAA;gBAAA,GAAAqiB,IAAA,WAAAC,EAAA,EAAAC,EAAA;kBAAA,OAAAD,EAAA,CAAAtiB,EAAA,GAAAuiB,EAAA,CAAAviB,EAAA;gBAAA,GACAyT,GAAA,WAAA3K,CAAA;kBAAA,OAAAA,CAAA,CAAA9I,EAAA;gBAAA,GAAA2b,IAAA;cACA;cACA,IAAAla,IAAA,CAAA8N,IAAA,CAAA4D,MAAA;gBACArS,IAAA,CAAA6H,IAAA,CAAAiY,OAAA,CAAAxhB,WAAA,CAAAyJ,MAAA,WAAAC,CAAA;kBAAA,OAAArH,IAAA,CAAA8N,IAAA,CAAA5F,QAAA,CAAAb,CAAA,CAAA9I,EAAA;gBAAA,GAAAqiB,IAAA,WAAAC,EAAA,EAAAC,EAAA;kBAAA,OAAAD,EAAA,CAAAtiB,EAAA,GAAAuiB,EAAA,CAAAviB,EAAA;gBAAA,GACAyT,GAAA,WAAA3K,CAAA;kBAAA,OAAAA,CAAA,CAAA9I,EAAA;gBAAA,GAAA2b,IAAA;cACA;cACA,IAAAla,IAAA,CAAAU,IAAA,CAAAgR,MAAA;gBACArS,IAAA,CAAA6H,IAAA,CAAAiY,OAAA,CAAAvhB,WAAA,CAAAwJ,MAAA,WAAAC,CAAA;kBAAA,OAAArH,IAAA,CAAAU,IAAA,CAAAwH,QAAA,CAAAb,CAAA,CAAA9I,EAAA;gBAAA,GAAAqiB,IAAA,WAAAC,EAAA,EAAAC,EAAA;kBAAA,OAAAD,EAAA,CAAAtiB,EAAA,GAAAuiB,EAAA,CAAAviB,EAAA;gBAAA,GACAyT,GAAA,WAAA3K,CAAA;kBAAA,OAAAA,CAAA,CAAA9I,EAAA;gBAAA,GAAA2b,IAAA;cACA;cACAiF,OAAA,CAAAnf,IAAA,CAAAgO,MAAA,GAAA3O,IAAA,CAAA6a,IAAA;cAAA,MAEA1gB,IAAA;gBAAAmnB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cACAC,YAAA;cACAP,IAAA,GAAA3N,IAAA,CAAA2N,IAAA;cACA0R,KAAA;cACAC,KAAA;cAEAzR,IAAA,GAAA7N,IAAA,CAAA6N,IAAA;cACA0R,KAAA;cAEAxR,IAAA,GAAA/N,IAAA,CAAA+N,IAAA;cACAyR,KAAA;cAEA1R,IAAA,GAAA9N,IAAA,CAAA8N,IAAA;cACA2R,KAAA;cACAC,KAAA;cACAC,SAAA;cACAnR,iBAAA;cACAC,kBAAA;cACA,IAAA0Q,OAAA,CAAA4B,oBAAA,CAAAjT,IAAA,EAAA2R,KAAA;gBACA,IAAAN,OAAA,CAAA4B,oBAAA,CAAAjT,IAAA,EAAA4R,KAAA;kBACA,KAAAlR,iBAAA,CAAAtG,QAAA;oBACAsG,iBAAA,CAAAtH,IAAA;kBACA;gBACA;cACA;cACA,IAAAiY,OAAA,CAAA4B,oBAAA,CAAApT,IAAA,EAAA2R,KAAA;gBACA,KAAA7Q,kBAAA,CAAAvG,QAAA;kBACAuG,kBAAA,CAAAvH,IAAA;gBACA;cACA;cACA,IAAAiY,OAAA,CAAA4B,oBAAA,CAAAhT,IAAA,EAAAyR,KAAA;gBACA,KAAA/Q,kBAAA,CAAAvG,QAAA;kBACAuG,kBAAA,CAAAvH,IAAA;gBACA;cACA;cAAA,KACAiY,OAAA,CAAA4B,oBAAA,CAAApT,IAAA,EAAA0R,KAAA;gBAAAsB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cAAA0S,UAAA,CAAAte,IAAA;cAAA;YAAA;cAAA,KACA8c,OAAA,CAAA4B,oBAAA,CAAAlT,IAAA,EAAA0R,KAAA;gBAAAoB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cAAA0S,UAAA,CAAAte,IAAA;cAAA;YAAA;cAAA,KACA8c,OAAA,CAAA4B,oBAAA,CAAAjT,IAAA,EAAA2R,KAAA;gBAAAkB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cAAA0S,UAAA,CAAAte,IAAA;cAAA;YAAA;cAAA,KACA8c,OAAA,CAAA4B,oBAAA,CAAApT,IAAA,EAAA2R,KAAA;gBAAAqB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cACAC,YAAA;cAAAyS,UAAA,CAAAte,IAAA;cAAA;YAAA;cAAA,KACA8c,OAAA,CAAA4B,oBAAA,CAAAhT,IAAA,EAAAyR,KAAA;gBAAAmB,UAAA,CAAAte,IAAA;gBAAA;cAAA;cACA4L,sBAAA;cACAC,YAAA;cAAAyS,UAAA,CAAAte,IAAA;cAAA;YAAA;cAEA4L,sBAAA;cACAC,YAAA;cAAAyS,UAAA,CAAAte,IAAA;cAAA,OACA8c,OAAA,CAAAD,iBAAA;YAAA;cAAA1a,GAAA,GAAAmc,UAAA,CAAAne,IAAA;cACA,IAAAgC,GAAA,UAAAA,GAAA;gBACAmb,SAAA;cACA;gBACAtiB,qBAAA,GAAA8hB,OAAA,CAAA9hB,oBAAA;gBACAwiB,OAAA;gBACAC,QAAA;gBAAAC,WAAA,OAAAxZ,2BAAA,CAAA7M,OAAA,EACA2D,qBAAA;gBAAA;kBAAA,KAAA0iB,WAAA,CAAAvZ,CAAA,MAAAwZ,OAAA,GAAAD,WAAA,CAAAtZ,CAAA,IAAAC,IAAA;oBAAAlB,IAAA,GAAAwa,OAAA,CAAArhB,KAAA;oBACAshB,aAAA,GAAAza,IAAA,CAAAya,aAAA;oBACAC,SAAA,GAAA1a,IAAA,CAAA0a,SAAA;oBACA,IAAAD,aAAA;sBACAJ,OAAA;oBACA,WAAAK,SAAA;sBACAJ,QAAA;oBACA;kBACA;gBAAA,SAAAjZ,GAAA;kBAAAkZ,WAAA,CAAAjZ,CAAA,CAAAD,GAAA;gBAAA;kBAAAkZ,WAAA,CAAAhZ,CAAA;gBAAA;gBACA,IAAA8Y,OAAA;kBACA5R,sBAAA;kBACA0R,SAAA;gBACA,WAAAG,QAAA;kBACA7R,sBAAA;kBACAC,YAAA;kBACAyR,SAAA;gBACA;cACA;YAAA;cAAAgB,UAAA,CAAAte,IAAA;cAAA,OAEA8c,OAAA,CAAAD,iBAAA;YAAA;cAAAiB,IAAA,GAAAQ,UAAA,CAAAne,IAAA;cACA,IAAA2d,IAAA;gBACA,KAAA3R,iBAAA,CAAAtG,QAAA;kBACAsG,iBAAA,CAAAtH,IAAA;gBACA;cACA;cACA7J,oBAAA,GAAA8hB,OAAA,CAAA9hB,oBAAA;cAAA+iB,WAAA,OAAA7Z,2BAAA,CAAA7M,OAAA,EACA2D,oBAAA;cAAA;gBAAA,KAAA+iB,WAAA,CAAA5Z,CAAA,MAAA6Z,OAAA,GAAAD,WAAA,CAAA3Z,CAAA,IAAAC,IAAA;kBAAAlB,MAAA,GAAA6a,OAAA,CAAA1hB,KAAA;kBACAshB,cAAA,GAAAza,MAAA,CAAAya,aAAA;kBACAC,UAAA,GAAA1a,MAAA,CAAA0a,SAAA;kBACAO,QAAA,GAAAjb,MAAA,CAAAib,QAAA;kBACA,IAAAR,cAAA;oBACA,KAAAzR,iBAAA,CAAAtG,QAAA;sBACAsG,iBAAA,CAAAtH,IAAA;oBACA;kBACA;kBACA,IAAAgZ,UAAA;oBACA,KAAAzR,kBAAA,CAAAvG,QAAA;sBACAuG,kBAAA,CAAAvH,IAAA;oBACA;kBACA;kBACA,IAAAuZ,QAAA,CAAA/W,OAAA,iBAAA+W,QAAA,CAAA/W,OAAA;oBACA,KAAA+E,kBAAA,CAAAvG,QAAA;sBACAuG,kBAAA,CAAAvH,IAAA;oBACA;kBACA;gBACA;cAAA,SAAAL,GAAA;gBAAAuZ,WAAA,CAAAtZ,CAAA,CAAAD,GAAA;cAAA;gBAAAuZ,WAAA,CAAArZ,CAAA;cAAA;cACAoY,OAAA,CAAAnf,IAAA,CAAAwO,iBAAA,GAAAA,iBAAA;cACA2Q,OAAA,CAAAnf,IAAA,CAAAyO,kBAAA,GAAAA,kBAAA;cACA,IAAAkR,SAAA;gBACAR,OAAA,CAAAnf,IAAA,CAAAiO,sBAAA,GAAAA,sBAAA;gBACAkR,OAAA,CAAAnf,IAAA,CAAAkO,YAAA,GAAAA,YAAA;cACA;YAAA;YAAA;cAAA,OAAAyS,UAAA,CAAAhe,IAAA;UAAA;QAAA,GAAAyc,SAAA;MAAA;IAGA;IACA2B,oBAAA,WAAAA,qBAAAC,IAAA,EAAAC,IAAA;MACA,OAAAD,IAAA,CAAAE,IAAA,WAAA1b,IAAA;QAAA,OAAAyb,IAAA,CAAA/Y,QAAA,CAAA1C,IAAA;MAAA;IACA;IACA2b,QAAA,WAAAA,SAAA;MACA,KAAAjnB,OAAA;IACA;IACAknB,QAAA,WAAAA,SAAAlD,SAAA,EAAAte,cAAA;MAAA,IAAAyhB,OAAA;MAAA,WAAAzf,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAwf,UAAA;QAAA,IAAAjkB,oBAAA,EAAAmH,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAsf,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAApf,IAAA,GAAAof,UAAA,CAAAnf,IAAA;YAAA;cACAgf,OAAA,CAAArhB,IAAA,CAAAiN,cAAA,GAAArN,cAAA;cACAyhB,OAAA,CAAArhB,IAAA,CAAAkN,aAAA,GAAAgR,SAAA;cACAmD,OAAA,CAAAnnB,OAAA;cACAmD,oBAAA;cAAA,MACAuC,cAAA,IAAAse,SAAA;gBAAAsD,UAAA,CAAAnf,IAAA;gBAAA;cAAA;cAAAmf,UAAA,CAAAnf,IAAA;cAAA,OACA,IAAAub,oDAAA;gBAAArf,EAAA,EAAA2f;cAAA;YAAA;cAAA1Z,GAAA,GAAAgd,UAAA,CAAAhf,IAAA;cACA,IAAAgC,GAAA,CAAA7K,IAAA;gBACA,IAAA6K,GAAA,CAAA7K,IAAA,CAAA2c,QAAA;kBACAjZ,oBAAA,GAAAmH,GAAA,CAAA7K,IAAA,CAAA2c,QAAA;gBACA;gBACA,IAAA9R,GAAA,CAAA7K,IAAA,CAAA8nB,IAAA;kBACAJ,OAAA,CAAArhB,IAAA,CAAAmN,OAAA,GAAA3I,GAAA,CAAA7K,IAAA,CAAA8nB,IAAA;gBACA;cACA;cACAJ,OAAA,CAAAhkB,oBAAA,GAAAA,oBAAA;cACA,IAAAA,oBAAA,IAAAA,oBAAA,CAAAqU,MAAA;gBACA2P,OAAA,CAAArE,UAAA;cACA;cAAAwE,UAAA,CAAAnf,IAAA;cAAA;YAAA;cAEAgf,OAAA,CAAA5O,QAAA;YAAA;YAAA;cAAA,OAAA+O,UAAA,CAAA7e,IAAA;UAAA;QAAA,GAAA2e,SAAA;MAAA;IAEA;IACAI,oBAAA,WAAAA,qBAAA;MACA,KAAAlnB,QAAA;MACA,KAAA2B,aAAA;MACA,KAAAhB,MAAA;MACA,KAAAgB,aAAA,QAAAD,SAAA;IACA;IACAylB,qBAAA,WAAAA,sBAAAvd,GAAA;MAAA,IAAAwd,OAAA;MAAA,WAAAhgB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA+f,UAAA;QAAA,IAAAC,OAAA,EAAAhM,QAAA;QAAA,WAAAjU,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA8f,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA5f,IAAA,GAAA4f,UAAA,CAAA3f,IAAA;YAAA;cACAuf,OAAA,CAAApnB,QAAA;cAAAwnB,UAAA,CAAA3f,IAAA;cAAA,OACA,IAAA4f,6DAAA;gBAAA1jB,EAAA,EAAA6F,GAAA,CAAA7F;cAAA;YAAA;cAAAujB,OAAA,GAAAE,UAAA,CAAAxf,IAAA;cACAsT,QAAA,GAAAgM,OAAA,CAAAhM,QAAA;cACA,IAAAA,QAAA;gBACA8L,OAAA,CAAAzlB,aAAA,GAAAiH,IAAA,CAAAC,KAAA,CAAAyS,QAAA;cACA;gBACA8L,OAAA,CAAAzlB,aAAA;cACA;cACAylB,OAAA,CAAA5hB,IAAA,CAAAxG,IAAA,GAAAsoB,OAAA,CAAAtoB,IAAA;cACAooB,OAAA,CAAAzmB,MAAA,GAAA2mB,OAAA,CAAAvjB,EAAA;YAAA;YAAA;cAAA,OAAAyjB,UAAA,CAAArf,IAAA;UAAA;QAAA,GAAAkf,SAAA;MAAA;IACA;IACAjL,qBAAA,WAAAA,sBAAApd,IAAA;MAAA,IAAA0oB,OAAA;MAAA,WAAAtgB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAqgB,UAAA;QAAA,IAAA5jB,EAAA,EAAA5E,IAAA,EAAA6C,QAAA,EAAAuZ,IAAA,EAAAC,IAAA;QAAA,WAAAnU,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAmgB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAjgB,IAAA,GAAAigB,UAAA,CAAAhgB,IAAA;YAAA;cAAA,MACA7I,IAAA;gBAAA6oB,UAAA,CAAAhgB,IAAA;gBAAA;cAAA;cAAAggB,UAAA,CAAAhgB,IAAA;cAAA,OACA6f,OAAA,CAAAxO,QAAA;YAAA;cAEAnV,EAAA,GAAA2jB,OAAA,CAAAliB,IAAA,CAAAzB,EAAA;cACA2jB,OAAA,CAAA/nB,UAAA;cAAAkoB,UAAA,CAAAhgB,IAAA;cAAA,OACA,IAAAigB,8CAAA;gBAAA/jB,EAAA,EAAAA;cAAA;YAAA;cAAA5E,IAAA,GAAA0oB,UAAA,CAAA7f,IAAA;cACAhG,QAAA,GAAA7C,IAAA,CAAAA,IAAA;cACAoc,IAAA,GAAAvZ,QAAA,CAAAuZ,IAAA;cACA,IAAAA,IAAA;gBACAmM,OAAA,CAAAzlB,YAAA,GAAAsZ,IAAA,CAAA/D,GAAA,WAAA9Y,IAAA;kBAAA;oBAAAA,IAAA,EAAAA;kBAAA;gBAAA;gBAAA;cACA;gBACAgpB,OAAA,CAAAzlB,YAAA;cACA;cACAuZ,IAAA,GAAAxZ,QAAA,CAAAwZ,IAAA;cACA,IAAAD,IAAA;gBACAmM,OAAA,CAAAxlB,YAAA,GAAAsZ,IAAA,CAAAhE,GAAA,WAAA9Y,IAAA;kBAAA;oBAAAA,IAAA,EAAAA;kBAAA;gBAAA;gBAAA;cACA;gBACAgpB,OAAA,CAAAxlB,YAAA;cACA;cACAwlB,OAAA,CAAA/nB,UAAA;YAAA;YAAA;cAAA,OAAAkoB,UAAA,CAAA1f,IAAA;UAAA;QAAA,GAAAwf,SAAA;MAAA;IACA;IACA;IACAI,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5gB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA2gB,UAAA;QAAA,IAAAvE,SAAA,EAAAjiB,iBAAA,EAAAuI,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAygB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvgB,IAAA,GAAAugB,UAAA,CAAAtgB,IAAA;YAAA;cACA6b,SAAA,GAAAsE,OAAA,CAAAxiB,IAAA,CAAAzB,EAAA;cACAtC,iBAAA,GAAAumB,OAAA,CAAAvmB,iBAAA;cACAumB,OAAA,CAAAroB,UAAA;cAAAwoB,UAAA,CAAAvgB,IAAA;cAAAugB,UAAA,CAAAtgB,IAAA;cAAA,OAEA,IAAAugB,qDAAA;gBAAArkB,EAAA,EAAA2f,SAAA;gBAAAjiB,iBAAA,EAAAmH,IAAA,CAAAyP,SAAA,CAAA5W,iBAAA;cAAA;YAAA;cAAAuI,GAAA,GAAAme,UAAA,CAAAngB,IAAA;cACAggB,OAAA,CAAA1P,UAAA;cACA0P,OAAA,CAAAroB,UAAA;cAAAwoB,UAAA,CAAAtgB,IAAA;cAAA;YAAA;cAAAsgB,UAAA,CAAAvgB,IAAA;cAAAugB,UAAA,CAAAhQ,EAAA,GAAAgQ,UAAA;cAEAH,OAAA,CAAAroB,UAAA;YAAA;YAAA;cAAA,OAAAwoB,UAAA,CAAAhgB,IAAA;UAAA;QAAA,GAAA8f,SAAA;MAAA;IAEA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlhB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAihB,UAAA;QAAA,IAAA7E,SAAA,EAAA8E,WAAA,EAAAlnB,wBAAA,EAAAmnB,WAAA,EAAAC,OAAA,EAAA1d,IAAA,EAAAhB,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAkhB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhhB,IAAA,GAAAghB,UAAA,CAAA/gB,IAAA;YAAA;cACA6b,SAAA,GAAA4E,OAAA,CAAA9iB,IAAA,CAAAzB,EAAA;cACAukB,OAAA,CAAA3oB,UAAA;cAAAipB,UAAA,CAAAhhB,IAAA;cAEA4gB,WAAA;cACAlnB,wBAAA,GAAAgnB,OAAA,CAAAhnB,wBAAA;cAAAmnB,WAAA,OAAA1c,2BAAA,CAAA7M,OAAA,EACAoC,wBAAA;cAAA;gBAAA,KAAAmnB,WAAA,CAAAzc,CAAA,MAAA0c,OAAA,GAAAD,WAAA,CAAAxc,CAAA,IAAAC,IAAA;kBAAAlB,IAAA,GAAA0d,OAAA,CAAAvkB,KAAA;kBACAqkB,WAAA,CAAA9b,IAAA;oBACAmc,OAAA,EAAA7d,IAAA,CAAA6d,OAAA;oBACAC,QAAA,EAAA9d,IAAA,CAAA8d;kBACA;gBACA;cAAA,SAAAzc,GAAA;gBAAAoc,WAAA,CAAAnc,CAAA,CAAAD,GAAA;cAAA;gBAAAoc,WAAA,CAAAlc,CAAA;cAAA;cAAAqc,UAAA,CAAA/gB,IAAA;cAAA,OACA,IAAAkhB,6CAAA;gBAAAhlB,EAAA,EAAA2f,SAAA;gBAAA8E,WAAA,EAAA5f,IAAA,CAAAyP,SAAA,CAAA/W,wBAAA;cAAA;YAAA;cAAA0I,GAAA,GAAA4e,UAAA,CAAA5gB,IAAA;cACAsgB,OAAA,CAAAhQ,UAAA;cACAgQ,OAAA,CAAA3oB,UAAA;cAAAipB,UAAA,CAAA/gB,IAAA;cAAA;YAAA;cAAA+gB,UAAA,CAAAhhB,IAAA;cAAAghB,UAAA,CAAAzQ,EAAA,GAAAyQ,UAAA;cAEAN,OAAA,CAAA3oB,UAAA;YAAA;YAAA;cAAA,OAAAipB,UAAA,CAAAzgB,IAAA;UAAA;QAAA,GAAAogB,SAAA;MAAA;IAEA;IACAS,YAAA,WAAAA,aAAA3K,KAAA;MAAA,IAAA4K,OAAA;MACA,IAAAC,OAAA,GAAA7K,KAAA,CAAA6K,OAAA;QAAA/pB,IAAA,GAAAkf,KAAA,CAAAlf,IAAA;MACA,IAAAgqB,IAAA;MACAD,OAAA,CAAAE,OAAA,WAAAC,MAAA,EAAApI,KAAA;QACA,IAAAA,KAAA;UACAkI,IAAA,CAAAlI,KAAA;UACA;QACA;QACA,eAAAvT,QAAA,CAAA2b,MAAA,CAAAnlB,KAAA;UACAilB,IAAA,CAAAlI,KAAA;UACA;QACA;QACA,IAAAqI,MAAA,GAAAnqB,IAAA,CAAAqY,GAAA,WAAAxM,IAAA;UAAA,OAAAue,MAAA,CAAAve,IAAA,CAAAqe,MAAA,CAAAG,QAAA;QAAA;QACA,KAAAF,MAAA,CAAAG,KAAA,WAAAtlB,KAAA;UAAA,OAAAulB,KAAA,CAAAvlB,KAAA;QAAA;UACAglB,IAAA,CAAAlI,KAAA,IAAAqI,MAAA,CAAAK,MAAA,WAAA/hB,IAAA,EAAAgiB,IAAA;YACA,IAAAzlB,KAAA,GAAAolB,MAAA,CAAAK,IAAA;YACA,KAAAF,KAAA,CAAAvlB,KAAA;cACA,OAAA8kB,OAAA,CAAAY,UAAA,CAAAjiB,IAAA,GAAAgiB,IAAA;YACA;cACA,OAAAX,OAAA,CAAAY,UAAA,CAAAjiB,IAAA;YACA;UACA;UACAuhB,IAAA,CAAAlI,KAAA;QACA;UACAkI,IAAA,CAAAlI,KAAA;QACA;MACA;MACA,OAAAkI,IAAA;IACA;IACAW,oBAAA,WAAAA,qBAAAzL,KAAA;MAAA,IAAA0L,OAAA;MACA,IAAAb,OAAA,GAAA7K,KAAA,CAAA6K,OAAA;QAAA/pB,IAAA,GAAAkf,KAAA,CAAAlf,IAAA;MACA,IAAAgqB,IAAA;MACAD,OAAA,CAAAE,OAAA,WAAAC,MAAA,EAAApI,KAAA;QACA,IAAAA,KAAA;UACAkI,IAAA,CAAAlI,KAAA;UACA;QACA;QACA,YAAAvT,QAAA,CAAA2b,MAAA,CAAAnlB,KAAA;UACAilB,IAAA,CAAAlI,KAAA;UACA;QACA;QACA,IAAAqI,MAAA,GAAAnqB,IAAA,CAAAqY,GAAA,WAAAxM,IAAA;UAAA,OAAAue,MAAA,CAAAve,IAAA,CAAAqe,MAAA,CAAAG,QAAA;QAAA;QACA,KAAAF,MAAA,CAAAG,KAAA,WAAAtlB,KAAA;UAAA,OAAAulB,KAAA,CAAAvlB,KAAA;QAAA;UACAglB,IAAA,CAAAlI,KAAA,IAAAqI,MAAA,CAAAK,MAAA,WAAA/hB,IAAA,EAAAgiB,IAAA;YACA,IAAAzlB,KAAA,GAAAolB,MAAA,CAAAK,IAAA;YACA,KAAAF,KAAA,CAAAvlB,KAAA;cACA,OAAA4lB,OAAA,CAAAF,UAAA,CAAAjiB,IAAA,GAAAgiB,IAAA;YACA;cACA,OAAAG,OAAA,CAAAF,UAAA,CAAAjiB,IAAA;YACA;UACA;UACAuhB,IAAA,CAAAlI,KAAA;QACA;UACAkI,IAAA,CAAAlI,KAAA;QACA;MACA;MACA,OAAAkI,IAAA;IACA;IACAa,mBAAA,WAAAA,oBAAAC,CAAA;MACA,IAAA7Y,MAAA,GAAA6Y,CAAA,CAAArgB,GAAA,CAAAwH,MAAA;MACA,IAAAA,MAAA;QACA,IAAAA,MAAA;UACA;YACA8Y,UAAA;UACA;QACA,WAAA9Y,MAAA,SAAAA,MAAA;UACA;YACA8Y,UAAA;YACA;UACA;QACA,WAAA9Y,MAAA;UACA;YACA8Y,UAAA;UACA;QACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAF,CAAA;MACA,IAAAG,OAAA,GAAAH,CAAA,CAAArgB,GAAA,CAAAwgB,OAAA;MACA,IAAAC,OAAA,GAAAJ,CAAA,CAAArgB,GAAA,CAAAygB,OAAA;MACA,IAAAA,OAAA;QACA;UACAC,KAAA;QACA;MACA;QACA,IAAAF,OAAA;UACA;YACAE,KAAA;UACA;QACA;MACA;IACA;IACAC,yBAAA,WAAAA,0BAAAC,KAAA;MAAA,IAAA5gB,GAAA,GAAA4gB,KAAA,CAAA5gB,GAAA;QAAAyf,MAAA,GAAAmB,KAAA,CAAAnB,MAAA;MACA,IAAAA,MAAA,CAAAnlB,KAAA,eAAAmlB,MAAA,CAAAnlB,KAAA;QACA,IAAA0F,GAAA,CAAA6gB,MAAA;UACA;QACA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA9gB,GAAA;MAAA,IAAA+gB,OAAA;MACA,IAAA/gB,GAAA,CAAA5K,IAAA;QACA,KAAAwhB,SAAA,kBAAApZ,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAsjB,UAAA;UAAA,IAAAC,OAAA;UAAA,WAAAxjB,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAqjB,WAAAC,UAAA;YAAA,kBAAAA,UAAA,CAAAnjB,IAAA,GAAAmjB,UAAA,CAAAljB,IAAA;cAAA;gBACA8iB,OAAA,CAAApL,KAAA,CAAAyL,oBAAA,CAAAliB,KAAA;gBACA6hB,OAAA,CAAApL,KAAA,CAAAyL,oBAAA,CAAAjiB,IAAA;gBACA8hB,OAAA;kBAAA9mB,EAAA,EAAA6F,GAAA,CAAAG;gBAAA;gBACA4gB,OAAA,CAAApL,KAAA,CAAAyL,oBAAA,CAAAhiB,YAAA,CAAA6hB,OAAA;gBACAF,OAAA,CAAApL,KAAA,CAAAyL,oBAAA,CAAAlmB,IAAA;cAAA;cAAA;gBAAA,OAAAimB,UAAA,CAAA5iB,IAAA;YAAA;UAAA,GAAAyiB,SAAA;QAAA,CACA;MACA;IACA;IACAK,UAAA,WAAAA,WAAArhB,GAAA,EAAAqX,KAAA;MACA,IAAArX,GAAA,CAAAshB,KAAA;QACA;MACA;QACA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAAnsB,IAAA;MAAA,IAAAosB,OAAA;MAAA,WAAAhkB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA+jB,UAAA;QAAA,IAAAvX,kBAAA,EAAAuK,KAAA,EAAArU,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA6jB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA3jB,IAAA,GAAA2jB,UAAA,CAAA1jB,IAAA;YAAA;cACAiM,kBAAA;cACAA,kBAAA,CAAAyH,IAAA,GAAA6P,OAAA,CAAAnpB,YAAA,CAAAuV,GAAA,WAAAxM,IAAA;gBAAA,OAAAA,IAAA,CAAAtM,IAAA;cAAA;cACAoV,kBAAA,CAAA0H,IAAA,GAAA4P,OAAA,CAAAlpB,YAAA,CAAAsV,GAAA,WAAAxM,IAAA;gBAAA,OAAAA,IAAA,CAAAtM,IAAA;cAAA;cACA2f,KAAA;gBACAta,EAAA,EAAAqnB,OAAA,CAAA5lB,IAAA,CAAAzB,EAAA;gBACA8P,aAAA,EAAAjL,IAAA,CAAAyP,SAAA,CAAAvE,kBAAA;gBACA9U,IAAA,EAAAA;cACA;cACA,IAAAA,IAAA;gBACA8U,kBAAA,CAAAyH,IAAA,GAAA6P,OAAA,CAAAjpB,iBAAA,CAAAqV,GAAA,WAAAxM,IAAA;kBAAA,OAAAA,IAAA,CAAAtM,IAAA;gBAAA;gBACAoV,kBAAA,CAAA0H,IAAA,GAAA4P,OAAA,CAAAhpB,iBAAA,CAAAoV,GAAA,WAAAxM,IAAA;kBAAA,OAAAA,IAAA,CAAAtM,IAAA;gBAAA;gBACA2f,KAAA;kBACAta,EAAA,EAAAqnB,OAAA,CAAA5lB,IAAA,CAAAzB,EAAA;kBACA+P,kBAAA,EAAAlL,IAAA,CAAAyP,SAAA,CAAAvE,kBAAA;kBACA9U,IAAA,EAAAA;gBACA;cACA;cAAAusB,UAAA,CAAA1jB,IAAA;cAAA,OACA,IAAAsjB,kDAAA,EAAA9M,KAAA;YAAA;cAAArU,GAAA,GAAAuhB,UAAA,CAAAvjB,IAAA;cACAojB,OAAA,CAAA9S,UAAA;YAAA;YAAA;cAAA,OAAAiT,UAAA,CAAApjB,IAAA;UAAA;QAAA,GAAAkjB,SAAA;MAAA;IACA;IACAG,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MAAA,WAAArkB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAokB,UAAA;QAAA,WAAArkB,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAkkB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAhkB,IAAA,GAAAgkB,UAAA,CAAA/jB,IAAA;YAAA;cACA4jB,OAAA,CAAAI,cAAA;YAAA;YAAA;cAAA,OAAAD,UAAA,CAAAzjB,IAAA;UAAA;QAAA,GAAAujB,SAAA;MAAA;IACA;IACAG,cAAA,WAAAA,eAAA7sB,IAAA;MAAA,IAAA8sB,OAAA;MAAA,WAAA1kB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAykB,UAAA;QAAA,IAAA/hB,GAAA,EAAAtI,SAAA,EAAAsqB,WAAA,EAAAC,OAAA,EAAAjhB,IAAA;QAAA,WAAA3D,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAykB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAvkB,IAAA,GAAAukB,UAAA,CAAAtkB,IAAA;YAAA;cAAA,KACAikB,OAAA,CAAAtmB,IAAA,CAAAmL,iBAAA;gBAAAwb,UAAA,CAAAtkB,IAAA;gBAAA;cAAA;cAAAskB,UAAA,CAAAvkB,IAAA;cAAA,MAEA5I,IAAA;gBAAAmtB,UAAA,CAAAtkB,IAAA;gBAAA;cAAA;cAAAskB,UAAA,CAAAtkB,IAAA;cAAA,OACAikB,OAAA,CAAA5S,QAAA;YAAA;cAEA4S,OAAA,CAAAnsB,UAAA;cAAAwsB,UAAA,CAAAtkB,IAAA;cAAA,OACA,IAAAukB,2BAAA,EAAAN,OAAA,CAAAtmB,IAAA,CAAAmL,iBAAA;YAAA;cAAA3G,GAAA,GAAAmiB,UAAA,CAAAnkB,IAAA;cACA,IAAAgC,GAAA,CAAAnF,IAAA,YAAAmF,GAAA,CAAA7K,IAAA,IAAA6K,GAAA,CAAA7K,IAAA,CAAAuC,SAAA;gBACAA,SAAA,GAAAkH,IAAA,CAAAC,KAAA,CAAAmB,GAAA,CAAA7K,IAAA,CAAAuC,SAAA;gBAAAsqB,WAAA,OAAAjgB,2BAAA,CAAA7M,OAAA,EACAwC,SAAA;gBAAA;kBAAA,KAAAsqB,WAAA,CAAAhgB,CAAA,MAAAigB,OAAA,GAAAD,WAAA,CAAA/f,CAAA,IAAAC,IAAA;oBAAAlB,IAAA,GAAAihB,OAAA,CAAA9nB,KAAA;oBACA6G,IAAA,CAAAqhB,WAAA;kBACA;gBAAA,SAAAhgB,GAAA;kBAAA2f,WAAA,CAAA1f,CAAA,CAAAD,GAAA;gBAAA;kBAAA2f,WAAA,CAAAzf,CAAA;gBAAA;gBACAuf,OAAA,CAAApqB,SAAA,GAAAA,SAAA;cACA;cACAoqB,OAAA,CAAAnsB,UAAA;cAAAwsB,UAAA,CAAAtkB,IAAA;cAAA;YAAA;cAAAskB,UAAA,CAAAvkB,IAAA;cAAAukB,UAAA,CAAAhU,EAAA,GAAAgU,UAAA;cAEAL,OAAA,CAAAnsB,UAAA;YAAA;YAAA;cAAA,OAAAwsB,UAAA,CAAAhkB,IAAA;UAAA;QAAA,GAAA4jB,SAAA;MAAA;IAGA;IACAO,OAAA,WAAAA,QAAAzf,CAAA;MACA,KAAAnL,SAAA,CAAA6qB,MAAA,CAAA1f,CAAA;IACA;IACA2f,aAAA,WAAAA,cAAA;MACA,KAAA5sB,YAAA;MACA,KAAAsB,KAAA;IACA;IACAurB,cAAA,WAAAA,eAAArgB,QAAA;MAAA,IAAAsgB,WAAA,OAAA3gB,2BAAA,CAAA7M,OAAA,EACAkN,QAAA,CAAAO,KAAA;QAAAggB,OAAA;MAAA;QAAA,KAAAD,WAAA,CAAA1gB,CAAA,MAAA2gB,OAAA,GAAAD,WAAA,CAAAzgB,CAAA,IAAAC,IAAA;UAAA,IAAAlB,IAAA,GAAA2hB,OAAA,CAAAxoB,KAAA;UACA,KAAAyoB,QAAA,CAAA5hB,IAAA,CAAAjH,EAAA;QACA;MAAA,SAAAsI,GAAA;QAAAqgB,WAAA,CAAApgB,CAAA,CAAAD,GAAA;MAAA;QAAAqgB,WAAA,CAAAngB,CAAA;MAAA;IACA;IACAqgB,QAAA,WAAAA,SAAA7oB,EAAA;MACA,SAAA7C,KAAA,CAAAwM,QAAA,CAAA3J,EAAA;QACA,KAAA7C,KAAA,QAAAA,KAAA,CAAA0L,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,KAAA9I,EAAA;QAAA;MACA;QACA,KAAA7C,KAAA,CAAAwL,IAAA,CAAA3I,EAAA;MACA;IACA;IACA8oB,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,SAAA5rB,KAAA,CAAAgW,MAAA;QACA,IAAA5I,GAAA,QAAA9K,QAAA,CAAAoJ,MAAA,WAAAC,CAAA;UAAA,OAAAigB,OAAA,CAAA5rB,KAAA,CAAAwM,QAAA,CAAAb,CAAA,CAAA9I,EAAA;QAAA;QACA,IAAArC,SAAA,QAAAA,SAAA;QAAA,IAAAqrB,WAAA,OAAAhhB,2BAAA,CAAA7M,OAAA,EACAoP,GAAA;UAAA0e,OAAA;QAAA;UAAA,KAAAD,WAAA,CAAA/gB,CAAA,MAAAghB,OAAA,GAAAD,WAAA,CAAA9gB,CAAA,IAAAC,IAAA;YAAA,IAAA+gB,CAAA,GAAAD,OAAA,CAAA7oB,KAAA;YACA,KAAAzC,SAAA,CAAA8V,GAAA,WAAA3K,CAAA;cAAA,OAAAA,CAAA,CAAA9I,EAAA;YAAA,GAAA2J,QAAA,CAAAuf,CAAA,CAAAlpB,EAAA;cACA,IAAAkmB,CAAA;gBACAlmB,EAAA,EAAAkpB,CAAA,CAAAlpB,EAAA;gBACAG,KAAA,EAAA+oB,CAAA,CAAAroB,KAAA;gBACA5F,IAAA,EAAAiuB,CAAA,CAAA7gB,QAAA;gBACA8gB,QAAA,EAAAD,CAAA,CAAAE,GAAA;gBACA7a,QAAA,EAAA2a,CAAA,CAAA3a,QAAA;gBACA8a,SAAA,EAAAH,CAAA,CAAAG,SAAA;gBACAf,WAAA,EAAAY,CAAA,CAAAZ,WAAA;gBACAgB,KAAA,EAAAJ,CAAA,CAAAI,KAAA,GAAAJ,CAAA,CAAAI,KAAA,CAAApR,KAAA,MAAAzE,GAAA,WAAA3K,CAAA;kBAAA,OAAA0c,MAAA,CAAA1c,CAAA;gBAAA;cACA;cACA,IAAAygB,WAAA;cACA,IAAAC,SAAA;cACA,IAAAN,CAAA,CAAAO,UAAA;gBACA,IAAAC,cAAA,GAAAR,CAAA,CAAAO,UAAA,CAAA9M,OAAA,YAAAA,OAAA,YAAAA,OAAA;gBACAuJ,CAAA,CAAAwD,cAAA,GAAAA,cAAA,CAAA/M,OAAA;gBACA4M,WAAA,GAAArD,CAAA,CAAAwD,cAAA,CAAAxR,KAAA;gBACA,SAAApP,CAAA,MAAAA,CAAA,GAAAygB,WAAA,CAAApW,MAAA,MAAArK,CAAA;kBACA,IAAAod,EAAA;kBACAA,EAAA,gBAAApd,CAAA;kBACA0gB,SAAA,CAAA7gB,IAAA,CAAAud,EAAA;gBACA;cACA;cACAA,CAAA,CAAAqD,WAAA,GAAAA,WAAA;cACArD,CAAA,CAAAsD,SAAA,GAAAA,SAAA;cACA7rB,SAAA,CAAAgL,IAAA,CAAAud,CAAA;YACA;UACA;QAAA,SAAA5d,GAAA;UAAA0gB,WAAA,CAAAzgB,CAAA,CAAAD,GAAA;QAAA;UAAA0gB,WAAA,CAAAxgB,CAAA;QAAA;QACA7K,SAAA,CAAA0kB,IAAA,WAAA6G,CAAA,EAAAS,CAAA;UAAA,OAAAT,CAAA,CAAAC,QAAA,GAAAQ,CAAA,CAAAR,QAAA;QAAA;QACA,KAAAttB,YAAA;MACA;IACA;IACA+tB,WAAA,WAAAA,YAAA/jB,GAAA;MAAA,IAAAgkB,OAAA;MAAA,WAAAxmB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAAumB,UAAA;QAAA,IAAAptB,cAAA,EAAAC,YAAA,EAAAoD,QAAA;QAAA,WAAAuD,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAAqmB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAAnmB,IAAA,GAAAmmB,UAAA,CAAAlmB,IAAA;YAAA;cACA+lB,OAAA,CAAA7tB,SAAA;cAAAguB,UAAA,CAAAlmB,IAAA;cAAA,OACA,IAAAmmB,wDAAA;gBAAAjqB,EAAA,EAAA6F,GAAA,CAAA5F;cAAA;YAAA;cAAAvD,cAAA,GAAAstB,UAAA,CAAA/lB,IAAA;cACA4lB,OAAA,CAAAntB,cAAA,GAAAA,cAAA;cAAAstB,UAAA,CAAAlmB,IAAA;cAAA,OACA,IAAAomB,0DAAA;gBAAAlqB,EAAA,EAAA6F,GAAA,CAAA7F;cAAA;YAAA;cAAArD,YAAA,GAAAqtB,UAAA,CAAA/lB,IAAA;cACA4lB,OAAA,CAAAltB,YAAA,GAAAA,YAAA;cACAoD,QAAA;gBACAC,EAAA,EAAA6F,GAAA,CAAA7F,EAAA;gBACAC,MAAA,EAAA4F,GAAA,CAAA5F;cACA;cACA4pB,OAAA,CAAA9pB,QAAA,GAAAA,QAAA;YAAA;YAAA;cAAA,OAAAiqB,UAAA,CAAA5lB,IAAA;UAAA;QAAA,GAAA0lB,SAAA;MAAA;IACA;IACAK,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/mB,kBAAA,CAAAlI,OAAA,mBAAAmI,oBAAA,CAAAnI,OAAA,IAAAoI,IAAA,UAAA8mB,UAAA;QAAA,IAAA1tB,YAAA,EAAAoD,QAAA,EAAAkG,GAAA;QAAA,WAAA3C,oBAAA,CAAAnI,OAAA,IAAAuI,IAAA,UAAA4mB,WAAAC,UAAA;UAAA,kBAAAA,UAAA,CAAA1mB,IAAA,GAAA0mB,UAAA,CAAAzmB,IAAA;YAAA;cACAnH,YAAA,GAAAytB,OAAA,CAAAztB,YAAA;cAAA,MACAA,YAAA,IAAAA,YAAA,CAAAwW,MAAA;gBAAAoX,UAAA,CAAAzmB,IAAA;gBAAA;cAAA;cACAsmB,OAAA,CAAAxuB,UAAA;cACAmE,QAAA,GAAAqqB,OAAA,CAAArqB,QAAA;cACAA,QAAA,CAAApD,YAAA,GAAAA,YAAA,CAAAgf,IAAA;cAAA4O,UAAA,CAAAzmB,IAAA;cAAA,OACA,IAAA0mB,kDAAA,EAAAzqB,QAAA;YAAA;cAAAkG,GAAA,GAAAskB,UAAA,CAAAtmB,IAAA;cACAmmB,OAAA,CAAApuB,SAAA;cACAouB,OAAA,CAAAxuB,UAAA;cACAwuB,OAAA,CAAA7V,UAAA;cAAAgW,UAAA,CAAAzmB,IAAA;cAAA;YAAA;cAEAsmB,OAAA,CAAAlW,QAAA;YAAA;YAAA;cAAA,OAAAqW,UAAA,CAAAnmB,IAAA;UAAA;QAAA,GAAAimB,SAAA;MAAA;IAEA;IACAI,sBAAA,WAAAA,uBAAA;MACA,IAAAlpB,QAAA,QAAAL,WAAA,CAAAK,QAAA;MACA,IAAAC,cAAA,QAAAN,WAAA,CAAAM,cAAA;MACA,IAAAjE,wBAAA,QAAAC,4BAAA;MACA,IAAA+D,QAAA;QACAhE,wBAAA,GAAAA,wBAAA,CAAAsL,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAAya,OAAA,CAAAhiB,QAAA,KAAAA,QAAA;QAAA;MACA;MACA,IAAAC,cAAA;QACAjE,wBAAA,GAAAA,wBAAA,CAAAsL,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA4hB,aAAA,KAAAlpB,cAAA;QAAA;MACA;MACA,KAAAjE,wBAAA,GAAAA,wBAAA;IACA;IACAotB,qBAAA,WAAAA,sBAAA;MACA,KAAAptB,wBAAA,QAAAC,4BAAA;MACA,KAAA0D,WAAA,CAAAK,QAAA;MACA,KAAAL,WAAA,CAAAM,cAAA;IACA;IACAopB,mBAAA,WAAAA,oBAAA;MACA,IAAAppB,cAAA,QAAAN,WAAA,CAAAM,cAAA;MACA,IAAAnE,oBAAA,QAAAC,wBAAA;MACA,IAAAkE,cAAA;QACAnE,oBAAA,GAAAA,oBAAA,CAAAwL,MAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA4hB,aAAA,KAAAlpB,cAAA;QAAA;MACA;MACA,KAAAnE,oBAAA,GAAAA,oBAAA;IACA;IACAwtB,kBAAA,WAAAA,mBAAA;MACA,KAAAxtB,oBAAA,QAAAC,wBAAA;MACA,KAAA4D,WAAA,CAAAM,cAAA;IACA;IACAspB,cAAA,WAAAA,eAAA9qB,EAAA;MACA,IAAA+qB,KAAA;MACA,IAAAxgB,GAAA;MACA,IAAAA,GAAA,CAAAZ,QAAA,CAAA3J,EAAA;QACA+qB,KAAA;MACA;MACA,OAAAA,KAAA;IACA;EACA;AACA", "ignoreList": []}]}