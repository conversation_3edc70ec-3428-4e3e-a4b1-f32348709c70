{"remainingRequest": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\src\\views\\software\\softwareDevelopingFormula\\saveOrUpdate.vue", "mtime": 1754031707465}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1744596505042}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1744596505178}, {"path": "C:\\sean\\workspace\\enow_project\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1744596530059}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0U29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgZ2V0U29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgZGVsU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgYWRkU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgdXBkYXRlU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgZXhwb3J0U29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSwNCiAgcXVlcnlTb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhRGljdCwNCiAgcXVlcnlGb3JtdWxhQ2xhc3NpZnlEYXRhLA0KICBnZXRGb3JtdWxhQ2F0ZWdvcnlUcmVlLA0KICBxdWVyeUZvcm11bGFNYXRlcmlhbERhdGEsDQogIGdldEZvcm11bGFMYWJOb0luZm9CeUNvZGUsDQogIHF1ZXJ5Rm9ybXVhbE1hdGVyaWFsUmVjaXBlQ2hhbmdlSGlzdG9yeURhdGEsDQogIHF1ZXJ5Rm9ybXVsYVp4YnpEYXRhTGlzdCwNCiAgcXVlcnlGb3JtdWxhWnhiekRhdGFEZXRhaWwsDQogIGdldFNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGFEZXRhaWwsDQogIGFkZFNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGFTcGVjWnhieiwNCiAgcXVlcnlDaXJIaXN0b3J5RGF0YSwNCiAgZ2V0Q2lyRGF0YVRyZWUsDQogIHF1ZXJ5RHVsaUhpc3RvcnlEYXRhLA0KICBnZXREdWxpRGF0YVRyZWUsDQogIGFkZEZvcm11bGFTcGVjTWF0ZXJpYWxEYXRhLA0KICBhZGRGb3JtdWxhU3ltZEZvcm0sDQogIGdlbmVyYXRlUEZvcm11bGFJbmZvLA0KICBnZW5lcmF0ZUJNYXRlcmlhbEluZm8sDQogIGdlbmVyYXRlTmV3Zm9ybXVsYUluZm8sDQogIGFkZFNvZnR3YXJlRGV2ZWxvcGluZ1VzZXJGb3JtdWxhU3BlY1p4YnosDQogIHF1ZXJ5TWF0ZXJpYWxGb3JtdWxhU3BlY0RhdGFMaXN0LA0KICBxdWVyeU1hdGVyaWFsRm9ybXVsYVNwZWNEYXRhRGV0YWlsLA0KICBhZGRGb3JtdWxhR3lqc0JlaWFuSW5mbywNCiAgcXVlcnlGb3JtdWxhTGVnYWxHeSwNCiAgcXVlcnlGb3JtdWxhU2hhcmVEZXB0RGF0YUxpc3QsDQogIHF1ZXJ5Rm9ybXVsYVNoYXJlRGVwdERhdGFEZXRhaWwsDQogIGFkZEZvcm11bGFTaGFyZURhdGFJbmZvLA0KICBxdWVyeUxvb2tGb3JtdWxhVGFicywNCiAgdXBkYXRlU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYUltZywNCiAgcXVlcnlGb3JtdWxhU3RhYmlsaXR5UmVjb3JkRGF0YUxpc3QsDQogIHVwZGF0ZVNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGFGaWxlSW1nLCBxdWVyeUZvcm11bGFBcHBvaW50TWF0ZXJpYWxEYXRhTGlzdA0KfSBmcm9tICJAL2FwaS9zb2Z0d2FyZS9zb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhIjsNCmltcG9ydCB7Zm9ybXVhbExpc3QsIGZvcm11YWxQcm9qZWN0RGV0YWlsfSBmcm9tICJAL2FwaS9wcm9qZWN0L3Byb2plY3QiOw0KaW1wb3J0IHtnZXRGb3JtdWxhSW5mb0J5Q29kZSwgZ2V0UmF3TWF0ZXJpYWxJbmZvQnlDb2RlfSBmcm9tICJAL2FwaS9zb2Z0d2FyZS9zb2Z0d2FyZU1hdGVyaWFsIjsNCmltcG9ydCB7aXNBcnJheSwgaXNTdHJpbmd9IGZyb20gIkAvdXRpbHMvdmFsaWRhdGUiOw0KaW1wb3J0IHNlbGVjdEZvcm11bGEgZnJvbSAiQC92aWV3cy9zb2Z0d2FyZS9mb3JtdWxhL2NvbXBvbmVudHMvc2VsZWN0Rm9ybXVsYSI7DQppbXBvcnQge2FsbEJjcFRlbXBsYXRlLCBnZXRCY3BUZW1wbGF0ZX0gZnJvbSAiQC9hcGkvcWMvYmNwVGVtcGxhdGUiOw0KaW1wb3J0IHthbGxKY3htfSBmcm9tICJAL2FwaS9xYy9qY3htIjsNCmltcG9ydCB7Y2hlY2tQZXJtaX0gZnJvbSAiQC91dGlscy9wZXJtaXNzaW9uIjsNCmltcG9ydCBTb2Z0d2FyZU1hdGVyaWFsU2F2ZSBmcm9tICJAL3ZpZXdzL3NvZnR3YXJlL3NvZnR3YXJlTWF0ZXJpYWwvc2F2ZSI7DQppbXBvcnQgeyBCYXNlNjQgfSBmcm9tICdqcy1iYXNlNjQnDQppbXBvcnQge3NlbGVjdERpY3RMYWJlbH0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvcnVveWkiOw0KaW1wb3J0IHthbGxUcmVlRGF0YX0gZnJvbSAiQC9hcGkvc3lzdGVtL3RyZWVEYXRhIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYVNhdmUiLA0KICBjb21wb25lbnRzOntzZWxlY3RGb3JtdWxhLFNvZnR3YXJlTWF0ZXJpYWxTYXZlfSwNCiAgcHJvcHM6IHsNCiAgICByZWFkb25seTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlLA0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgd3hPcGVuOmZhbHNlLA0KICAgICAgYWN0aXZlTmFtZTogImJhc2UiLA0KICAgICAgY3VycmVudFRhYjogJ2Jhc2UnLA0KICAgICAgd3hPcHRpb25zOiBbXSwNCiAgICAgIGNvbmNsdXNpb25PZlNhZmV0eUFzc2Vzc21lbnROYW1lOiAiZmlyc3QiLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgIGJ0bkxvYWRpbmc6IGZhbHNlLA0KICAgICAgY2F0ZWdvcnlPcGVuOiBmYWxzZSwNCiAgICAgIGV4cG9ydExvYWRpbmc6IGZhbHNlLA0KICAgICAgZnVsbHNjcmVlbkZsYWc6IGZhbHNlLA0KICAgICAgc2hhcmVPcGVuOiBmYWxzZSwNCiAgICAgIHNwZWNPcGVuOiBmYWxzZSwNCiAgICAgIHRvdGFsUGVyY2VudFZhbDowLA0KICAgICAgc2pUb3RhbFBlcmNldDowLA0KICAgICAgaXNTaG93TWF0ZXJpYWxHb29kc05hbWU6MCwNCiAgICAgIGlzR2VuRm9ybXVsYTowLA0KICAgICAgaXNCTWZvcm11bGE6MCwNCiAgICAgIGFjdGl2ZU5hbWVzOiBbXSwNCiAgICAgIGlkczogW10sDQogICAgICBzdGF0dXNPcHRpb25zOiBbXSwNCiAgICAgIHNoYXJlRGVwdERhdGFzOiBbXSwNCiAgICAgIHNoYXJlRGVwdElkczogW10sDQogICAgICBzcGVjSWQ6IG51bGwsDQogICAgICBvd25lcnNob3BDb21wYW55T3B0aW9uczogW10sDQogICAgICBienh6T3B0aW9uczogW10sDQogICAgICBwcm9qZWN0TGlzdDogW10sDQogICAgICB0eXBlT3B0aW9uczogW10sDQogICAgICBjYXRlZ29yeUxpc3Q6IFtdLA0KICAgICAgenhiekxpc3Q6IFtdLA0KICAgICAgeG1JZHM6IFtdLA0KICAgICAgcmVjaXBlQ2hhbmdlSGlzdG9yeURhdGE6IFtdLA0KICAgICAgZm9ybXVsYVRhYmxlRGF0YUxpc3Q6IFtdLA0KICAgICAgZm9ybXVsYVRhYmxlRGF0YUxpc3RCYWNrOiBbXSwNCiAgICAgIGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdDogW10sDQogICAgICBjb21wb3NpdGlvblRhYmxlRGF0YUxpc3RCYWNrOiBbXSwNCiAgICAgIHNvZnR3YXJlRm9ybXVsYVNwZWNMaXN0OiBbXSwNCiAgICAgIHNwZWNNYXRlcmlhbERhdGFzOiBbXSwNCiAgICAgIGl0ZW1BcnJheTogW10sDQogICAgICB1c2VySXRlbUFycmF5OiBbXSwNCiAgICAgIGd0TnVtU3RyOiAnJywNCiAgICAgIGx0TnVtU3RyOiAnJywNCiAgICAgIGl0ZW1OYW1lczogW10sDQogICAgICB6eGJ6RGV0YWlsOnt9LA0KICAgICAgZ3lqc0RhdGE6e30sDQogICAgICBneWpzRGF0YUxpc3Q6W10sDQogICAgICB6ZnlsRGF0YUxpc3Q6W10sDQogICAgICBneWpzQmVpYW5EYXRhTGlzdDpbXSwNCiAgICAgIHpmeWxCZWlhbkRhdGFMaXN0OltdLA0KICAgICAgc3BlY09iajp7fSwNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIHNob3dTZWFyY2g6IGZhbHNlLA0KICAgICAgdG90YWw6IDAsDQogICAgICBpc0NvcHk6IDAsDQogICAgICBzb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhTGlzdDogW10sDQogICAgICBlZmZpY2FjeU9wdGlvbnM6IFtdLA0KICAgICAgb3RoZXJTcGVjaWFsQ2xhaW1zT3B0aW9uczogW10sDQogICAgICBmb3JtdWxhTWF0ZXJpYWxEYXRhczogW10sDQogICAgICBjaG9vc2VGb3JtdWxhTWF0ZXJpYWxEYXRhczogW10sDQogICAgICBwRm9ybXVsYU1hcERhdGE6IFtdLA0KICAgICAgenlid09wdGlvbnM6IFtdLA0KICAgICAgdGVtcGxhdGVMaXN0OiBbXSwNCiAgICAgIGNwanhPcHRpb25zOiBbXSwNCiAgICAgIHN5cnFPcHRpb25zOiBbXSwNCiAgICAgIHN5ZmZPcHRpb25zOiBbXSwNCiAgICAgIHd4VHJlZTogW10sDQogICAgICBwdXJwb3NlT3B0aW9uczogW10sDQogICAgICBjYXRlZ29yeUFycmF5OiBbXSwNCiAgICAgIGpjWG1MaXN0OiBbXSwNCiAgICAgIHN0YWJpbGl0eURhdGFMaXN0OiBbXSwNCiAgICAgIHJlbGF0aW9uU3RhYmlsaXR5RGF0YUxpc3Q6IFtdLA0KICAgICAgamxPcHRpb25zOiBbDQogICAgICAgIDEwLDUwLDEwMCw1MDAsMTAwMCwNCiAgICAgIF0sDQogICAgICBtak9wdGlvbnM6IFsNCiAgICAgICAgMTAsNTAsMTAwLA0KICAgICAgXSwNCiAgICAgIHBsT3B0aW9uczogW10sDQogICAgICBjaGVja1Jvdzogew0KICAgICAgICBpZDpudWxsLA0KICAgICAgICBkZXB0SWQ6bnVsbA0KICAgICAgfSwNCiAgICAgIGNhdGVnb3J5UHJvcHM6IHsNCiAgICAgICAgbGFiZWw6ICdjYXRlZ29yeU5hbWUnLA0KICAgICAgICB2YWx1ZTogJ2NhdGVnb3J5SWQnDQogICAgICB9LA0KICAgICAgY2lyRGF0YUFycmF5OiBbXSwNCiAgICAgIGlzTG9vazpmYWxzZSwNCiAgICAgIGNpckRhdGFQcm9wczogew0KICAgICAgICBsYWJlbDogJ2xhYmVsJywNCiAgICAgICAgdmFsdWU6ICdpZCcNCiAgICAgIH0sDQogICAgICBkdWxpRGF0YUFycmF5OiBbXSwNCiAgICAgIGNvc21ldGljQ2FzZUZpcnN0T3B0aW9uczogW10sDQogICAgICBjb3NtZXRpY0Nhc2VTZWNvbmRPcHRpb25zOiBbXSwNCiAgICAgIGR1bGlEYXRhUHJvcHM6IHsNCiAgICAgICAgbGFiZWw6ICd6aFR5cGUnLA0KICAgICAgICB2YWx1ZTogJ2lkJw0KICAgICAgfSwNCiAgICAgIGZvcm11bGFUYWJzOlt7DQogICAgICAgIHRpdGxlOifln7rnoYDkv6Hmga8nLA0KICAgICAgICBjb2RlOidiYXNlJw0KICAgICAgfSx7DQogICAgICAgIHRpdGxlOifphY3mlrnpobXpnaInLA0KICAgICAgICBjb2RlOidmb3JtdWxhTWF0ZXJpYWwnDQogICAgICB9LHsNCiAgICAgICAgdGl0bGU6J+mZhOS7ticsDQogICAgICAgIGNvZGU6J2Zvcm11bGFGaWxlJw0KICAgICAgfV0sDQogICAgICB0aXRsZTogIiIsDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIGlzRWRpdDogdHJ1ZSwNCiAgICAgIGNlcnRpZmljYXRpb25PcHRpb25zOltdLA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBsYWJvcmF0b3J5Q29kZTogbnVsbCwNCiAgICAgICAgZm9ybXVsYU5hbWU6IG51bGwsDQogICAgICAgIGV3Z0NvbG9yOiBudWxsLA0KICAgICAgICBjb21jbHVzaW9uVHlwZTpudWxsDQogICAgICB9LA0KICAgICAgZm9ybToge30sDQogICAgICBydWxlczogew0KICAgICAgICBwcm9qZWN0Tm86IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oup6aG555uu57yW56CBJ30sDQogICAgICAgIF0sDQogICAgICAgIGN1c3RvbWVyTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflrqLmiLflkI3np7DkuI3lhYHorrjkuLrnqbonfSwNCiAgICAgICAgXSwNCiAgICAgICAgcHJvZHVjdE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn5Lqn5ZOB5ZCN56ew5LiN5YWB6K645Li656m6J30sDQogICAgICAgIF0sDQogICAgICAgIGNpclRleHQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6K+36YCJ5oupQ0lS5Y6G5Y+y55So6YePJ30sDQogICAgICAgIF0sDQogICAgICAgIGR1bGlUZXh0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+mAieaLqeavkueQhuS9v+eUqOmHj+WPguiAgyd9LA0KICAgICAgICBdLA0KICAgICAgICBsYWJvcmF0b3J5Q29kZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICflrp7pqozlrqTnvJbnoIHkuI3lhYHorrjkuLrnqbonfSwNCiAgICAgICAgXSwNCiAgICAgICAgY2F0ZWdvcnlUZXh0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+mFjeaWueexu+WIq+S4jeWFgeiuuOS4uuepuid9LA0KICAgICAgICBdLA0KICAgICAgICBwZmx4OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+S9v+eUqOaWueazleS4jeWFgeiuuOS4uuepuid9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIHJmbE9wdGlvbnM6Ww0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiczJywNCiAgICAgICAgICBkaWN0TGFiZWw6J+afk+WPkeexuycNCiAgICAgICAgfSwgew0KICAgICAgICAgIGRpY3RWYWx1ZTonMicsDQogICAgICAgICAgZGljdExhYmVsOifpmLLohLHnsbsnDQogICAgICAgIH0sIHsNCiAgICAgICAgICBkaWN0VmFsdWU6JzEnLA0KICAgICAgICAgIGRpY3RMYWJlbDon54Or5Y+R57G7Jw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgeXBseU9wdGlvbnM6Ww0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOicwJywNCiAgICAgICAgICBkaWN0TGFiZWw6J+ajgOa1i+aWueazlScNCiAgICAgICAgfSwgew0KICAgICAgICAgIGRpY3RWYWx1ZTonMicsDQogICAgICAgICAgZGljdExhYmVsOiflrp7pqozlrqTlsI/moLcnDQogICAgICAgIH0sIHsNCiAgICAgICAgICBkaWN0VmFsdWU6JzMnLA0KICAgICAgICAgIGRpY3RMYWJlbDon5Lit6K+V5qC35ZOBJw0KICAgICAgICB9LCB7DQogICAgICAgICAgZGljdFZhbHVlOic0JywNCiAgICAgICAgICBkaWN0TGFiZWw6J+Wkp+i0p+agt+WTgScNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHFpT3B0aW9uczpbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6J+aciemmmeWRsycsDQogICAgICAgICB9LCB7DQogICAgICAgICAgZGljdFZhbHVlOifmnInljp/mlpnnibnlvoHmgKfmsJTlkbMnLA0KICAgICAgICAgfSwgew0KICAgICAgICAgIGRpY3RWYWx1ZTon5peg5ZGzJywNCiAgICAgICAgIH0NCiAgICAgIF0sDQogICAgICBxYm1ibE9wdGlvbnM6Ww0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOicxJywNCiAgICAgICAgICBkaWN0TGFiZWw6J+elm+aWkee+jueZveexuycNCiAgICAgICAgfSwgew0KICAgICAgICAgIGRpY3RWYWx1ZTonMicsDQogICAgICAgICAgZGljdExhYmVsOifnpZvmlpHnvo7nmb3nsbso5LuF5YW354mp55CG6YGu55uW5L2c55SoKScNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIGNvc21ldGljQ2xhc3NpZmljYXRpb25PcHRpb25zOlsNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTonMScsDQogICAgICAgICAgZGljdExhYmVsOifnrKzkuIDnsbvljJblpoblk4EnDQogICAgICAgIH0sIHsNCiAgICAgICAgICBkaWN0VmFsdWU6JzInLA0KICAgICAgICAgIGRpY3RMYWJlbDon56ys5LqM57G75YyW5aaG5ZOBJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgY2FzZU9wdGlvbnM6Ww0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOicxJywNCiAgICAgICAgICBkaWN0TGFiZWw6J+aDheW9ouS4gCcNCiAgICAgICAgfSwgew0KICAgICAgICAgIGRpY3RWYWx1ZTonMicsDQogICAgICAgICAgZGljdExhYmVsOifmg4XlvaLkuownDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICB1c2VPcHRpb25zOlsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOicvJywNCiAgICAgICAgfSwgIHsNCiAgICAgICAgICB2YWx1ZTon5oyH5a6aJywNCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHNwZWNNYXRlcmlhbERhdGFzMTpbDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTon5LqM5rCn5YyW6ZKbJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6J0NJIDc3ODkxJw0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgc3BlY01hdGVyaWFsRGF0YXMyOlsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOifmsKfljJbplIwnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTonQ0kgNzc5NDcnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBmZmp0eGZ4cGdPcHRpb25zOiBbew0KICAgICAgICBkaWN0VmFsdWU6JzAnLA0KICAgICAgICBkaWN0TGFiZWw6J+mrmOmjjumZqScNCiAgICAgIH0sew0KICAgICAgICBkaWN0VmFsdWU6JzEnLA0KICAgICAgICBkaWN0TGFiZWw6J+S4remjjumZqScNCiAgICAgIH0sew0KICAgICAgICBkaWN0VmFsdWU6JzInLA0KICAgICAgICBkaWN0TGFiZWw6J+S9jumjjumZqScNCiAgICAgIH0sew0KICAgICAgICBkaWN0VmFsdWU6JzMnLA0KICAgICAgICBkaWN0TGFiZWw6J+aXoOmjjumZqScNCiAgICAgIH0sew0KICAgICAgICBkaWN0VmFsdWU6JzQnLA0KICAgICAgICBkaWN0TGFiZWw6J+a1i+ivleayoemAmui/hycNCiAgICAgIH1dLA0KICAgICAgd2R4T3B0aW9uczogW3sNCiAgICAgICAgZGljdFZhbHVlOicwJywNCiAgICAgICAgZGljdExhYmVsOifov5vooYzkuK0nDQogICAgICB9LHsNCiAgICAgICAgZGljdFZhbHVlOicxJywNCiAgICAgICAgZGljdExhYmVsOifmtYvor5XpgJrov4cnDQogICAgICB9LHsNCiAgICAgICAgZGljdFZhbHVlOicyJywNCiAgICAgICAgZGljdExhYmVsOifmtYvor5XlpLHotKUnDQogICAgICB9LHsNCiAgICAgICAgZGljdFZhbHVlOiczJywNCiAgICAgICAgZGljdExhYmVsOifmnaHku7bmjqXlj5cnDQogICAgICB9XSwNCiAgICAgIHlwRnJvbU9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAn5a6e6aqM5a6kJyx2YWx1ZTogMH0sDQogICAgICAgIHtsYWJlbDogJ+S4reivlScsdmFsdWU6IDF9LA0KICAgICAgICB7bGFiZWw6ICfnlJ/kuqcnLHZhbHVlOiAyfSwNCiAgICAgICAge2xhYmVsOiAn55Sf5oqA5aSN5qC3Jyx2YWx1ZTogM30sDQogICAgICBdLA0KICAgICAgc3RhYmlsaXR5U3RhdHVzT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICfov5vooYzkuK0nLHZhbHVlOiAwfSwNCiAgICAgICAge2xhYmVsOiAn5rWL6K+V6YCa6L+HJyx2YWx1ZTogMX0sDQogICAgICAgIHtsYWJlbDogJ+a1i+ivleWksei0pScsdmFsdWU6IDJ9LA0KICAgICAgICB7bGFiZWw6ICfmnaHku7bmjqXlj5cnLHZhbHVlOiAzfSwNCiAgICAgIF0sDQogICAgfTsNCiAgfSwNCiAgYXN5bmMgY3JlYXRlZCgpIHsNCiAgICB0aGlzLnd4VHJlZSA9IHRoaXMudG9UcmVlKHRoaXMud3hPcHRpb25zLCAwKQ0KDQogICAgLy/kvb/nlKjnm67nmoQNCiAgICBsZXQgY2VydGlmaWNhdGlvblJlcyA9IGF3YWl0IHRoaXMuZ2V0RGljdHMoIlNPRlRXQVJFX0NFUlRJRklDQVRJT04iKQ0KICAgIHRoaXMuY2VydGlmaWNhdGlvbk9wdGlvbnMgPSBjZXJ0aWZpY2F0aW9uUmVzLmRhdGENCg0KICAgIHRoaXMuZ2V0RGljdHMoInFjX2p5cGwiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMucGxPcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgIH0pDQogIH0sDQogIHdhdGNoOiB7DQogICAgIiRyb3V0ZS5xdWVyeS5wYXJhbXMiOiB7DQogICAgICBpbW1lZGlhdGU6IHRydWUsDQogICAgICBoYW5kbGVyKCkgew0KICAgICAgICBsZXQgcGFyYW1zID0gdGhpcy4kcm91dGUucXVlcnkucGFyYW1zOw0KICAgICAgICBpZihwYXJhbXMpIHsNCiAgICAgICAgICBsZXQgcXVlcnkgPSBCYXNlNjQuZGVjb2RlKEJhc2U2NC5kZWNvZGUocGFyYW1zKSk7DQogICAgICAgICAgaWYocXVlcnkpew0KICAgICAgICAgICAgcXVlcnkgPSBKU09OLnBhcnNlKHF1ZXJ5KTsNCiAgICAgICAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgICAgICAgIHRoaXMuaW5pdCgxKTsNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlVXBkYXRlKHF1ZXJ5LmlkLHF1ZXJ5LnNoYXJlVHlwZT09PTE/dHJ1ZTpmYWxzZSk7DQogICAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgICAgdGhpcy50aXRsZSA9IHF1ZXJ5LnNoYXJlVHlwZT09PTE/J+S/ruaUuemFjeaWuSc6J+afpeeci+mFjeaWuSc7DQogICAgICAgICAgfQ0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICB0aGlzLnJlc2V0KCk7DQogICAgICAgICAgdGhpcy5pbml0KDIpOw0KICAgICAgICAgIHRoaXMucXVlcnlQcm9qZWN0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuaGFuZGxlQWRkKCk7DQogICAgICAgICAgdGhpcy5pc0xvb2sgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9LA0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGFzeW5jIHNob3dXeCgpIHsNCiAgICAgIHRoaXMud3hPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgc2VsZWN0RGljdExhYmVsLA0KICAgIGFzeW5jIGRlc2lnbmF0ZUNoYW5nZShyb3cpew0KICAgICAgIGxldCBkZXNpZ25hdGVkVXNlID0gcm93LmRlc2lnbmF0ZWRVc2U7DQogICAgICAgaWYoJ+aMh+Wumic9PT1kZXNpZ25hdGVkVXNlKXsgIC8v5oyH5a6a5Y6f5paZDQogICAgICAgICAgbGV0IG1hdGVyaWFsSWQgPSByb3cubWF0ZXJpYWxJZDsNCiAgICAgICAgICBsZXQgcmVzID0gYXdhaXQgcXVlcnlGb3JtdWxhQXBwb2ludE1hdGVyaWFsRGF0YUxpc3Qoe2lkOm1hdGVyaWFsSWR9KTsNCiAgICAgICAgICByb3cubWF0ZXJpYWxDb2RlcyA9IHJlczsNCiAgICAgICB9ZWxzZXsNCiAgICAgICAgICByb3cuYXBwb2ludENvZGUgPSAnJzsNCiAgICAgICAgICByb3cubWF0ZXJpYWxDb2RlcyA9IFtdOw0KICAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHF1ZXJ5UHJvamVjdExpc3QoKXsNCiAgICAgIGxldCBwcm9qZWN0TGlzdCAgPSBhd2FpdCBmb3JtdWFsTGlzdCgpOw0KICAgICAgdGhpcy5wcm9qZWN0TGlzdCA9IHByb2plY3RMaXN0Ow0KICAgIH0sDQogICAgYXN5bmMgaW5pdCh0eXBlKXsNCiAgICAgIGlmKGNoZWNrUGVybWkoWydzb2Z0d2FyZTpzb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhOmxvb2tNYXRlcmlhbEdvb2RzTmFtZSddKSkgew0KICAgICAgICB0aGlzLmlzU2hvd01hdGVyaWFsR29vZHNOYW1lID0gMTsNCiAgICAgIH1lbHNlew0KICAgICAgICB0aGlzLmlzU2hvd01hdGVyaWFsR29vZHNOYW1lID0gMDsNCiAgICAgIH0NCiAgICAgIGlmKGNoZWNrUGVybWkoWydzb2Z0d2FyZTpzb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhOmdlblBmb3JtdWxhSW5mbyddKSkgew0KICAgICAgICB0aGlzLmlzR2VuRm9ybXVsYSA9IDE7DQogICAgICB9ZWxzZXsNCiAgICAgICAgdGhpcy5pc0dlbkZvcm11bGEgPSAwOw0KICAgICAgfQ0KICAgICAgaWYoY2hlY2tQZXJtaShbJ3NvZnR3YXJlOnNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGE6Z2VuQk1Gb3JtdWxhSW5mbyddKSkgew0KICAgICAgICB0aGlzLmlzQk1mb3JtdWxhID0gMTsNCiAgICAgIH1lbHNlew0KICAgICAgICB0aGlzLmlzQk1mb3JtdWxhID0gMDsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0RGljdHMoIlpYQlpfU1RBVFVTIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuc3RhdHVzT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KQ0KICAgICAgdGhpcy5nZXREaWN0cygiT1dORVJTSE9QX0NPTVBBTlkiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vd25lcnNob3BDb21wYW55T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KQ0KICAgICAgdGhpcy5nZXREaWN0cygiQlpYWiIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmJ6eHpPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgIH0pDQogICAgICB0aGlzLmdldERpY3RzKCJyZF95c3R5X3R5cGUiKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50eXBlT3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KQ0KICAgICAgdGhpcy5nZXREaWN0cygiU09GVFdBUkVfRk9STVVMQV9DQVNFMSIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNvc21ldGljQ2FzZUZpcnN0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KQ0KICAgICAgdGhpcy5nZXREaWN0cygiU09GVFdBUkVfRk9STVVMQV9DQVNFMiIpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNvc21ldGljQ2FzZVNlY29uZE9wdGlvbnMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgfSkNCiAgICAgIGNvbnN0IGNhdGVnb3J5U2V0ID0gbmV3IFNldCgpDQogICAgICBjb25zdCBqY1htTGlzdCA9IGF3YWl0IGFsbEpjeG0oe3R5cGU6MX0pDQogICAgICB0aGlzLmpjWG1MaXN0ID0gamNYbUxpc3QNCiAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBqY1htTGlzdCkgew0KICAgICAgICBjYXRlZ29yeVNldC5hZGQoaXRlbS5jYXRlZ29yeSkNCiAgICAgIH0NCiAgICAgIGNvbnN0IGNhdGVnb3J5TGlzdCA9IFtdDQogICAgICBmb3IgKGNvbnN0IGNhdGVnb3J5IG9mIGNhdGVnb3J5U2V0KSB7DQogICAgICAgIGNhdGVnb3J5TGlzdC5wdXNoKHsNCiAgICAgICAgICBjYXRlZ29yeSwNCiAgICAgICAgICBhcnJheTogamNYbUxpc3QuZmlsdGVyKGk9PmkuY2F0ZWdvcnkgPT09IGNhdGVnb3J5KSwNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHRoaXMuY2F0ZWdvcnlMaXN0ID0gY2F0ZWdvcnlMaXN0LmZpbHRlcihpPT5pLmNhdGVnb3J5ICE9PSAn5b6u55Sf54mpJykNCiAgICAgIHRoaXMudGVtcGxhdGVMaXN0ID0gYXdhaXQgYWxsQmNwVGVtcGxhdGUoKTsNCiAgICAgIC8v6YWN5pa55L2/55So55So6YCUDQogICAgICBsZXQgcHVycG9zZVJlcyA9ICBhd2FpdCB0aGlzLmdldERpY3RzKCJTT0ZUV0FSRV9GT1JNVUxBX1BVUlBPU0UiKQ0KICAgICAgdGhpcy5wdXJwb3NlT3B0aW9ucyA9IHB1cnBvc2VSZXMuZGF0YQ0KICAgICAgLy/ojrflj5blrZflhbjmlbDmja4NCiAgICAgIGxldCBkaWN0T2JqID0gYXdhaXQgcXVlcnlTb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhRGljdCgpOw0KICAgICAgdGhpcy5lZmZpY2FjeU9wdGlvbnMgPSBkaWN0T2JqLkdYWENfREFUQV9MSVNUOw0KICAgICAgdGhpcy5vdGhlclNwZWNpYWxDbGFpbXNPcHRpb25zID0gZGljdE9iai5HWFhDX0RBVEFfTElTVF9PVEhFUjsNCiAgICAgIHRoaXMuenlid09wdGlvbnMgPSBkaWN0T2JqLlpZQldfREFUQV9MSVNUOw0KICAgICAgdGhpcy5jcGp4T3B0aW9ucyA9IGRpY3RPYmouQ1BKWF9EQVRBX0xJU1Q7DQogICAgICB0aGlzLnN5cnFPcHRpb25zID0gZGljdE9iai5TWVJRX0RBVEFfTElTVDsNCiAgICAgIHRoaXMuc3lmZk9wdGlvbnMgPSBkaWN0T2JqLlBGTFhfREFUQV9MSVNUOw0KICAgICAgbGV0IGNhdGVnb3J5QWxsQXJyYXkgPSBhd2FpdCBxdWVyeUZvcm11bGFDbGFzc2lmeURhdGEoKQ0KICAgICAgaWYodHlwZT09Mil7DQogICAgICAgIGxldCBkYXRhcyA9IFsyMiwyMywyNCwyNSwyNiwyNyw0Myw1MCw2MV07DQogICAgICAgIGNhdGVnb3J5QWxsQXJyYXkgPSBjYXRlZ29yeUFsbEFycmF5LmZpbHRlcihpPT4hZGF0YXMuaW5jbHVkZXMoaS5jYXRlZ29yeUlkKSk7DQogICAgICB9DQogICAgICB0aGlzLmNhdGVnb3J5QXJyYXkgPSBhd2FpdCBnZXRGb3JtdWxhQ2F0ZWdvcnlUcmVlKGNhdGVnb3J5QWxsQXJyYXkpDQogICAgICBsZXQgenhiekxpc3QgPSBhd2FpdCBxdWVyeUZvcm11bGFaeGJ6RGF0YUxpc3QoKTsNCiAgICAgIHRoaXMuenhiekxpc3QgPSB6eGJ6TGlzdDsNCiAgICAgIC8v6I635Y+WY2ly5Y6G5Y+y5L2/55So6YePDQogICAgICBsZXQgY2lyRGF0YUFsbEFycmF5ID0gYXdhaXQgcXVlcnlDaXJIaXN0b3J5RGF0YSgpOw0KICAgICAgdGhpcy5jaXJEYXRhQXJyYXkgPSBhd2FpdCBnZXRDaXJEYXRhVHJlZShjaXJEYXRhQWxsQXJyYXkpOw0KICAgICAgLy/ojrflj5bmr5LnkIYv5L6b5bqU5ZWG5L2/55So6YeP5Y+C6ICDDQogICAgICBsZXQgZHVsaURhdGFBbGxBcnJheSA9IGF3YWl0IHF1ZXJ5RHVsaUhpc3RvcnlEYXRhKCk7DQogICAgICB0aGlzLmR1bGlEYXRhQXJyYXkgPSBhd2FpdCBnZXREdWxpRGF0YVRyZWUoZHVsaURhdGFBbGxBcnJheSk7DQogICAgfSwNCiAgICBhc3luYyBpdGVtTmFtZUNoYW5nZShpdGVtTmFtZSl7DQogICAgICBsZXQgaXRlbU5hbWVzID0gdGhpcy5pdGVtTmFtZXM7DQogICAgICBsZXQgYXJyID0gaXRlbU5hbWVzLmZpbHRlcihpPT4gaS5pZCA9PT0gaXRlbU5hbWUpDQogICAgICBsZXQgaXRlbU5hbWVUZXh0ID0gJyc7DQogICAgICBpZihhcnIgJiYgYXJyWzBdKSB7DQogICAgICAgIGl0ZW1OYW1lVGV4dCA9IGFyclswXS50ZXh0Ow0KICAgICAgfQ0KICAgICAgIHRoaXMuZm9ybS5pdGVtTmFtZVRleHQgPSBpdGVtTmFtZVRleHQ7DQogICAgIH0sDQogICAgYXN5bmMgcHJvamVjdENoYW5nZShwcm9qZWN0Tm8pIHsNCiAgICAgIC8v6I635Y+W6aG555uu6K+m5oOFDQogICAgICBsZXQgcHJvamVjdERldGFpbCA9IGF3YWl0IGZvcm11YWxQcm9qZWN0RGV0YWlsKHtwcm9qZWN0Tm99KTsNCiAgICAgIGxldCBpc0VkaXQgPSB0cnVlOw0KICAgICAgaWYocHJvamVjdE5vLmluZGV4T2YoIlAiKSE9LTEgfHwgcHJvamVjdE5vPT0nMjEwMDAyMDg5JyB8fHByb2plY3RObz09JzI0MDAwMDM2NSd8fHByb2plY3RObz09JzI0MDAwMTA0MicgfHwgcHJvamVjdE5vPT0nMjEwMDAyMDg4JyB8fCBwcm9qZWN0Tm89PScyMjAwMDU0NTcnIHx8IHByb2plY3RObz09JzI0MDAwMDM2NScpew0KICAgICAgICAgaXNFZGl0ID0gZmFsc2U7DQogICAgICB9DQogICAgICB0aGlzLmlzRWRpdCA9IGlzRWRpdDsNCiAgICAgIHRoaXMuZm9ybS5pdGVtTmFtZSA9ICcnOw0KICAgICAgdGhpcy5mb3JtLml0ZW1OYW1lVGV4dCA9ICcnOw0KICAgICAgdGhpcy5pdGVtTmFtZXMgPSBbXTsNCiAgICAgIGlmKHByb2plY3REZXRhaWwhPW51bGwgJiYgcHJvamVjdERldGFpbC5pZCl7DQogICAgICAgIHRoaXMuZm9ybS5jdXN0b21lck5hbWUgPSBwcm9qZWN0RGV0YWlsLmN1c3RvbWVyTmFtZTsNCiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3ROYW1lID0gcHJvamVjdERldGFpbC5wcm9kdWN0TmFtZTsNCiAgICAgICAgdGhpcy5mb3JtLmJyYW5kTmFtZSA9IHByb2plY3REZXRhaWwuYnJhbmROYW1lOw0KICAgICAgICB0aGlzLmZvcm0uc2VyaWVzTmFtZSA9IHByb2plY3REZXRhaWwuc2VyaWVzTmFtZTsNCiAgICAgICAgbGV0IGl0ZW1OYW1lcyA9IHByb2plY3REZXRhaWwuaXRlbU5hbWVzOw0KICAgICAgICBpZihpdGVtTmFtZXMpew0KICAgICAgICAgIGl0ZW1OYW1lcyA9IEpTT04ucGFyc2UoaXRlbU5hbWVzKTsNCiAgICAgICAgICB0aGlzLml0ZW1OYW1lcyA9IGl0ZW1OYW1lczsNCiAgICAgICAgfQ0KICAgICAgIH1lbHNlew0KICAgICAgICB0aGlzLmZvcm0uY3VzdG9tZXJOYW1lID0gJyc7DQogICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0TmFtZSA9ICcnOw0KICAgICAgICB0aGlzLmZvcm0uYnJhbmROYW1lID0gJyc7DQogICAgICAgIHRoaXMuZm9ybS5zZXJpZXNOYW1lID0gJyc7DQogICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgenhiekNoYW5nZShpZCl7DQogICAgICBsZXQgenhiekRldGFpbCA9IGF3YWl0IHF1ZXJ5Rm9ybXVsYVp4YnpEYXRhRGV0YWlsKHtpZH0pOw0KICAgICAgdGhpcy5mb3JtLmV4ZWNOdW1iZXIgPSB6eGJ6RGV0YWlsLnp4YnpoOw0KICAgICAgdGhpcy56eGJ6RGV0YWlsID0genhiekRldGFpbDsNCiAgICB9LA0KICAgIGFzeW5jIGdldExpc3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0gT2JqZWN0LmFzc2lnbih7fSx0aGlzLnF1ZXJ5UGFyYW1zKQ0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGV0IHJlcyA9IGF3YWl0IGxpc3RTb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhKHBhcmFtcykNCiAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB0aGlzLnNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGFMaXN0ID0gcmVzLnJvd3MNCiAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWwNCiAgICB9LA0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgYXN5bmMgcmVzZXQoKSB7DQogICAgICB0aGlzLmN1cnJlbnRUYWIgPSAnYmFzZSc7DQogICAgICB0aGlzLnN0YWJpbGl0eURhdGFMaXN0ID0gW107DQogICAgICB0aGlzLnJlbGF0aW9uU3RhYmlsaXR5RGF0YUxpc3QgPSBbXTsNCiAgICAgIHRoaXMuYWN0aXZlTmFtZXMgPSBbXTsNCiAgICAgIHRoaXMuaXNDb3B5ID0gMDsNCiAgICAgIHRoaXMuaXNMb29rID0gZmFsc2U7DQogICAgICB0aGlzLmFjdGl2ZU5hbWUgPSAnYmFzZSc7DQogICAgICB0aGlzLml0ZW1OYW1lcyA9IFtdOw0KICAgICAgdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IFtdOw0KICAgICAgdGhpcy5jaG9vc2VGb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IFtdOw0KICAgICAgdGhpcy5wRm9ybXVsYU1hcERhdGEgPSBbXTsNCiAgICAgIHRoaXMucmVjaXBlQ2hhbmdlSGlzdG9yeURhdGEgPSBbXTsNCiAgICAgIHRoaXMuZ3lqc0RhdGEgPSB7fTsNCiAgICAgIHRoaXMuZ3lqc0RhdGFMaXN0ID0gW107DQogICAgICB0aGlzLnpmeWxEYXRhTGlzdCA9IFtdOw0KICAgICAgdGhpcy5neWpzQmVpYW5EYXRhTGlzdCA9IFtdOw0KICAgICAgdGhpcy56ZnlsQmVpYW5EYXRhTGlzdCA9IFtdOw0KICAgICAgdGhpcy5zb2Z0d2FyZUZvcm11bGFTcGVjTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtdWxhVGFibGVEYXRhTGlzdCA9IFtdOw0KICAgICAgdGhpcy5mb3JtdWxhVGFibGVEYXRhTGlzdEJhY2sgPSBbXTsNCiAgICAgIHRoaXMuY29tcG9zaXRpb25UYWJsZURhdGFMaXN0ID0gW107DQogICAgICB0aGlzLmNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdEJhY2sgPSBbXTsNCiAgICAgIHRoaXMuc3BlY01hdGVyaWFsRGF0YXMgPSBbXTsNCiAgICAgIHRoaXMuaXRlbUFycmF5ID0gW107DQogICAgICB0aGlzLnVzZXJJdGVtQXJyYXkgPSBbXTsNCiAgICAgIHRoaXMuenhiekRldGFpbCA9IHt9Ow0KICAgICAgdGhpcy5ndE51bVN0ciA9ICcnOw0KICAgICAgdGhpcy5sdE51bVN0ciA9ICcnOw0KICAgICAgdGhpcy5zcGVjT2JqID0ge307DQogICAgICB0aGlzLnRvdGFsUGVyY2VudFZhbCA9IDA7DQogICAgICB0aGlzLnNwZWNJZCA9IG51bGw7DQogICAgICB0aGlzLnNqVG90YWxQZXJjZXQgPSAwOw0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgY3VycmVudFRlbXBsYXRlSWQ6IG51bGwsDQogICAgICAgIHJlbGF0aW9uTWF0ZXJpYWxDb2RlOiBudWxsLA0KICAgICAgICBmb3JtdWxhTmFtZTogbnVsbCwNCiAgICAgICAgZW5nbGlzaE5hbWU6IG51bGwsDQogICAgICAgIG1hdGVyaWFsQ29kZTogbnVsbCwNCiAgICAgICAgZm9ybXVsYUNvZGVQYXJhbXM6IG51bGwsDQogICAgICAgIHByaWNlOiBudWxsLA0KICAgICAgICB3ZWlnaHQ6IDEwMCwNCiAgICAgICAgaXNMb2NrOiAxLA0KICAgICAgICBpc01hdGVyYWw6IG51bGwsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIG9wZXJhdG9yOiBudWxsLA0KICAgICAgICBjcmVhdGVkVGltZTogbnVsbCwNCiAgICAgICAgaXNEZWw6IG51bGwsDQogICAgICAgIGxhc3RNb2RpZmllZFRpbWU6IG51bGwsDQogICAgICAgIG5vdGU6IG51bGwsDQogICAgICAgIGZvcm11bGFDb2RlOiBudWxsLA0KICAgICAgICBkdWxpSWQ6IG51bGwsDQogICAgICAgIGNpcklkOiBudWxsLA0KICAgICAgICBjaXJUZXh0OiBudWxsLA0KICAgICAgICBkdWxpVGV4dDogbnVsbCwNCiAgICAgICAgbGFib3JhdG9yeUNvZGU6IG51bGwsDQogICAgICAgIHByb2R1Y3ROYW1lOiBudWxsLA0KICAgICAgICBicmFuZElkOiBudWxsLA0KICAgICAgICBjdXN0b21lckNvZGU6IG51bGwsDQogICAgICAgIGN1c3RvbWVyTmFtZTogbnVsbCwNCiAgICAgICAgc2VyaWVzTmFtZTogbnVsbCwNCiAgICAgICAgYXBwZWFyYW5jZTogbnVsbCwNCiAgICAgICAgY29sb3VyOiBudWxsLA0KICAgICAgICBwaDogbnVsbCwNCiAgICAgICAgdmlzY29zaXR5OiBudWxsLA0KICAgICAgICBzdGFiaWxpdHlyZXN1bHQ6IG51bGwsDQogICAgICAgIGd4Z3M6IG51bGwsDQogICAgICAgIGNhdGVnb3J5OiBudWxsLA0KICAgICAgICBicmFuZE5hbWU6IG51bGwsDQogICAgICAgIHN0YW5kYXJkOiBudWxsLA0KICAgICAgICBpbnRyb0ZpbGU6IFtdLA0KICAgICAgICBvcmdhbml6YXRpb25JZDogbnVsbCwNCiAgICAgICAgb2xkRm9ybXVsYUNvZGU6IG51bGwsDQogICAgICAgIGNvcHlGb3JtdWxhSWQ6IG51bGwsDQogICAgICAgIGFkZFRpcHM6IG51bGwsDQogICAgICAgIHdlbmRpbmd4aW5nRmlsZTogW10sDQogICAgICAgIGdvbmd5aUZpbGU6IFtdLA0KICAgICAgICB4aWFuZ3Jvbmd4aW5nRmlsZTogW10sDQogICAgICAgIHdlaXNoZW53dUZpbGU6IFtdLA0KICAgICAgICB4aWFvZmVpemhlRmlsZTogW10sDQogICAgICAgIHFpdGFGaWxlOiBbXSwNCiAgICAgICAgZXhlY051bWJlcjogbnVsbCwNCiAgICAgICAgaXNEcmFmdDogMSwNCiAgICAgICAgZ3h4YzogW10sDQogICAgICAgIGd4eGNPdGhlcjogW10sDQogICAgICAgIHp5Ync6IFtdLA0KICAgICAgICBzeXJxOiBbXSwNCiAgICAgICAgY3BqeDogW10sDQogICAgICAgIHBmbHg6IFtdLA0KICAgICAgICBjcGZsZG06IG51bGwsDQogICAgICAgIGNvc21ldGljQ2xhc3NpZmljYXRpb246IG51bGwsDQogICAgICAgIGNvc21ldGljQ2FzZTogbnVsbCwNCiAgICAgICAgZXhlY051bWJlcklkOiBudWxsLA0KICAgICAgICBhcXBnamw6IG51bGwsDQogICAgICAgIGdvbmd5aWppYW5zaHU6IG51bGwsDQogICAgICAgIGdvbmd5aWppYW5zaHVCZWlhbjogbnVsbCwNCiAgICAgICAgcmFuZmFsZWk6IFtdLA0KICAgICAgICBjb3NtZXRpY0Nhc2VGaXJzdDogW10sDQogICAgICAgIGNvc21ldGljQ2FzZVNlY29uZDogW10sDQogICAgICAgIHF1YmFubWVpYmFpbGVpOiBbXSwNCiAgICAgICAgZmFuZ3NoYWlsZWk6IGZhbHNlLA0KICAgICAgICBzZmE6IG51bGwsDQogICAgICAgIHBhOiBudWxsLA0KICAgICAgICB5dXNob3VzZmE6IG51bGwsDQogICAgICAgIHhpbmdvbmd4aWFvOiBmYWxzZSwNCiAgICAgICAgeGluZ29uZ3hpYW9jb250ZW50OiBudWxsLA0KICAgICAgICBmdGxUaW1lOiBudWxsLA0KICAgICAgICBmaWxDb2RlOiBudWxsLA0KICAgICAgICBiYUNvZGU6IG51bGwsDQogICAgICAgIGJhVGltZTogbnVsbCwNCiAgICAgICAgZmlsQ29kZU5vdGU6IG51bGwsDQogICAgICAgIGJhQ29kZU5vdGU6IG51bGwsDQogICAgICAgIHdheGNOYW1lOiBudWxsLA0KICAgICAgICB3YXhjT3RoZXJuYW1lOiBudWxsLA0KICAgICAgICB3YXhjU3RhdHVzOiBudWxsLA0KICAgICAgICBiYVN0YXR1czogbnVsbCwNCiAgICAgICAgZm9ybXVsYVBpZDogbnVsbCwNCiAgICAgICAgYnBOb3RlOiBudWxsLA0KICAgICAgICB6c1RpbWU6IG51bGwsDQogICAgICAgIHpzQ29kZTogbnVsbCwNCiAgICAgICAgZ29uZ3lpamlhbnNodVpzOiBudWxsLA0KICAgICAgICB5ZkZpbGU6IG51bGwsDQogICAgICAgIHpzRmlsZTogbnVsbCwNCiAgICAgICAgaXNMb3ZlOiBudWxsLA0KICAgICAgICB1cFJhdGU6IG51bGwsDQogICAgICAgIG9yaVByaWNlOiBudWxsLA0KICAgICAgICBsZXZlbE51bTogbnVsbCwNCiAgICAgICAgcHVycG9zZTogJ+aZrumAmicsDQogICAgICAgIGZvcm11bGFTdGF0dXM6IDAsDQogICAgICAgIGZvcm11bGFSZW1hcms6IG51bGwsDQogICAgICAgIHByb2plY3RObzogbnVsbCwNCiAgICAgICAgaXRlbU5hbWU6IG51bGwsDQogICAgICAgIGl0ZW1OYW1lVGV4dDogbnVsbCwNCiAgICAgICAgZm9ybXVsYUltYWdlOiBudWxsLA0KICAgICAgICBmb3JtdWxhQ29uc3RydWN0aW9uSWRlYXM6IG51bGwsDQogICAgICAgIGlzUmVhbHNlOiBudWxsLA0KICAgICAgICBtYXRlcmlhbFN0YXR1c0luZm86IG51bGwsDQogICAgICAgIGltcG9ydENvdW50cnlJbmZvOiBudWxsLA0KICAgICAgICBvcGVyYXRvck5hbWU6IG51bGwsDQogICAgICAgIHN0YWJpbGl0eVN0YXR1czogbnVsbCwNCiAgICAgICAgaXNSZXN1bHQ6IG51bGwsDQogICAgICAgIGlzR3Q6IG51bGwsDQogICAgICAgIHR5cGU6IG51bGwsDQogICAgICAgIHdlaXNoZW53dVJlc3VsdDogbnVsbCwNCiAgICAgICAgd2Vpc2hlbnd1UmVtYXJrOiBudWxsLA0KICAgICAgICB4aWFuZ3Jvbmd4aW5nUmVzdWx0OiBudWxsLA0KICAgICAgICB4aWFuZ3Jvbmd4aW5nUmVtYXJrOiBudWxsLA0KICAgICAgICB3ZW5kaW5neGluZ1Jlc3VsdDogbnVsbCwNCiAgICAgICAgd2VuZGluZ3hpbmdSZW1hcms6IG51bGwsDQogICAgICAgIG1hdGVyaWFsQ3ljbGU6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KDQogICAgICBpZiAoIXRoaXMud3hPcHRpb25zLmxlbmd0aCkgew0KICAgICAgICB0aGlzLnd4T3B0aW9ucyA9IGF3YWl0IGFsbFRyZWVEYXRhKHt0eXBlOiA5fSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0uaWQpDQogICAgfSwNCiAgICBoYW5kbGVGb3JtdWxhTWF0ZXJpYWxTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmNob29zZUZvcm11bGFNYXRlcmlhbERhdGFzID0gc2VsZWN0aW9uOw0KICAgIH0sDQogICAgYXN5bmMgZ2VuUGZvcm11bGFJbmZvKCkgew0KICAgICAgbGV0IGNob29zZUZvcm11bGFNYXRlcmlhbERhdGFzID0gdGhpcy5jaG9vc2VGb3JtdWxhTWF0ZXJpYWxEYXRhczsNCiAgICAgIGlmIChjaG9vc2VGb3JtdWxhTWF0ZXJpYWxEYXRhcyAmJiBjaG9vc2VGb3JtdWxhTWF0ZXJpYWxEYXRhcy5sZW5ndGggPiAwKSB7DQogICAgICAgIGZvciAobGV0IGl0ZW0gb2YgY2hvb3NlRm9ybXVsYU1hdGVyaWFsRGF0YXMpIHsNCiAgICAgICAgICBsZXQgdHlwZSA9IGl0ZW0udHlwZTsNCiAgICAgICAgICBpZiAodHlwZSA9PSAxKSB7DQogICAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfnlJ/miJDplJnor68s6K+36YCJ5oup5Y6f5paZ57yW56CB5L+h5oGvJyk7DQogICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGxldCBpZCA9IHRoaXMuZm9ybS5pZDsNCiAgICAgICAgbGV0IGRhdGEgPSBhd2FpdCBnZW5lcmF0ZVBGb3JtdWxhSW5mbyh7aWQsZm9ybXVsYU1hdGVyaWFsRGF0YXM6SlNPTi5zdHJpbmdpZnkoY2hvb3NlRm9ybXVsYU1hdGVyaWFsRGF0YXMpfSk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nljp/mlpnkv6Hmga8hJyk7DQogICAgICB9DQogICAgfSwNCiAgICBsaW1pdERlY2ltYWwocm93KXsNCiAgICAgICBjb25zdCBpbnB1dFZhbHVlID0gcm93LnBlcmNlbnRhZ2U7DQogICAgICAvLyDmraPliJnooajovr7lvI/ljLnphY3mnIDlpJo25L2N5bCP5pWw55qE5pWw5a2XDQogICAgICBjb25zdCByZWdleCA9IC9eXGQqXC4/XGR7MCw2fSQvOw0KICAgICAgaWYgKHJlZ2V4LnRlc3QoaW5wdXRWYWx1ZSkpIHsNCiAgICAgICAgdGhpcy5sYXN0VmFsdWUgPSBpbnB1dFZhbHVlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5LiN56ym5ZCI5p2h5Lu277yM5Zue6YCA5Yiw5LiK5LiA5Liq5pyJ5pWI5YC8DQogICAgICAgIHJvdy5wZXJjZW50YWdlID0gdGhpcy5sYXN0VmFsdWU7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBnZW5lckJNYXRlcmlhbEluZm8oaWQpIHsNCiAgICAgICB0aGlzLiRjb25maXJtKCfmgqjnoa7lrpropoHnlJ/miJBC5Luj56CB5ZCXPycsICLmj5DnpLoiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJpbmZvIg0KICAgICAgfSkudGhlbihhc3luYyBmdW5jdGlvbiAoKSB7DQogICAgICAgICBsZXQgZGF0YSA9IGF3YWl0IGdlbmVyYXRlQk1hdGVyaWFsSW5mbyh7aWR9KTsNCiAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgIHRoaXMubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICBhc3luYyBnZW5OZXdmb3JtdWxhSW5mbygpIHsNCiAgICAgIGxldCBpZCA9IHRoaXMuZm9ybS5pZDsNCiAgICAgIGxldCByZXMgPSBhd2FpdCBnZW5lcmF0ZU5ld2Zvcm11bGFJbmZvKHtpZH0pOw0KICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmk43kvZzmiJDlip8iKTsNCiAgICB9LA0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMuZm9ybXVsYVRhYnMgPSBbew0KICAgICAgICB0aXRsZTogJ+WfuuehgOS/oeaBrycsDQogICAgICAgIGNvZGU6ICdiYXNlJw0KICAgICAgfSwgew0KICAgICAgICB0aXRsZTogJ+mFjeaWuemhtemdoicsDQogICAgICAgIGNvZGU6ICdmb3JtdWxhTWF0ZXJpYWwnDQogICAgICB9LCB7DQogICAgICAgIHRpdGxlOiAn6ZmE5Lu2JywNCiAgICAgICAgY29kZTogJ2Zvcm11bGFGaWxlJw0KICAgICAgfV07DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLliJvlu7rphY3mlrkiOw0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlVXBkYXRlKGlkLGlzTG9vaykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgbGV0IGZvcm11bGFUYWJzID0gYXdhaXQgcXVlcnlMb29rRm9ybXVsYVRhYnMoKTsNCiAgICAgIHRoaXMuZm9ybXVsYVRhYnMgPSBmb3JtdWxhVGFiczsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICBnZXRTb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhRGV0YWlsKGlkKS50aGVuKGFzeW5jIHJlc3BvbnNlID0+IHsNCiAgICAgICAgbGV0IGZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICBsZXQgaXNFZGl0ID0gdHJ1ZTsNCiAgICAgICAgbGV0IHByb2plY3RObyA9IGZvcm0ucHJvamVjdE5vOw0KICAgICAgICBpZihwcm9qZWN0Tm8uaW5kZXhPZigiUCIpIT0tMSB8fHByb2plY3RObz09JzIxMDAwMjA4OSd8fHByb2plY3RObz09JzI0MDAwMDM2NSd8fHByb2plY3RObz09JzI0MDAwMTA0MicgfHwgcHJvamVjdE5vPT0nMjEwMDAyMDg4JyB8fCBwcm9qZWN0Tm89PScyMjAwMDU0NTcnIHx8IHByb2plY3RObz09JzI0MDAwMDM2NScpew0KICAgICAgICAgIGlzRWRpdCA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICAgIGlmKGZvcm0uaXNMb2NrPT09Mil7DQogICAgICAgICAgaXNFZGl0ID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5pc0VkaXQgPSBpc0VkaXQ7DQogICAgICAgIGlmKGZvcm0uZmFuZ3NoYWlsZWkpew0KICAgICAgICAgIGZvcm0uZmFuZ3NoYWlsZWkgPSBmb3JtLmZhbmdzaGFpbGVpPT0xP3RydWU6ZmFsc2U7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIGZvcm0uZmFuZ3NoYWlsZWkgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgICBpZihmb3JtLnhpbmdvbmd4aWFvKXsNCiAgICAgICAgICBmb3JtLnhpbmdvbmd4aWFvID1mb3JtLnhpbmdvbmd4aWFvPT0xP3RydWU6ZmFsc2U7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIGZvcm0ueGluZ29uZ3hpYW8gPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgc3BlY09iaiA9IGZvcm0uc3BlY09iajsNCiAgICAgICAgaWYoc3BlY09iail7DQogICAgICAgICAgdGhpcy5zcGVjT2JqID0gSlNPTi5wYXJzZShzcGVjT2JqKTsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy5zcGVjT2JqID0ge307DQogICAgICAgIH0NCiAgICAgICAgbGV0IGZvcm11bGFPYmogPSBmb3JtLmZvcm11bGFPYmo7DQogICAgICAgIGlmKGZvcm11bGFPYmopew0KICAgICAgICAgIGZvcm11bGFPYmogPSBKU09OLnBhcnNlKGZvcm11bGFPYmopOw0KICAgICAgICAgIHRoaXMudG90YWxQZXJjZW50VmFsID0gZm9ybXVsYU9iai50b3RhbFBlcmNlbnRWYWw7DQogICAgICAgICAgdGhpcy5zalRvdGFsUGVyY2V0ID0gZm9ybXVsYU9iai5zalRvdGFsUGVyY2V0Ow0KICAgICAgICAgIGlmKGZvcm11bGFPYmouZGF0YUxpc3Qpew0KICAgICAgICAgICAgdGhpcy5mb3JtdWxhVGFibGVEYXRhTGlzdCA9IGZvcm11bGFPYmouZGF0YUxpc3Q7DQogICAgICAgICAgICB0aGlzLmZvcm11bGFUYWJsZURhdGFMaXN0QmFjayA9IGZvcm11bGFPYmouZGF0YUxpc3Q7DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLmZvcm11bGFUYWJsZURhdGFMaXN0ID0gW107DQogICAgICAgICAgICB0aGlzLmZvcm11bGFUYWJsZURhdGFMaXN0QmFjayA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihmb3JtdWxhT2JqLmFsbExpc3Qpew0KICAgICAgICAgICAgdGhpcy5jb21wb3NpdGlvblRhYmxlRGF0YUxpc3QgPSBmb3JtdWxhT2JqLmFsbExpc3Q7DQogICAgICAgICAgICB0aGlzLmNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdEJhY2sgPSBmb3JtdWxhT2JqLmFsbExpc3Q7DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLmNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCA9IFtdOw0KICAgICAgICAgICAgdGhpcy5jb21wb3NpdGlvblRhYmxlRGF0YUxpc3RCYWNrID0gW107DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmKGZvcm11bGFPYmouc3BlY01hdGVyaWFsRGF0YSl7DQogICAgICAgICAgICB0aGlzLnNwZWNNYXRlcmlhbERhdGFzID0gZm9ybXVsYU9iai5zcGVjTWF0ZXJpYWxEYXRhOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy5zcGVjTWF0ZXJpYWxEYXRhcyA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihmb3JtdWxhT2JqLmx0TnVtU3RyKXsNCiAgICAgICAgICAgIHRoaXMubHROdW1TdHIgPSBmb3JtdWxhT2JqLmx0TnVtU3RyOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy5sdE51bVN0ciA9ICcnOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihmb3JtdWxhT2JqLmd0TnVtU3RyKXsNCiAgICAgICAgICAgIHRoaXMuZ3ROdW1TdHIgPSBmb3JtdWxhT2JqLmd0TnVtU3RyOw0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy5ndE51bVN0ciA9ICcnOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAvL+iOt+WPlumFjeaWueWOn+aWmeS/oeaBrw0KICAgICAgICBsZXQgZm9ybXVsYU1hdGVyaWFsRGF0YXMgPSBmb3JtLmZvcm11bGFNYXRlcmlhbERhdGFzOw0KICAgICAgICBpZihmb3JtdWxhTWF0ZXJpYWxEYXRhcyl7DQogICAgICAgICAgIHRoaXMuZm9ybXVsYU1hdGVyaWFsRGF0YXMgPSBKU09OLnBhcnNlKGZvcm11bGFNYXRlcmlhbERhdGFzKTsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLmNhdGVnb3J5VGV4dCkgew0KICAgICAgICAgIGxldCBjYXRlZ29yeVRleHRMaXN0ID0gZm9ybS5jYXRlZ29yeVRleHQuc3BsaXQoJywnKTsNCiAgICAgICAgICBsZXQgY2F0ZWdvcnlJZHMgPSBbXQ0KICAgICAgICAgIGZvciAobGV0IHQgb2YgY2F0ZWdvcnlUZXh0TGlzdCkgew0KICAgICAgICAgICAgY2F0ZWdvcnlJZHMucHVzaChwYXJzZUludCh0KSkNCiAgICAgICAgICB9DQogICAgICAgICAgZm9ybS5jYXRlZ29yeVRleHQgPSBjYXRlZ29yeUlkcw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGZvcm0uY2F0ZWdvcnlUZXh0ID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0uY2lyVGV4dCkgew0KICAgICAgICAgIGxldCBjaXJUZXh0TGlzdCA9IGZvcm0uY2lyVGV4dC5zcGxpdCgnLCcpOw0KICAgICAgICAgIGxldCBjaXJJZCA9IFtdDQogICAgICAgICAgZm9yIChsZXQgdCBvZiBjaXJUZXh0TGlzdCkgew0KICAgICAgICAgICAgY2lySWQucHVzaChwYXJzZUludCh0KSkNCiAgICAgICAgICB9DQogICAgICAgICAgZm9ybS5jaXJUZXh0ID0gY2lySWQNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLmNpclRleHQgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS5kdWxpVGV4dCkgew0KICAgICAgICAgIGxldCBkdWxpVGV4dExpc3QgPSBmb3JtLmR1bGlUZXh0LnNwbGl0KCcsJyk7DQogICAgICAgICAgbGV0IGR1bGlJZCA9IFtdDQogICAgICAgICAgZm9yIChsZXQgdCBvZiBkdWxpVGV4dExpc3QpIHsNCiAgICAgICAgICAgIGR1bGlJZC5wdXNoKHBhcnNlSW50KHQpKQ0KICAgICAgICAgIH0NCiAgICAgICAgICBmb3JtLmR1bGlUZXh0ID0gZHVsaUlkDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS5kdWxpVGV4dCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLmd4eGMpIHsNCiAgICAgICAgICBmb3JtLmd4eGMgPSBmb3JtLmd4eGMuc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLmd4eGMgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS5neHhjT3RoZXIpIHsNCiAgICAgICAgICBmb3JtLmd4eGNPdGhlciA9IGZvcm0uZ3h4Y090aGVyLnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS5neHhjT3RoZXIgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS56eWJ3KSB7DQogICAgICAgICAgZm9ybS56eWJ3ID0gZm9ybS56eWJ3LnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS56eWJ3ID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0uc3lycSkgew0KICAgICAgICAgIGZvcm0uc3lycSA9IGZvcm0uc3lycS5zcGxpdCgiLCIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGZvcm0uc3lycSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLmNwangpIHsNCiAgICAgICAgICBmb3JtLmNwanggPSBmb3JtLmNwanguc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLmNwanggPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS5wZmx4KSB7DQogICAgICAgICAgZm9ybS5wZmx4ID0gZm9ybS5wZmx4LnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS5wZmx4ID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0ucmFuZmFsZWkpIHsNCiAgICAgICAgICBmb3JtLnJhbmZhbGVpID0gZm9ybS5yYW5mYWxlaS5zcGxpdCgiLCIpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGZvcm0ucmFuZmFsZWkgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdCkgew0KICAgICAgICAgIGZvcm0uY29zbWV0aWNDYXNlRmlyc3QgPSBmb3JtLmNvc21ldGljQ2FzZUZpcnN0LnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLmNvc21ldGljQ2FzZVNlY29uZCkgew0KICAgICAgICAgIGZvcm0uY29zbWV0aWNDYXNlU2Vjb25kID0gZm9ybS5jb3NtZXRpY0Nhc2VTZWNvbmQuc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLmNvc21ldGljQ2FzZVNlY29uZCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLnF1YmFubWVpYmFpbGVpKSB7DQogICAgICAgICAgZm9ybS5xdWJhbm1laWJhaWxlaSA9IGZvcm0ucXViYW5tZWliYWlsZWkuc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLnF1YmFubWVpYmFpbGVpID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0uaW50cm9GaWxlKSB7DQogICAgICAgICAgZm9ybS5pbnRyb0ZpbGUgPSBKU09OLnBhcnNlKGZvcm0uaW50cm9GaWxlKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLmludHJvRmlsZSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLndlbmRpbmd4aW5nRmlsZSkgew0KICAgICAgICAgIGZvcm0ud2VuZGluZ3hpbmdGaWxlID0gSlNPTi5wYXJzZShmb3JtLndlbmRpbmd4aW5nRmlsZSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS53ZW5kaW5neGluZ0ZpbGUgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAoZm9ybS5nb25neWlGaWxlKSB7DQogICAgICAgICAgZm9ybS5nb25neWlGaWxlID0gSlNPTi5wYXJzZShmb3JtLmdvbmd5aUZpbGUpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGZvcm0uZ29uZ3lpRmlsZSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLnhpYW5ncm9uZ3hpbmdGaWxlKSB7DQogICAgICAgICAgZm9ybS54aWFuZ3Jvbmd4aW5nRmlsZSA9IEpTT04ucGFyc2UoZm9ybS54aWFuZ3Jvbmd4aW5nRmlsZSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS54aWFuZ3Jvbmd4aW5nRmlsZSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmIChmb3JtLndlaXNoZW53dUZpbGUpIHsNCiAgICAgICAgICBmb3JtLndlaXNoZW53dUZpbGUgPSBKU09OLnBhcnNlKGZvcm0ud2Vpc2hlbnd1RmlsZSk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgZm9ybS53ZWlzaGVud3VGaWxlID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0ueGlhb2ZlaXpoZUZpbGUpIHsNCiAgICAgICAgICBmb3JtLnhpYW9mZWl6aGVGaWxlID0gSlNPTi5wYXJzZShmb3JtLnhpYW9mZWl6aGVGaWxlKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLnhpYW9mZWl6aGVGaWxlID0gW107DQogICAgICAgIH0NCiAgICAgICAgaWYgKGZvcm0ucWl0YUZpbGUpIHsNCiAgICAgICAgICBmb3JtLnFpdGFGaWxlID0gSlNPTi5wYXJzZShmb3JtLnFpdGFGaWxlKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBmb3JtLnFpdGFGaWxlID0gW107DQogICAgICAgIH0NCiAgICAgICAgbGV0IGl0ZW1OYW1lcyA9IFtdOw0KICAgICAgICBpZihmb3JtLml0ZW1BcnIpew0KICAgICAgICAgIGl0ZW1OYW1lcyA9IGZvcm0uaXRlbUFycjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLml0ZW1OYW1lcyA9IGl0ZW1OYW1lczsNCg0KICAgICAgICBsZXQgcEZvcm11bGFNYXBEYXRhID0gW107DQogICAgICAgIGlmKGZvcm0ucEZvcm11bGFNYXBEYXRhKXsNCiAgICAgICAgICBwRm9ybXVsYU1hcERhdGEgPSBmb3JtLnBGb3JtdWxhTWFwRGF0YTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnBGb3JtdWxhTWFwRGF0YSA9IHBGb3JtdWxhTWFwRGF0YTsNCiAgICAgICAgLy/ojrflj5bkv6Hmga/mlbDmja4NCiAgICAgICAgbGV0IGV4ZWNOdW1iZXJJZCA9IGZvcm0uZXhlY051bWJlcklkOw0KICAgICAgICBpZihleGVjTnVtYmVySWQpew0KICAgICAgICAgIHRoaXMuenhiekNoYW5nZShleGVjTnVtYmVySWQpOw0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICB0aGlzLnp4YnpEZXRhaWwgPSB7fTsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgamN4bUpzb24gPSBmb3JtLmpjeG1Kc29uOw0KICAgICAgICBpZihqY3htSnNvbil7DQogICAgICAgICAgIHRoaXMuaXRlbUFycmF5ID0gSlNPTi5wYXJzZShqY3htSnNvbik7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIHRoaXMuaXRlbUFycmF5ID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtID0gZm9ybTsNCiAgICAgICAgbGV0IGdvbmd5aWppYW5zaHUgPSBmb3JtLmdvbmd5aWppYW5zaHU7DQogICAgICAgIGlmKGdvbmd5aWppYW5zaHUpew0KICAgICAgICAgIGxldCBneWpzRGF0YSA9IEpTT04ucGFyc2UoZ29uZ3lpamlhbnNodSk7DQogICAgICAgICAgbGV0IGd5anMgPSBneWpzRGF0YS5neWpzOw0KICAgICAgICAgIGlmKGd5anMpew0KICAgICAgICAgICAgdGhpcy5neWpzRGF0YUxpc3QgPSBneWpzLm1hcChuYW1lID0+ICh7IG5hbWUgfSkpOzsNCiAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgIHRoaXMucmVmcmVzaEZvcm11bGFMZWdhbEd5KCcwJyk7DQogICAgICAgICAgICB0aGlzLmd5anNEYXRhTGlzdCA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBsZXQgemZ5bCA9IGd5anNEYXRhLnpmeWw7DQogICAgICAgICAgaWYoZ3lqcyl7DQogICAgICAgICAgICB0aGlzLnpmeWxEYXRhTGlzdCA9IHpmeWwubWFwKG5hbWUgPT4gKHsgbmFtZSB9KSk7Ow0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy56ZnlsRGF0YUxpc3QgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIHRoaXMuZ3lqc0RhdGFMaXN0ID0gW107DQogICAgICAgICAgdGhpcy56ZnlsRGF0YUxpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLnJlZnJlc2hGb3JtdWxhTGVnYWxHeSgnMCcpOw0KICAgICAgICB9DQogICAgICAgIGxldCBnb25neWlqaWFuc2h1QmVpYW4gPSBmb3JtLmdvbmd5aWppYW5zaHVCZWlhbjsNCiAgICAgICAgaWYoZ29uZ3lpamlhbnNodUJlaWFuKXsNCiAgICAgICAgICBsZXQgZ3lqc0RhdGEgPSBKU09OLnBhcnNlKGdvbmd5aWppYW5zaHVCZWlhbik7DQogICAgICAgICAgbGV0IGd5anMgPSBneWpzRGF0YS5neWpzOw0KICAgICAgICAgIGlmKGd5anMpew0KICAgICAgICAgICAgdGhpcy5neWpzQmVpYW5EYXRhTGlzdCA9IGd5anMubWFwKG5hbWUgPT4gKHsgbmFtZSB9KSk7Ow0KICAgICAgICAgIH1lbHNlew0KICAgICAgICAgICAgdGhpcy5neWpzQmVpYW5EYXRhTGlzdCA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBsZXQgemZ5bCA9IGd5anNEYXRhLnpmeWw7DQogICAgICAgICAgaWYoZ3lqcyl7DQogICAgICAgICAgICB0aGlzLnpmeWxCZWlhbkRhdGFMaXN0ID0gemZ5bC5tYXAobmFtZSA9PiAoeyBuYW1lIH0pKTs7DQogICAgICAgICAgfWVsc2V7DQogICAgICAgICAgICB0aGlzLnpmeWxCZWlhbkRhdGFMaXN0ID0gW107DQogICAgICAgICAgfQ0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICB0aGlzLmd5anNCZWlhbkRhdGFMaXN0ID0gW107DQogICAgICAgICAgdGhpcy56ZnlsQmVpYW5EYXRhTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIGlmKGlzTG9vayl7DQogICAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnphY3mlrkiOw0KICAgICAgICB9ZWxzZXsNCiAgICAgICAgICB0aGlzLnRpdGxlID0gIuafpeeci+mFjeaWuSI7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5pc0xvb2sgPSBpc0xvb2s7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgICBsZXQgcmVjaXBlQ2hhbmdlSGlzdG9yeURhdGEgPSBhd2FpdCBxdWVyeUZvcm11YWxNYXRlcmlhbFJlY2lwZUNoYW5nZUhpc3RvcnlEYXRhKHtpZH0pOw0KICAgICAgdGhpcy5yZWNpcGVDaGFuZ2VIaXN0b3J5RGF0YSA9IHJlY2lwZUNoYW5nZUhpc3RvcnlEYXRhOw0KICAgICAgLy/ojrflj5ZzcGVj5YaF5a65DQogICAgICB0aGlzLnF1ZXJ5TWF0ZXJpYWxGb3JtdWxhU3BlY0RhdGFMaXN0KGlkKTsNCiAgICAgIC8v6I635Y+W5YWz6IGU56iz5a6a5oCn6K6w5b2V5YaF5a65DQogICAgICB0aGlzLnF1ZXJ5Rm9ybXVsYVN0YWJpbGl0eVJlY29yZERhdGFMaXN0KGlkKTsNCiAgICB9LA0KICAgIHN0YWJpbGl0eVN0YXR1c0Zvcm1hdChyb3cpIHsNCiAgICAgIGNvbnN0IGFyciA9IHRoaXMuc3RhYmlsaXR5U3RhdHVzT3B0aW9ucy5maWx0ZXIoaT0+IGkudmFsdWUgPT09IHJvdy5zdGFiaWxpdHlTdGF0dXMpDQogICAgICBpZihhcnIgJiYgYXJyWzBdKSB7DQogICAgICAgIHJldHVybiBhcnJbMF0ubGFiZWwNCiAgICAgIH0NCiAgICB9LA0KICAgIHlwRm9ybWF0KHJvdykgew0KICAgICAgY29uc3QgYXJyID0gdGhpcy55cEZyb21PcHRpb25zLmZpbHRlcihpPT4gaS52YWx1ZSA9PT0gcm93LnlwRnJvbSkNCiAgICAgIGlmKGFyciAmJiBhcnJbMF0pIHsNCiAgICAgICAgcmV0dXJuIGFyclswXS5sYWJlbA0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgcXVlcnlNYXRlcmlhbEZvcm11bGFTcGVjRGF0YUxpc3QoaWQpIHsNCiAgICAgIGxldCBzb2Z0d2FyZUZvcm11bGFTcGVjTGlzdCA9IGF3YWl0IHF1ZXJ5TWF0ZXJpYWxGb3JtdWxhU3BlY0RhdGFMaXN0KHtpZH0pOw0KICAgICAgdGhpcy5zb2Z0d2FyZUZvcm11bGFTcGVjTGlzdCA9IHNvZnR3YXJlRm9ybXVsYVNwZWNMaXN0Ow0KICAgIH0sDQogICAgYXN5bmMgcXVlcnlGb3JtdWxhU3RhYmlsaXR5UmVjb3JkRGF0YUxpc3QoaWQpIHsNCiAgICAgIGxldCBmb3JtdWxhU3RhYmlsaXR5T2JqID0gYXdhaXQgcXVlcnlGb3JtdWxhU3RhYmlsaXR5UmVjb3JkRGF0YUxpc3Qoe2lkfSk7DQogICAgICBsZXQgcmVsYXRpb25TdGFiaWxpdHlEYXRhTGlzdCA9IGZvcm11bGFTdGFiaWxpdHlPYmoucmVsYXRpb25TdGFiaWxpdHlEYXRhTGlzdDsNCiAgICAgIGxldCBzdGFiaWxpdHlEYXRhTGlzdCA9IGZvcm11bGFTdGFiaWxpdHlPYmouc3RhYmlsaXR5RGF0YUxpc3Q7DQogICAgICB0aGlzLnN0YWJpbGl0eURhdGFMaXN0ID0gc3RhYmlsaXR5RGF0YUxpc3QNCiAgICAgIHRoaXMucmVsYXRpb25TdGFiaWxpdHlEYXRhTGlzdCA9IHJlbGF0aW9uU3RhYmlsaXR5RGF0YUxpc3QNCiAgICB9LA0KICAgIGFzeW5jIGNvcHlHb25neWkoKSB7DQogICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlpI3liLblt6XoibrmlbDmja4s5Lya5riF56m65bey5aGr5pWw5o2uIScpDQogICAgICB0aGlzLmd5anNCZWlhbkRhdGFMaXN0ID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLmd5anNEYXRhTGlzdCkpOw0KICAgICAgdGhpcy56ZnlsQmVpYW5EYXRhTGlzdCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy56ZnlsRGF0YUxpc3QpKTsNCiAgICB9LA0KICAgIGFzeW5jIHN1Ym1pdFVwbG9hZEZvcm0oKSB7DQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IGZvcm11bGFJbWFnZSA9IHRoaXMuZm9ybS5mb3JtdWxhSW1hZ2U7DQogICAgICBsZXQgZm9ybXVsYUNvbnN0cnVjdGlvbklkZWFzID0gdGhpcy5mb3JtLmZvcm11bGFDb25zdHJ1Y3Rpb25JZGVhczsNCiAgICAgIGxldCBpZCA9IHRoaXMuZm9ybS5pZDsNCiAgICAgIGxldCByZW1hcmsgPSB0aGlzLmZvcm0ucmVtYXJrOw0KICAgICAgbGV0IGZvcm11bGFNYXRlcmlhbERhdGFzID0gdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhczsNCiAgICAgIGZvcm11bGFNYXRlcmlhbERhdGFzID0gSlNPTi5zdHJpbmdpZnkoZm9ybXVsYU1hdGVyaWFsRGF0YXMpOw0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgaWQsZm9ybXVsYUltYWdlLGZvcm11bGFNYXRlcmlhbERhdGFzLHJlbWFyayxmb3JtdWxhQ29uc3RydWN0aW9uSWRlYXMNCiAgICAgIH07DQogICAgICB0cnkgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgdXBkYXRlU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYUltZyhwYXJhbXMpOw0KICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+S/ruaUueaIkOWKnyEnKTsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzdWJtaXRVcGxvYWRGaWxlRm9ybSgpIHsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgZm9ybSA9IHRoaXMuZm9ybTsNCiAgICAgIGxldCBwYXJhbSA9IHt9Ow0KICAgICAgcGFyYW0uaWQgPSBmb3JtLmlkOw0KICAgICAgaWYgKGZvcm0uaW50cm9GaWxlKSB7DQogICAgICAgIHBhcmFtLmludHJvRmlsZSA9IEpTT04uc3RyaW5naWZ5KGZvcm0uaW50cm9GaWxlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtLmludHJvRmlsZSA9ICIiOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0ud2VuZGluZ3hpbmdGaWxlKSB7DQogICAgICAgIHBhcmFtLndlbmRpbmd4aW5nRmlsZSA9IEpTT04uc3RyaW5naWZ5KGZvcm0ud2VuZGluZ3hpbmdGaWxlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtLndlbmRpbmd4aW5nRmlsZSA9ICIiOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0uZ29uZ3lpRmlsZSkgew0KICAgICAgICBwYXJhbS5nb25neWlGaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS5nb25neWlGaWxlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtLmdvbmd5aUZpbGUgPSAiIjsNCiAgICAgIH0NCiAgICAgIGlmIChmb3JtLnhpYW5ncm9uZ3hpbmdGaWxlKSB7DQogICAgICAgIHBhcmFtLnhpYW5ncm9uZ3hpbmdGaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS54aWFuZ3Jvbmd4aW5nRmlsZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBwYXJhbS54aWFuZ3Jvbmd4aW5nRmlsZSA9ICIiOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0ud2Vpc2hlbnd1RmlsZSkgew0KICAgICAgICBwYXJhbS53ZWlzaGVud3VGaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS53ZWlzaGVud3VGaWxlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtLndlaXNoZW53dUZpbGUgPSAiIjsNCiAgICAgIH0NCiAgICAgIGlmIChmb3JtLnhpYW9mZWl6aGVGaWxlKSB7DQogICAgICAgIHBhcmFtLnhpYW9mZWl6aGVGaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS54aWFvZmVpemhlRmlsZSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBwYXJhbS54aWFvZmVpemhlRmlsZSA9ICIiOw0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0ucWl0YUZpbGUpIHsNCiAgICAgICAgcGFyYW0ucWl0YUZpbGUgPSBKU09OLnN0cmluZ2lmeShmb3JtLnFpdGFGaWxlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHBhcmFtLnFpdGFGaWxlID0gIiI7DQogICAgICB9DQogICAgICBwYXJhbS53ZW5kaW5neGluZ1Jlc3VsdCA9IGZvcm0ud2VuZGluZ3hpbmdSZXN1bHQ7DQogICAgICBwYXJhbS53ZW5kaW5neGluZ1JlbWFyayA9IGZvcm0ud2VuZGluZ3hpbmdSZW1hcms7DQogICAgICBwYXJhbS54aWFuZ3Jvbmd4aW5nUmVzdWx0ID0gZm9ybS54aWFuZ3Jvbmd4aW5nUmVzdWx0Ow0KICAgICAgcGFyYW0ueGlhbmdyb25neGluZ1JlbWFyayA9IGZvcm0ueGlhbmdyb25neGluZ1JlbWFyazsNCiAgICAgIHBhcmFtLndlaXNoZW53dVJlc3VsdCA9IGZvcm0ud2Vpc2hlbnd1UmVzdWx0Ow0KICAgICAgcGFyYW0ud2Vpc2hlbnd1UmVtYXJrID0gZm9ybS53ZWlzaGVud3VSZW1hcms7DQogICAgICB0cnkgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgdXBkYXRlU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYUZpbGVJbWcocGFyYW0pOw0KICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+S/ruaUueaIkOWKnyEnKTsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7DQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzdWJtaXRGb3JtKGlzRHJhZnQpIHsNCiAgICAgIGlmKGlzRHJhZnQ9PT0wKXsNCiAgICAgICAgYXdhaXQgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCkNCiAgICAgIH0NCiAgICAgIGxldCBmb3JtID0gT2JqZWN0LmFzc2lnbih7fSx0aGlzLmZvcm0pOw0KICAgICAgbGV0IHByb2plY3RObyA9IGZvcm0ucHJvamVjdE5vOw0KICAgICAgaWYoIXByb2plY3RObyB8fCBwcm9qZWN0Tm8ubGVuZ3RoPT0wKXsNCiAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36YCJ5oup6aG555uuJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGxldCBjYXRlZ29yeVRleHQgPSBmb3JtLmNhdGVnb3J5VGV4dDsNCiAgICAgIGlmKCFjYXRlZ29yeVRleHQgfHwgY2F0ZWdvcnlUZXh0Lmxlbmd0aD09MCl7DQogICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+mAieaLqemFjeaWueexu+WIqycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBmb3JtLmlzRHJhZnQgPSBpc0RyYWZ0Ow0KICAgICAgaWYoZm9ybS5jYXRlZ29yeVRleHQgJiYgZm9ybS5jYXRlZ29yeVRleHQubGVuZ3RoID4gMCAmJiBpc0FycmF5KGZvcm0uY2F0ZWdvcnlUZXh0KSkgew0KICAgICAgICBmb3JtLmNhdGVnb3J5VGV4dCA9IGZvcm0uY2F0ZWdvcnlUZXh0LmpvaW4oJywnKQ0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0uY2F0ZWdvcnlUZXh0ID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLmNpclRleHQgJiYgZm9ybS5jaXJUZXh0Lmxlbmd0aCA+IDAgJiYgaXNBcnJheShmb3JtLmNpclRleHQpKSB7DQogICAgICAgIGZvcm0uY2lyVGV4dCA9IGZvcm0uY2lyVGV4dC5qb2luKCcsJykNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLmNpclRleHQgPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0uZHVsaVRleHQgJiYgZm9ybS5kdWxpVGV4dC5sZW5ndGggPiAwICYmIGlzQXJyYXkoZm9ybS5kdWxpVGV4dCkpIHsNCiAgICAgICAgZm9ybS5kdWxpVGV4dCA9IGZvcm0uZHVsaVRleHQuam9pbignLCcpDQogICAgICB9ZWxzZXsNCiAgICAgICAgZm9ybS5kdWxpVGV4dCA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS5neHhjKXsNCiAgICAgICAgZm9ybS5neHhjID0gZm9ybS5neHhjLmpvaW4oJywnKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLmd4eGMgPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0uZ3h4Y090aGVyKXsNCiAgICAgICAgZm9ybS5neHhjT3RoZXIgPSBmb3JtLmd4eGNPdGhlci5qb2luKCcsJyk7DQogICAgICB9ZWxzZXsNCiAgICAgICAgZm9ybS5neHhjT3RoZXIgPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0uenlidyl7DQogICAgICAgIGZvcm0uenlidyA9IGZvcm0uenlidy5qb2luKCcsJyk7DQogICAgICB9ZWxzZXsNCiAgICAgICAgZm9ybS56eWJ3ID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLnN5cnEpew0KICAgICAgICBmb3JtLnN5cnEgPSBmb3JtLnN5cnEuam9pbignLCcpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0uc3lycSA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS5jcGp4KXsNCiAgICAgICAgZm9ybS5jcGp4ID0gZm9ybS5jcGp4LmpvaW4oJywnKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLmNwanggPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0ucGZseCl7DQogICAgICAgIGZvcm0ucGZseCA9IGZvcm0ucGZseC5qb2luKCcsJyk7DQogICAgICB9ZWxzZXsNCiAgICAgICAgZm9ybS5wZmx4ID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLnJhbmZhbGVpKXsNCiAgICAgICAgZm9ybS5yYW5mYWxlaSA9IGZvcm0ucmFuZmFsZWkuam9pbignLCcpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0ucmFuZmFsZWkgPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0uY29zbWV0aWNDYXNlRmlyc3Qpew0KICAgICAgICBmb3JtLmNvc21ldGljQ2FzZUZpcnN0ID0gZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdC5qb2luKCcsJyk7DQogICAgICB9ZWxzZXsNCiAgICAgICAgZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdCA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS5jb3NtZXRpY0Nhc2VTZWNvbmQpew0KICAgICAgICBmb3JtLmNvc21ldGljQ2FzZVNlY29uZCA9IGZvcm0uY29zbWV0aWNDYXNlU2Vjb25kLmpvaW4oJywnKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLmNvc21ldGljQ2FzZVNlY29uZCA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS5xdWJhbm1laWJhaWxlaSl7DQogICAgICAgIGZvcm0ucXViYW5tZWliYWlsZWkgPSBmb3JtLnF1YmFubWVpYmFpbGVpLmpvaW4oJywnKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLnF1YmFubWVpYmFpbGVpID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLmludHJvRmlsZSl7DQogICAgICAgIGZvcm0uaW50cm9GaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS5pbnRyb0ZpbGUpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0uaW50cm9GaWxlID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLndlbmRpbmd4aW5nRmlsZSl7DQogICAgICAgIGZvcm0ud2VuZGluZ3hpbmdGaWxlID0gSlNPTi5zdHJpbmdpZnkoZm9ybS53ZW5kaW5neGluZ0ZpbGUpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0ud2VuZGluZ3hpbmdGaWxlID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLmdvbmd5aUZpbGUpew0KICAgICAgICBmb3JtLmdvbmd5aUZpbGUgPSBKU09OLnN0cmluZ2lmeShmb3JtLmdvbmd5aUZpbGUpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0uZ29uZ3lpRmlsZSA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS54aWFuZ3Jvbmd4aW5nRmlsZSl7DQogICAgICAgIGZvcm0ueGlhbmdyb25neGluZ0ZpbGUgPSBKU09OLnN0cmluZ2lmeShmb3JtLnhpYW5ncm9uZ3hpbmdGaWxlKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLnhpYW5ncm9uZ3hpbmdGaWxlID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLndlaXNoZW53dUZpbGUpew0KICAgICAgICBmb3JtLndlaXNoZW53dUZpbGUgPSBKU09OLnN0cmluZ2lmeShmb3JtLndlaXNoZW53dUZpbGUpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0ud2Vpc2hlbnd1RmlsZSA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoZm9ybS54aWFvZmVpemhlRmlsZSl7DQogICAgICAgIGZvcm0ueGlhb2ZlaXpoZUZpbGUgPSBKU09OLnN0cmluZ2lmeShmb3JtLnhpYW9mZWl6aGVGaWxlKTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLnhpYW9mZWl6aGVGaWxlID0gJyc7DQogICAgICB9DQogICAgICBpZihmb3JtLnFpdGFGaWxlKXsNCiAgICAgICAgZm9ybS5xaXRhRmlsZSA9IEpTT04uc3RyaW5naWZ5KGZvcm0ucWl0YUZpbGUpOw0KICAgICAgfWVsc2V7DQogICAgICAgIGZvcm0ucWl0YUZpbGUgPSAnJzsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0uZmFuZ3NoYWlsZWkpew0KICAgICAgICBmb3JtLmZhbmdzaGFpbGVpID0gMTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLmZhbmdzaGFpbGVpID0gMDsNCiAgICAgIH0NCiAgICAgIGlmKGZvcm0ueGluZ29uZ3hpYW8pew0KICAgICAgICBmb3JtLnhpbmdvbmd4aWFvID0gMTsNCiAgICAgIH1lbHNlew0KICAgICAgICBmb3JtLnhpbmdvbmd4aWFvID0gMDsNCiAgICAgIH0NCiAgICAgIGxldCBmb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IHRoaXMuZm9ybXVsYU1hdGVyaWFsRGF0YXM7DQogICAgICBpZihmb3JtdWxhTWF0ZXJpYWxEYXRhcyAmJiBmb3JtdWxhTWF0ZXJpYWxEYXRhcy5sZW5ndGg+MCl7DQogICAgICAgIGZvcihsZXQgaXRlbSBvZiBmb3JtdWxhTWF0ZXJpYWxEYXRhcyl7DQogICAgICAgICAgbGV0IGRlc2lnbmF0ZWRVc2UgPSBpdGVtLmRlc2lnbmF0ZWRVc2U7DQogICAgICAgICAgbGV0IGlzUmVsYXRpb24gPSBpdGVtLmlzUmVsYXRpb247DQogICAgICAgICAgbGV0IGlzRnggPSBpdGVtLmlzRng7DQogICAgICAgICAgbGV0IHJlbWFyayA9IGl0ZW0ucmVtYXJrOw0KICAgICAgICAgIGxldCByZWxhdGlvbkNvZGUgPSBpdGVtLnJlbGF0aW9uQ29kZTsNCiAgICAgICAgICBsZXQgbWF0ZXJpYWxDb2RlID0gaXRlbS5tYXRlcmlhbENvZGU7DQogICAgICAgICAgbGV0IGFwcG9pbnRDb2RlID0gaXRlbS5hcHBvaW50Q29kZTsNCiAgICAgICAgICBpZihkZXNpZ25hdGVkVXNlPT09J+aMh+WumicgJiYgIWFwcG9pbnRDb2RlKXsNCiAgICAgICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+mAieaLqeaMh+WumuWOn+aWmSEnKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoaXNSZWxhdGlvbj09MSAmJiAhcmVtYXJrKXsNCiAgICAgICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+i+k+WFpeS9v+eUqOS7o+eggVsnK21hdGVyaWFsQ29kZSsnXeeahOWkh+azqCzlrZjlnKjmjqjojZDljp/mlplbJytyZWxhdGlvbkNvZGUrJ10nKTsNCiAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoaXNGeD09MSl7DQogICAgICAgICAgICBsZXQgbXNnID0gbWF0ZXJpYWxDb2RlICsgIuS4uuaKpOiCpOmjjumZqeWOn+aWme+8jOivt+aguOWuniEiOw0KICAgICAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybShtc2csICLorablkYoiLCB7DQogICAgICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgZm9ybS5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IEpTT04uc3RyaW5naWZ5KGZvcm11bGFNYXRlcmlhbERhdGFzKTsNCiAgICAgICAgbGV0IHJldHVybk9iaiA9IHRoaXMuaXNSZXBlYXQoZm9ybXVsYU1hdGVyaWFsRGF0YXMpOw0KICAgICAgICBsZXQgbnVtID0gcmV0dXJuT2JqLm51bTsNCiAgICAgICAgaWYobnVtPjApew0KICAgICAgICAgIGxldCByZXBlYXRDb2RlID0gcmV0dXJuT2JqLnJlcGVhdENvZGU7DQogICAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgn5a2Y5Zyo6YeN5aSN5Y6f5paZJytyZXBlYXRDb2RlKycs5piv5ZCm56Gu6K6k5re75YqgIScpDQogICAgICAgIH0NCiAgICAgIH1lbHNlew0KICAgICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCfmgqjov5jmsqHmnInpgInmi6nljp/mlpnvvIznoa7lrprmt7vliqDphY3mlrnvvJ8nKTsNCiAgICAgICAgZm9ybS5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9ICcnOw0KICAgICAgfQ0KICAgICAgaWYoIWZvcm0ucGZseCAmJiBpc0RyYWZ0PT09MCl7DQogICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nkvb/nlKjmlrnms5UhJyk7DQogICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAoZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgIHRyeSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGF3YWl0IHVwZGF0ZVNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGEoZm9ybSkNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuZm9ybS5jdXJyZW50VmVyc2lvbiA9IHBhcnNlRmxvYXQodGhpcy5mb3JtLmN1cnJlbnRWZXJzaW9uKSArIDE7DQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKQ0KICAgICAgICAgIC8vdGhpcy5jbG9zZSgpOw0KICAgICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2UNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdHJ5IHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlDQogICAgICAgICAgYXdhaXQgYWRkU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYShmb3JtKQ0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgICAgIHRoaXMuY2xvc2UoKTsNCiAgICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjbG9zZSgpIHsNCiAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJ0YWdzVmlldy9kZWxWaWV3IiwgdGhpcy4kcm91dGUpOw0KICAgICAgbGV0IHZpZXcgPSB7DQogICAgICAgIGZ1bGxQYXRoIDogJy9yZC9zb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhJywNCiAgICAgICAgbmFtZToiU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYSIsDQogICAgICAgIHBhdGg6Ii9yZC9zb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhIiwNCiAgICAgICAgdGl0bGU6IueglOWPkemFjeaWuSINCiAgICAgIH07DQogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgndGFnc1ZpZXcvZGVsQ2FjaGVkVmlldycsIHZpZXcpLnRoZW4oKCkgPT4gew0KICAgICAgICBjb25zdCB7IGZ1bGxQYXRoIH0gPSB2aWV3DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRyb3V0ZXIucmVwbGFjZSh7DQogICAgICAgICAgICBwYXRoOiAnL3JlZGlyZWN0JyArIGZ1bGxQYXRoDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvL+WIpOaWreaYr+WQpumHjeWkjQ0KICAgIGlzUmVwZWF0KGRhdGFzKXsNCiAgICAgICBsZXQgcmV0dXJuT2JqID0ge251bTowLHJlcGVhdENvZGU6Jyd9Ow0KICAgICAgIGxldCByZXBlYXRDb2Rlc1NldCA9IG5ldyBTZXQoKTsNCiAgICAgIGlmKGRhdGFzICYmIGRhdGFzLmxlbmd0aD4wKXsNCiAgICAgICAgIGxldCBjb2RlcyA9IFtdOw0KICAgICAgICAgZm9yKGxldCBpdGVtIG9mIGRhdGFzKXsNCiAgICAgICAgICAgIGNvZGVzLnB1c2goaXRlbS5tYXRlcmlhbENvZGUpOw0KICAgICAgICAgfQ0KICAgICAgICAgZm9yKGxldCBjb2RlIG9mIGNvZGVzKXsNCiAgICAgICAgICAgICBsZXQgaW5kZXggPSAwOw0KICAgICAgICAgICAgIGZvcihsZXQgaXRlbSBvZiBkYXRhcyl7DQogICAgICAgICAgICAgICAgIGxldCBtYXRlcmlhbENvZGUgPSBpdGVtLm1hdGVyaWFsQ29kZTsNCiAgICAgICAgICAgICAgICAgaWYoY29kZSA9PT0gbWF0ZXJpYWxDb2RlKXsNCiAgICAgICAgICAgICAgICAgICBpbmRleCsrOw0KICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgfQ0KICAgICAgICAgICAgIGlmKGluZGV4PjEpew0KICAgICAgICAgICAgICAgcmVwZWF0Q29kZXNTZXQuYWRkKGNvZGUpOw0KICAgICAgICAgICAgIH0NCiAgICAgICAgIH0NCiAgICAgICB9DQogICAgICAgaWYocmVwZWF0Q29kZXNTZXQgJiYgcmVwZWF0Q29kZXNTZXQuc2l6ZT4wKXsNCiAgICAgICAgIGxldCBzdHIgPSBKU09OLnN0cmluZ2lmeShBcnJheS5mcm9tKHJlcGVhdENvZGVzU2V0KSk7DQogICAgICAgICByZXR1cm5PYmogPSB7bnVtOjEscmVwZWF0Q29kZTpzdHJ9Ow0KICAgICAgIH0NCiAgICAgICByZXR1cm4gcmV0dXJuT2JqOw0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk56CU5Y+R6YWN5pa557yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhuT8nLCAi6K2m5ZGKIiwgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgICByZXR1cm4gZGVsU29mdHdhcmVEZXZlbG9waW5nRm9ybXVsYShpZHMpOw0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHRoaXMucXVlcnlQYXJhbXM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rmiYDmnInnoJTlj5HphY3mlrnmlbDmja7pobk/JywgIuitpuWRiiIsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZXhwb3J0TG9hZGluZyA9IHRydWU7DQogICAgICAgICAgcmV0dXJuIGV4cG9ydFNvZnR3YXJlRGV2ZWxvcGluZ0Zvcm11bGEocXVlcnlQYXJhbXMpOw0KICAgICAgICB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmRvd25sb2FkKHJlc3BvbnNlLm1zZyk7DQogICAgICAgICAgdGhpcy5leHBvcnRMb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGFzeW5jIHF1ZXJ5TWF0ZXJpYWxDb2RlKCkgew0KICAgICAgbGV0IG1hdGVyaWFsQ29kZSA9IHRoaXMuZm9ybS5tYXRlcmlhbENvZGU7DQogICAgICBsZXQgZm9ybXVsYU1hdGVyaWFsRGF0YXMgPSB0aGlzLmZvcm11bGFNYXRlcmlhbERhdGFzOw0KICAgICAgaWYgKG1hdGVyaWFsQ29kZSkgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0UmF3TWF0ZXJpYWxJbmZvQnlDb2RlKHttYXRlcmlhbENvZGV9KTsNCiAgICAgICAgaWYocmVzLmRhdGEpew0KICAgICAgICAgIGxldCBpc1JlbGF0aW9uID0gcmVzLmRhdGEuaXNSZWxhdGlvbjsNCiAgICAgICAgICAgaWYoaXNSZWxhdGlvbj09MSl7DQogICAgICAgICAgICAgbGV0IHRpcHNJbmZvID0gcmVzLmRhdGEudGlwc0luZm87DQogICAgICAgICAgICAgdGhpcy5tc2dJbmZvKHRpcHNJbmZvKTsNCiAgICAgICAgICB9DQogICAgICAgICAgZm9ybXVsYU1hdGVyaWFsRGF0YXMudW5zaGlmdChyZXMuZGF0YSk7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IGZvcm11bGFNYXRlcmlhbERhdGFzOw0KICAgICAgICB0aGlzLmNvZGVDaGFuZ2UoMSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fovpPlhaXljp/mlpnku6PnoIEhJyk7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBxdWVyeUZvcm11bGFDb2RlKCkgew0KICAgICAgbGV0IGZvcm11bGFDb2RlUGFyYW1zID0gdGhpcy5mb3JtLmZvcm11bGFDb2RlUGFyYW1zOw0KICAgICAgbGV0IGZvcm11bGFNYXRlcmlhbERhdGFzID0gdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhczsNCiAgICAgIGlmIChmb3JtdWxhQ29kZVBhcmFtcykgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0Rm9ybXVsYUluZm9CeUNvZGUoe2Zvcm11bGFDb2RlOmZvcm11bGFDb2RlUGFyYW1zfSk7DQogICAgICAgICBpZihyZXMuZGF0YSl7DQogICAgICAgICAgZm9ybXVsYU1hdGVyaWFsRGF0YXMudW5zaGlmdChyZXMuZGF0YSk7DQogICAgICAgICAgfQ0KICAgICAgICAgdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IGZvcm11bGFNYXRlcmlhbERhdGFzOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5tc2dFcnJvcign6K+36L6T5YWl6YWN5pa557yW56CBIScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgY29uZmlybVNlbGVjdEdvb2RzKCkgew0KICAgICAgbGV0IGZvcm11bGFDb2RlUGFyYW1zID0gdGhpcy5mb3JtLmZvcm11bGFDb2RlUGFyYW1zOw0KICAgICAgbGV0IGZvcm11bGFNYXRlcmlhbERhdGFzID0gdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhczsNCiAgICAgIGlmIChmb3JtdWxhQ29kZVBhcmFtcykgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0Rm9ybXVsYUxhYk5vSW5mb0J5Q29kZSh7bGFib3JhdG9yeUNvZGU6Zm9ybXVsYUNvZGVQYXJhbXN9KTsNCiAgICAgICAgIGlmKHJlcy5kYXRhKXsNCiAgICAgICAgICAgZm9ybXVsYU1hdGVyaWFsRGF0YXMucHVzaChyZXMuZGF0YSk7DQogICAgICAgICB9DQogICAgICAgICB0aGlzLmZvcm11bGFNYXRlcmlhbERhdGFzID0gZm9ybXVsYU1hdGVyaWFsRGF0YXM7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fovpPlhaXlrp7pqozlrqTnvJbnoIEhJyk7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBzdWJtaXRTcGVjKCl7DQogICAgICBsZXQgc3BlY09iaiA9IHt9Ow0KICAgICAgbGV0IGZvcm0gPSB0aGlzLmZvcm07DQogICAgICBsZXQgaXRlbUFycmF5ID0gdGhpcy5pdGVtQXJyYXk7DQogICAgICBpZighZm9ybS5leGVjTnVtYmVySWQpew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nmiafooYzmoIflh4Yv5qCH5YeG5ZCN56ewJyk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGlmKCEoaXRlbUFycmF5ICYmaXRlbUFycmF5Lmxlbmd0aD4wKSl7DQogICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+mAieaLqeagh+WHhuaooeadvycpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBzcGVjT2JqLmV4ZWNOdW1iZXJJZCA9IGZvcm0uZXhlY051bWJlcklkOw0KICAgICAgc3BlY09iai5leGVjTnVtYmVyID0gZm9ybS5leGVjTnVtYmVyOw0KICAgICAgc3BlY09iai5jdXJyZW50VGVtcGxhdGVJZCA9IHRoaXMuZm9ybS5jdXJyZW50VGVtcGxhdGVJZDsNCiAgICAgIHNwZWNPYmouZm9ybXVsYUlkID0gZm9ybS5pZDsNCiAgICAgIHNwZWNPYmouaXRlbUFycmF5ID0gaXRlbUFycmF5Ow0KICAgICAgc3BlY09iai5pc0xvY2sgPSBmb3JtLmlzTG9jazsNCiAgICAgIHNwZWNPYmouZm9ybXVsYUNvZGUgPSBmb3JtLmZvcm11bGFDb2RlOw0KICAgICAgc3BlY09iai5sYWJvcmF0b3J5Q29kZSA9IGZvcm0ubGFib3JhdG9yeUNvZGU7DQogICAgICBzcGVjT2JqLnByb2R1Y3ROYW1lID0gZm9ybS5wcm9kdWN0TmFtZTsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICB0cnl7DQogICAgICAgIGxldCByZXMgPSBhd2FpdCBhZGRTb2Z0d2FyZURldmVsb3BpbmdGb3JtdWxhU3BlY1p4Ynooe3NwZWNPYmo6SlNPTi5zdHJpbmdpZnkoc3BlY09iail9KTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfmt7vliqDmiJDlip8hJyk7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfWNhdGNoKGUpew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIHN1Ym1pdFVzZXJTcGVjKCl7DQogICAgICBsZXQgc3BlY09iaiA9IHt9Ow0KICAgICAgbGV0IGZvcm0gPSB0aGlzLmZvcm07DQogICAgICBsZXQgaXRlbUFycmF5ID0gdGhpcy51c2VySXRlbUFycmF5Ow0KICAgICAgaWYoIWZvcm0udHlwZSl7DQogICAgICAgIHRoaXMubXNnRXJyb3IoJ+ivt+mAieaLqeagt+WTgeadpea6kCcpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBzcGVjT2JqLmZvcm11bGFJZCA9IGZvcm0uaWQ7DQogICAgICBzcGVjT2JqLnNwZWNJZCA9IHRoaXMuc3BlY0lkOw0KICAgICAgc3BlY09iai5pdGVtQXJyYXkgPSBpdGVtQXJyYXk7DQogICAgICBzcGVjT2JqLnR5cGUgPSBmb3JtLnR5cGU7DQogICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOw0KICAgICAgdHJ5ew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgYWRkU29mdHdhcmVEZXZlbG9waW5nVXNlckZvcm11bGFTcGVjWnhieih7c3BlY09iajpKU09OLnN0cmluZ2lmeShzcGVjT2JqKX0pOw0KICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+a3u+WKoOaIkOWKnyEnKTsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMuc3BlY09wZW4gPSBmYWxzZTsNCiAgICAgICAgdGhpcy5xdWVyeU1hdGVyaWFsRm9ybXVsYVNwZWNEYXRhTGlzdChmb3JtLmlkKTsNCiAgICAgIH1jYXRjaChlKXsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBkZWxGb3JtdWxhTWF0ZXJpYWwocm93KXsNCiAgICAgIHRoaXMuZm9ybXVsYU1hdGVyaWFsRGF0YXMgPSB0aGlzLmZvcm11bGFNYXRlcmlhbERhdGFzLmZpbHRlcih4ID0+IHsNCiAgICAgICAgcmV0dXJuIHgua2V5ICE9IHJvdy5rZXk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNhdGVnb3J5Q2hhbmdlKGlkKXsNCiAgICAgICBsZXQgZm9ybSA9IHRoaXMuZm9ybTsNCiAgICAgICBsZXQgcmFuZmFsZWkgPSBmb3JtLnJhbmZhbGVpOw0KICAgICAgIGxldCBxdWJhbm1laWJhaWxlaSA9IGZvcm0ucXViYW5tZWliYWlsZWk7DQogICAgICAgbGV0IGZhbmdzaGFpbGVpID0gZm9ybS5mYW5nc2hhaWxlaTsNCiAgICAgICBsZXQgc2ZhID0gZm9ybS5zZmE7DQogICAgICAgbGV0IHBhID0gZm9ybS5wYTsNCiAgICAgICBsZXQgeXVzaG91c2ZhID0gZm9ybS55dXNob3VzZmE7DQogICAgICAgbGV0IHhpbmdvbmd4aWFvID0gZm9ybS54aW5nb25neGlhbzsNCiAgICAgICBsZXQgeGluZ29uZ3hpYW9jb250ZW50ID0gZm9ybS54aW5nb25neGlhb2NvbnRlbnQ7DQogICAgICBpZihyYW5mYWxlaS5sZW5ndGg+MA0KICAgICAgfHxxdWJhbm1laWJhaWxlaS5sZW5ndGg+MA0KICAgICAgfHxmYW5nc2hhaWxlaQ0KICAgICAgfHxzZmEgfHwgcGEgfHwgeXVzaG91c2ZhIHx8IHhpbmdvbmd4aWFvIHx8eGluZ29uZ3hpYW9jb250ZW50KXsNCiAgICAgICAgdGhpcy5mb3JtLmNvc21ldGljQ2xhc3NpZmljYXRpb24gPSAnMSc7DQogICAgICAgIHRoaXMuZm9ybS5jb3NtZXRpY0Nhc2UgPSAnMSc7DQogICAgICAgIGxldCBjb3NtZXRpY0Nhc2VGaXJzdCA9IFtdOw0KICAgICAgICBpZighY29zbWV0aWNDYXNlRmlyc3QuaW5jbHVkZXMoJzEnKSl7DQogICAgICAgICAgY29zbWV0aWNDYXNlRmlyc3QucHVzaCgnMScpOw0KICAgICAgICAgIHRoaXMuZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdCA9IGNvc21ldGljQ2FzZUZpcnN0Ow0KICAgICAgICB9DQogICAgICB9ZWxzZXsNCiAgICAgICAgdGhpcy5mb3JtLmNvc21ldGljQ2xhc3NpZmljYXRpb24gPSAnJzsNCiAgICAgICAgdGhpcy5mb3JtLmNvc21ldGljQ2FzZSA9ICcnOw0KICAgICAgICB0aGlzLmNvZGVDaGFuZ2UoMSk7DQogICAgICB9DQogICAgfSwNCiAgICBjYXRlZ29yeUNoYW5nZU5ldyhpZCl7DQogICAgICAgbGV0IHJlcyA9IDE7DQogICAgICAgbGV0IGZvcm0gPSB0aGlzLmZvcm07DQogICAgICAgbGV0IHJhbmZhbGVpID0gZm9ybS5yYW5mYWxlaTsNCiAgICAgICBsZXQgcXViYW5tZWliYWlsZWkgPSBmb3JtLnF1YmFubWVpYmFpbGVpOw0KICAgICAgIGxldCBmYW5nc2hhaWxlaSA9IGZvcm0uZmFuZ3NoYWlsZWk7DQogICAgICAgbGV0IHNmYSA9IGZvcm0uc2ZhOw0KICAgICAgIGxldCBwYSA9IGZvcm0ucGE7DQogICAgICAgbGV0IHl1c2hvdXNmYSA9IGZvcm0ueXVzaG91c2ZhOw0KICAgICAgIGxldCB4aW5nb25neGlhbyA9IGZvcm0ueGluZ29uZ3hpYW87DQogICAgICAgbGV0IHhpbmdvbmd4aWFvY29udGVudCA9IGZvcm0ueGluZ29uZ3hpYW9jb250ZW50Ow0KICAgICAgIGlmKHJhbmZhbGVpLmxlbmd0aD4wDQogICAgICB8fHF1YmFubWVpYmFpbGVpLmxlbmd0aD4wDQogICAgICB8fGZhbmdzaGFpbGVpDQogICAgICB8fHNmYSB8fCBwYSB8fCB5dXNob3VzZmEgfHwgeGluZ29uZ3hpYW8gfHx4aW5nb25neGlhb2NvbnRlbnQpew0KICAgICAgICB0aGlzLmZvcm0uY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcxJzsNCiAgICAgICAgdGhpcy5mb3JtLmNvc21ldGljQ2FzZSA9ICcxJzsNCiAgICAgICAgcmVzID0gMzsNCiAgICAgIH1lbHNlew0KICAgICAgICB0aGlzLmZvcm0uY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcnOw0KICAgICAgICB0aGlzLmZvcm0uY29zbWV0aWNDYXNlID0gJyc7DQogICAgICAgIHJlcyA9IDI7DQogICAgICB9DQogICAgICByZXR1cm4gcmVzOw0KICAgIH0sDQogICAgYXN5bmMgY29kZUNoYW5nZSh0eXBlKSB7DQogICAgICBsZXQgY29kZSA9IFtdDQogICAgICBsZXQgZm9ybSA9IHRoaXMuZm9ybQ0KICAgICAgaWYgKGZvcm0uZ3h4Yy5sZW5ndGggPiAwKSB7DQogICAgICAgIGNvZGUucHVzaCh0aGlzLmVmZmljYWN5T3B0aW9ucy5maWx0ZXIoaSA9PiBmb3JtLmd4eGMuaW5jbHVkZXMoaS5pZCkpDQogICAgICAgICAgLnNvcnQoKG4xLCBuMikgPT4gbjEuaWQgLSBuMi5pZCkubWFwKGkgPT4gaS5pZCkuam9pbignLycpKQ0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0uenlidy5sZW5ndGggPiAwKSB7DQogICAgICAgIGNvZGUucHVzaCh0aGlzLnp5YndPcHRpb25zLmZpbHRlcihpID0+IGZvcm0uenlidy5pbmNsdWRlcyhpLmlkKSkuc29ydCgobjEsIG4yKSA9PiBuMS5pZCAtIG4yLmlkKQ0KICAgICAgICAgIC5tYXAoaSA9PiBpLmlkKS5qb2luKCcvJykpDQogICAgICB9DQogICAgICBpZiAoZm9ybS5jcGp4Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgY29kZS5wdXNoKHRoaXMuY3BqeE9wdGlvbnMuZmlsdGVyKGkgPT4gZm9ybS5jcGp4LmluY2x1ZGVzKGkuaWQpKS5zb3J0KChuMSwgbjIpID0+IG4xLmlkIC0gbjIuaWQpDQogICAgICAgICAgLm1hcChpID0+IGkuaWQpLmpvaW4oJy8nKSkNCiAgICAgIH0NCiAgICAgIGlmIChmb3JtLnN5cnEubGVuZ3RoID4gMCkgew0KICAgICAgICBjb2RlLnB1c2godGhpcy5zeXJxT3B0aW9ucy5maWx0ZXIoaSA9PiBmb3JtLnN5cnEuaW5jbHVkZXMoaS5pZCkpLnNvcnQoKG4xLCBuMikgPT4gbjEuaWQgLSBuMi5pZCkNCiAgICAgICAgICAubWFwKGkgPT4gaS5pZCkuam9pbignLycpKQ0KICAgICAgfQ0KICAgICAgaWYgKGZvcm0ucGZseC5sZW5ndGggPiAwKSB7DQogICAgICAgIGNvZGUucHVzaCh0aGlzLnN5ZmZPcHRpb25zLmZpbHRlcihpID0+IGZvcm0ucGZseC5pbmNsdWRlcyhpLmlkKSkuc29ydCgobjEsIG4yKSA9PiBuMS5pZCAtIG4yLmlkKQ0KICAgICAgICAgIC5tYXAoaSA9PiBpLmlkKS5qb2luKCcvJykpDQogICAgICB9DQogICAgICB0aGlzLmZvcm0uY3BmbGRtID0gY29kZS5qb2luKCd+JykNCg0KICAgICAgaWYgKHR5cGUgPT0gMSkgew0KICAgICAgICBsZXQgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICIiOw0KICAgICAgICBsZXQgY29zbWV0aWNDYXNlID0gIiI7DQogICAgICAgIGxldCBneHhjID0gZm9ybS5neHhjOw0KICAgICAgICBsZXQgZ3h4YzEgPSBbJ0EnLCAnMycsICc0JywgJzEnLCAnMicsICc1J107DQogICAgICAgIGxldCBneHhjMiA9IFsnMTQnLCAnMTUnLCAnMTknLCAnNicsICcyMycsICcyNSddOw0KDQogICAgICAgIGxldCB6eWJ3ID0gZm9ybS56eWJ3Ow0KICAgICAgICBsZXQgenlidzEgPSBbJ0InXTsNCg0KICAgICAgICBsZXQgY3BqeCA9IGZvcm0uY3BqeDsNCiAgICAgICAgbGV0IGNwangyID0gWyc5JywgJzEwJ107DQoNCiAgICAgICAgbGV0IHN5cnEgPSBmb3JtLnN5cnE7DQogICAgICAgIGxldCBzeXJxMSA9IFsnQycsICcxJywgJzInXTsNCiAgICAgICAgbGV0IHN5cnEyID0gWycxJywgJzInXTsNCiAgICAgICAgbGV0IGlzUHJvY2VzcyA9IHRydWU7DQogICAgICAgIGxldCBjb3NtZXRpY0Nhc2VGaXJzdCA9IFtdOw0KICAgICAgICBsZXQgY29zbWV0aWNDYXNlU2Vjb25kID0gW107DQogICAgICAgIGlmICh0aGlzLmFycmF5Q29udGFpbnNBbm90aGVyKHN5cnEsIHN5cnExKSkgew0KICAgICAgICAgIGlmKHRoaXMuYXJyYXlDb250YWluc0Fub3RoZXIoc3lycSxzeXJxMikpew0KICAgICAgICAgICAgaWYoIWNvc21ldGljQ2FzZUZpcnN0LmluY2x1ZGVzKCcyJykpew0KICAgICAgICAgICAgICBjb3NtZXRpY0Nhc2VGaXJzdC5wdXNoKCcyJyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmFycmF5Q29udGFpbnNBbm90aGVyKGd4eGMsIGd4eGMyKSl7DQogICAgICAgICAgaWYoIWNvc21ldGljQ2FzZVNlY29uZC5pbmNsdWRlcygnMycpKXsNCiAgICAgICAgICAgIGNvc21ldGljQ2FzZVNlY29uZC5wdXNoKCczJyk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmFycmF5Q29udGFpbnNBbm90aGVyKGNwangsIGNwangyKSl7DQogICAgICAgICAgaWYoIWNvc21ldGljQ2FzZVNlY29uZC5pbmNsdWRlcygnNCcpKXsNCiAgICAgICAgICAgIGNvc21ldGljQ2FzZVNlY29uZC5wdXNoKCc0Jyk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmFycmF5Q29udGFpbnNBbm90aGVyKGd4eGMsIGd4eGMxKSkgew0KICAgICAgICAgIGNvc21ldGljQ2xhc3NpZmljYXRpb24gPSAnMSc7DQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5hcnJheUNvbnRhaW5zQW5vdGhlcih6eWJ3LCB6eWJ3MSkpIHsNCiAgICAgICAgICBjb3NtZXRpY0NsYXNzaWZpY2F0aW9uID0gJzEnOw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuYXJyYXlDb250YWluc0Fub3RoZXIoc3lycSwgc3lycTEpKSB7DQogICAgICAgICAgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcxJzsNCiAgICAgICAgfSBlbHNlIGlmICh0aGlzLmFycmF5Q29udGFpbnNBbm90aGVyKGd4eGMsIGd4eGMyKSkgew0KICAgICAgICAgIGNvc21ldGljQ2xhc3NpZmljYXRpb24gPSAnMic7DQogICAgICAgICAgY29zbWV0aWNDYXNlID0gJzEnOw0KICAgICAgICB9IGVsc2UgaWYgKHRoaXMuYXJyYXlDb250YWluc0Fub3RoZXIoY3BqeCwgY3BqeDIpKSB7DQogICAgICAgICAgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcyJzsNCiAgICAgICAgICBjb3NtZXRpY0Nhc2UgPSAnMSc7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcyJzsNCiAgICAgICAgICBjb3NtZXRpY0Nhc2UgPSAnMic7DQogICAgICAgICAgbGV0IHJlcyA9IGF3YWl0IHRoaXMuY2F0ZWdvcnlDaGFuZ2VOZXcoMSk7DQogICAgICAgICAgaWYocmVzPT09MSB8fCByZXMgPT09Myl7DQogICAgICAgICAgICBpc1Byb2Nlc3MgPSBmYWxzZTsNCiAgICAgICAgICB9ZWxzZXsNCiAgICAgICAgICAgIGxldCBmb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IHRoaXMuZm9ybXVsYU1hdGVyaWFsRGF0YXM7DQogICAgICAgICAgICBsZXQgaXNGaXJzdCA9IGZhbHNlOw0KICAgICAgICAgICAgbGV0IGlzU2Vjb25kID0gZmFsc2U7DQogICAgICAgICAgICBmb3IobGV0IGl0ZW0gb2YgZm9ybXVsYU1hdGVyaWFsRGF0YXMpew0KICAgICAgICAgICAgICAgIGxldCBpc05ld01hdGVyaWFsID0gaXRlbS5pc05ld01hdGVyaWFsOw0KICAgICAgICAgICAgICAgIGxldCBpbmljTm1qeWwgPSBpdGVtLmluaWNObWp5bDsNCiAgICAgICAgICAgICAgICBpZihpc05ld01hdGVyaWFsPT0n5pivJyl7DQogICAgICAgICAgICAgICAgICBpc0ZpcnN0ID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB9ZWxzZSBpZihpbmljTm1qeWw9PSfmmK8nKXsNCiAgICAgICAgICAgICAgICAgIGlzU2Vjb25kID0gdHJ1ZTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZihpc0ZpcnN0KXsNCiAgICAgICAgICAgICAgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcxJzsNCiAgICAgICAgICAgICAgaXNQcm9jZXNzID0gdHJ1ZTsNCiAgICAgICAgICAgIH1lbHNlIGlmKGlzU2Vjb25kKXsNCiAgICAgICAgICAgICAgY29zbWV0aWNDbGFzc2lmaWNhdGlvbiA9ICcyJzsNCiAgICAgICAgICAgICAgY29zbWV0aWNDYXNlID0gJzEnOw0KICAgICAgICAgICAgICBpc1Byb2Nlc3MgPSB0cnVlOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBsZXQgcmVzMSA9IGF3YWl0IHRoaXMuY2F0ZWdvcnlDaGFuZ2VOZXcoMSk7DQogICAgICAgIGlmKHJlczE9PTMpew0KICAgICAgICAgIGlmKCFjb3NtZXRpY0Nhc2VGaXJzdC5pbmNsdWRlcygnMScpKXsNCiAgICAgICAgICAgIGNvc21ldGljQ2FzZUZpcnN0LnB1c2goJzEnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgbGV0IGZvcm11bGFNYXRlcmlhbERhdGFzID0gdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhczsNCiAgICAgICAgZm9yKGxldCBpdGVtIG9mIGZvcm11bGFNYXRlcmlhbERhdGFzKXsNCiAgICAgICAgICBsZXQgaXNOZXdNYXRlcmlhbCA9IGl0ZW0uaXNOZXdNYXRlcmlhbDsNCiAgICAgICAgICBsZXQgaW5pY05tanlsID0gaXRlbS5pbmljTm1qeWw7DQogICAgICAgICAgbGV0IHN5bWRJbmZvID0gaXRlbS5zeW1kSW5mbzsNCiAgICAgICAgICBpZihpc05ld01hdGVyaWFsPT0n5pivJyl7DQogICAgICAgICAgICBpZighY29zbWV0aWNDYXNlRmlyc3QuaW5jbHVkZXMoJzMnKSl7DQogICAgICAgICAgICAgIGNvc21ldGljQ2FzZUZpcnN0LnB1c2goJzMnKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgaWYoaW5pY05tanlsPT0n5pivJyl7DQogICAgICAgICAgICBpZighY29zbWV0aWNDYXNlU2Vjb25kLmluY2x1ZGVzKCcxJykpew0KICAgICAgICAgICAgICBjb3NtZXRpY0Nhc2VTZWNvbmQucHVzaCgnMScpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihzeW1kSW5mby5pbmRleE9mKCfpmLLmmZLliYInKSE9LTEgfHxzeW1kSW5mby5pbmRleE9mKCflhYnnqLPlrprliYInKSE9LTEgKXsNCiAgICAgICAgICAgIGlmKCFjb3NtZXRpY0Nhc2VTZWNvbmQuaW5jbHVkZXMoJzInKSl7DQogICAgICAgICAgICAgIGNvc21ldGljQ2FzZVNlY29uZC5wdXNoKCcyJyk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuZm9ybS5jb3NtZXRpY0Nhc2VGaXJzdCA9IGNvc21ldGljQ2FzZUZpcnN0Ow0KICAgICAgICB0aGlzLmZvcm0uY29zbWV0aWNDYXNlU2Vjb25kID0gY29zbWV0aWNDYXNlU2Vjb25kOw0KICAgICAgICBpZihpc1Byb2Nlc3Mpew0KICAgICAgICAgIHRoaXMuZm9ybS5jb3NtZXRpY0NsYXNzaWZpY2F0aW9uID0gY29zbWV0aWNDbGFzc2lmaWNhdGlvbjsNCiAgICAgICAgICB0aGlzLmZvcm0uY29zbWV0aWNDYXNlID0gY29zbWV0aWNDYXNlOw0KICAgICAgICB9DQoNCiAgICAgIH0NCiAgICB9LA0KICAgIGFycmF5Q29udGFpbnNBbm90aGVyKGFycjEsIGFycjIpIHsNCiAgICAgIHJldHVybiBhcnIxLnNvbWUoaXRlbSA9PiBhcnIyLmluY2x1ZGVzKGl0ZW0pKTsNCiAgICB9LA0KICAgIHRvQ2hvb3NlKCl7DQogICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIGFzeW5jIHNlbGVjdGVkKGZvcm11bGFJZCwgbGFib3JhdG9yeUNvZGUpIHsNCiAgICAgIHRoaXMuZm9ybS5vbGRGb3JtdWxhQ29kZSA9IGxhYm9yYXRvcnlDb2RlOw0KICAgICAgdGhpcy5mb3JtLmNvcHlGb3JtdWxhSWQgPSBmb3JtdWxhSWQ7DQogICAgICB0aGlzLnZpc2libGUgPSBmYWxzZTsNCiAgICAgIGxldCBmb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IFtdOw0KICAgICAgaWYgKGxhYm9yYXRvcnlDb2RlICYmIGZvcm11bGFJZCkgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgZ2V0Rm9ybXVsYUxhYk5vSW5mb0J5Q29kZSh7aWQ6IGZvcm11bGFJZH0pOw0KICAgICAgICBpZiAocmVzLmRhdGEpIHsNCiAgICAgICAgICBpZihyZXMuZGF0YS5kYXRhTGlzdCl7DQogICAgICAgICAgICBmb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IHJlcy5kYXRhLmRhdGFMaXN0Ow0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihyZXMuZGF0YS50aXBzKXsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5hZGRUaXBzID0gcmVzLmRhdGEudGlwczsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtdWxhTWF0ZXJpYWxEYXRhcyA9IGZvcm11bGFNYXRlcmlhbERhdGFzOw0KICAgICAgICBpZihmb3JtdWxhTWF0ZXJpYWxEYXRhcyAmJiBmb3JtdWxhTWF0ZXJpYWxEYXRhcy5sZW5ndGg+MCl7DQogICAgICAgICAgdGhpcy5jb2RlQ2hhbmdlKDEpOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nphY3mlrkhJyk7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVGb3JtdWxhU3BlY0FkZCgpew0KICAgICAgIHRoaXMuc3BlY09wZW4gPSB0cnVlOw0KICAgICAgIHRoaXMudXNlckl0ZW1BcnJheSA9IFtdOw0KICAgICAgIHRoaXMuc3BlY0lkID0gbnVsbDsNCiAgICAgICB0aGlzLnVzZXJJdGVtQXJyYXkgPSB0aGlzLml0ZW1BcnJheTsNCiAgICB9LA0KICAgIGFzeW5jIGhhbmRsZUZvcm11bGFTcGVjRWRpdChyb3cpIHsNCiAgICAgIHRoaXMuc3BlY09wZW4gPSB0cnVlOw0KICAgICAgbGV0IGRhdGFPYmogPSBhd2FpdCBxdWVyeU1hdGVyaWFsRm9ybXVsYVNwZWNEYXRhRGV0YWlsKHtpZDpyb3cuaWR9KTsNCiAgICAgIGxldCBqY3htSnNvbiA9IGRhdGFPYmouamN4bUpzb247DQogICAgICBpZihqY3htSnNvbil7DQogICAgICAgIHRoaXMudXNlckl0ZW1BcnJheSA9IEpTT04ucGFyc2UoamN4bUpzb24pOw0KICAgICAgfWVsc2V7DQogICAgICAgIHRoaXMudXNlckl0ZW1BcnJheSA9IFtdOw0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtLnR5cGUgPSBkYXRhT2JqLnR5cGUrJyc7DQogICAgICB0aGlzLnNwZWNJZCA9IGRhdGFPYmouaWQ7DQogICAgfSwNCiAgICBhc3luYyByZWZyZXNoRm9ybXVsYUxlZ2FsR3kodHlwZSl7DQogICAgICBpZih0eXBlPT09JzEnKXsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgn5piv5ZCm5Yi35paw5bel6Im65pWw5o2uLOS8mua4heepuuW3suWhq+aVsOaNriEnKQ0KICAgICAgfQ0KICAgICAgIGxldCBpZCA9IHRoaXMuZm9ybS5pZDsNCiAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSB0cnVlOw0KICAgICAgIGxldCBkYXRhID0gYXdhaXQgcXVlcnlGb3JtdWxhTGVnYWxHeSh7aWR9KTsNCiAgICAgICBsZXQgZ3lqc0RhdGEgPSBkYXRhLmRhdGE7DQogICAgICAgIGxldCBneWpzID0gZ3lqc0RhdGEuZ3lqczsNCiAgICAgICAgaWYoZ3lqcyl7DQogICAgICAgICAgdGhpcy5neWpzRGF0YUxpc3QgPSBneWpzLm1hcChuYW1lID0+ICh7IG5hbWUgfSkpOzsNCiAgICAgICAgfWVsc2V7DQogICAgICAgICAgdGhpcy5neWpzRGF0YUxpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgemZ5bCA9IGd5anNEYXRhLnpmeWw7DQogICAgICAgIGlmKGd5anMpew0KICAgICAgICAgIHRoaXMuemZ5bERhdGFMaXN0ID0gemZ5bC5tYXAobmFtZSA9PiAoeyBuYW1lIH0pKTs7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgIHRoaXMuemZ5bERhdGFMaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8v5o+Q5Lqk54m55q6K5Y6f5paZ5L+h5oGvDQogICAgYXN5bmMgc3VibWl0VGlwc01hdGVyaWFsRm9ybXVsYUluZm8oKSB7DQogICAgICBsZXQgZm9ybXVsYUlkID0gdGhpcy5mb3JtLmlkOw0KICAgICAgbGV0IHNwZWNNYXRlcmlhbERhdGFzID0gdGhpcy5zcGVjTWF0ZXJpYWxEYXRhczsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICB0cnkgew0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgYWRkRm9ybXVsYVNwZWNNYXRlcmlhbERhdGEoe2lkOmZvcm11bGFJZCxzcGVjTWF0ZXJpYWxEYXRhczogSlNPTi5zdHJpbmdpZnkoc3BlY01hdGVyaWFsRGF0YXMpfSk7DQogICAgICAgIHRoaXMubXNnU3VjY2Vzcygn5pON5L2c5oiQ5YqfIScpOw0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvL+aPkOS6pOmFjeaWueS9v+eUqOebrueahA0KICAgIGFzeW5jIHN1Ym1pdFN5bWRJbmZvKCkgew0KICAgICAgbGV0IGZvcm11bGFJZCA9IHRoaXMuZm9ybS5pZDsNCiAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICB0cnkgew0KICAgICAgICBsZXQgZm9ybXVsYVN5bWQgPSBbXTsNCiAgICAgICAgbGV0IGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCA9IHRoaXMuY29tcG9zaXRpb25UYWJsZURhdGFMaXN0Ow0KICAgICAgICBmb3IobGV0IGl0ZW0gb2YgY29tcG9zaXRpb25UYWJsZURhdGFMaXN0KXsNCiAgICAgICAgICBmb3JtdWxhU3ltZC5wdXNoKHsNCiAgICAgICAgICAgIGNoaU5hbWU6aXRlbS5jaGlOYW1lLA0KICAgICAgICAgICAgY3BwZlN5bWQ6aXRlbS5jcHBmU3ltZCwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICBsZXQgcmVzID0gYXdhaXQgYWRkRm9ybXVsYVN5bWRGb3JtKHtpZDpmb3JtdWxhSWQsZm9ybXVsYVN5bWQ6IEpTT04uc3RyaW5naWZ5KGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCl9KTsNCiAgICAgICAgdGhpcy5tc2dTdWNjZXNzKCfmk43kvZzmiJDlip8hJyk7DQogICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFN1bW1hcmllcyhwYXJhbSkgew0KICAgICAgY29uc3QgeyBjb2x1bW5zLCBkYXRhIH0gPSBwYXJhbTsNCiAgICAgIGNvbnN0IHN1bXMgPSBbXTsNCiAgICAgIGNvbHVtbnMuZm9yRWFjaCgoY29sdW1uLCBpbmRleCkgPT4gew0KICAgICAgICBpZiAoaW5kZXggPT09IDApIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICflkIjorqEnOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZighWyfmr5TkvosoJSknXS5pbmNsdWRlcyhjb2x1bW4ubGFiZWwpKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAnJzsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YS5tYXAoaXRlbSA9PiBOdW1iZXIoaXRlbVtjb2x1bW4ucHJvcGVydHldKSk7DQogICAgICAgIGlmICghdmFsdWVzLmV2ZXJ5KHZhbHVlID0+IGlzTmFOKHZhbHVlKSkpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gTnVtYmVyKGN1cnIpOw0KICAgICAgICAgICAgaWYgKCFpc05hTih2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMua2VlcERpZ2l0cyhwcmV2ICsgY3VyciwxMCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gdGhpcy5rZWVwRGlnaXRzKHByZXYsMTApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApOw0KICAgICAgICAgIHN1bXNbaW5kZXhdICs9ICcnOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gJyc7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHN1bXM7DQogICAgfSwNCiAgICBnZXRTdW1tYXJpZXNQRm9ybXVsYShwYXJhbSkgew0KICAgICAgY29uc3QgeyBjb2x1bW5zLCBkYXRhIH0gPSBwYXJhbTsNCiAgICAgIGNvbnN0IHN1bXMgPSBbXTsNCiAgICAgIGNvbHVtbnMuZm9yRWFjaCgoY29sdW1uLCBpbmRleCkgPT4gew0KICAgICAgICBpZiAoaW5kZXggPT09IDApIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICflkIjorqEnOw0KICAgICAgICAgIHJldHVybjsNCiAgICAgICAgfQ0KICAgICAgICBpZighWyfmr5TkvosnXS5pbmNsdWRlcyhjb2x1bW4ubGFiZWwpKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAnJzsNCiAgICAgICAgICByZXR1cm47DQogICAgICAgIH0NCiAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YS5tYXAoaXRlbSA9PiBOdW1iZXIoaXRlbVtjb2x1bW4ucHJvcGVydHldKSk7DQogICAgICAgIGlmICghdmFsdWVzLmV2ZXJ5KHZhbHVlID0+IGlzTmFOKHZhbHVlKSkpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gTnVtYmVyKGN1cnIpOw0KICAgICAgICAgICAgaWYgKCFpc05hTih2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHRoaXMua2VlcERpZ2l0cyhwcmV2ICsgY3VyciwxMCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gdGhpcy5rZWVwRGlnaXRzKHByZXYsMTApOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApOw0KICAgICAgICAgIHN1bXNbaW5kZXhdICs9ICcnOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gJyc7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIHN1bXM7DQogICAgfSwNCiAgICBmb3JtdWxhTWF0ZXJpYWxCYWNrKG8pIHsNCiAgICAgIGxldCBzdGF0dXMgPSBvLnJvdy5zdGF0dXM7DQogICAgICBpZihzdGF0dXMpew0KICAgICAgICBpZihzdGF0dXM9PTMpew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAnb3JhbmdlJw0KICAgICAgICAgIH0NCiAgICAgICAgfWVsc2UgaWYoc3RhdHVzPT00IHx8IHN0YXR1cz09NSl7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjOGNjMGE4Jw0KICAgICAgICAgICAgLy8gYmFja2dyb3VuZDogJyM5OTk5ZmYnDQogICAgICAgICAgfQ0KICAgICAgICB9ZWxzZSBpZihzdGF0dXM+MCl7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZWQnDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjb21wb3NpdGlvblRhYmxlU3R5bGUobykgew0KICAgICAgbGV0IHBlcmNlcnQgPSBvLnJvdy5wZXJjZXJ0Ow0KICAgICAgbGV0IGlzQ29sb3IgPSBvLnJvdy5pc0NvbG9yOw0KICAgICAgaWYoaXNDb2xvcj09MSl7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgY29sb3I6ICdyZWQnDQogICAgICAgIH0NCiAgICAgIH1lbHNlew0KICAgICAgICBpZihwZXJjZXJ0PD0wLjEpew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBjb2xvcjogJ2JsdWUnDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBjb21wb3NpdGlvbkNlbGxUYWJsZVN0eWxlKHsgcm93LCBjb2x1bW4gfSkgew0KICAgICAgaWYgKGNvbHVtbi5sYWJlbCA9PT0gJ+S4reaWh+WQjeensCcgfHwgY29sdW1uLmxhYmVsID09PSAnSU5DSSDkuK3mloflkI0nKSB7DQogICAgICAgIGlmIChyb3cuaXNUaXBzPT09MSkgew0KICAgICAgICAgIHJldHVybiAiYmFja2dyb3VuZDojOTk2NkZGIjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQogICAgbWF0ZXJpYWxEZXRhaWxzKHJvdyl7DQogICAgICBpZihyb3cudHlwZT09MCl7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKGFzeW5jICgpID0+IHsNCiAgICAgICAgICB0aGlzLiRyZWZzLnNvZnR3YXJlTWF0ZXJpYWxTYXZlLnJlc2V0KCk7DQogICAgICAgICAgdGhpcy4kcmVmcy5zb2Z0d2FyZU1hdGVyaWFsU2F2ZS5pbml0KCk7DQogICAgICAgICAgbGV0IGRhdGFSb3cgPSB7aWQ6cm93Lm1hdGVyaWFsSWR9Ow0KICAgICAgICAgIHRoaXMuJHJlZnMuc29mdHdhcmVNYXRlcmlhbFNhdmUuaGFuZGxlVXBkYXRlKGRhdGFSb3csJ+afpeeci+WOn+aWmScsMCk7DQogICAgICAgICAgdGhpcy4kcmVmcy5zb2Z0d2FyZU1hdGVyaWFsU2F2ZS5vcGVuID0gdHJ1ZTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBzZWxlY3RhYmxlKHJvdyxpbmRleCl7DQogICAgICBpZiAocm93LmlzVXNlID09IDEpIHsNCiAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgYWRkRm9ybXVsYUd5anNCZWlhbkluZm8odHlwZSkgew0KICAgICAgbGV0IGdvbmd5aWppYW5zaHVCZWlhbiA9IHt9Ow0KICAgICAgZ29uZ3lpamlhbnNodUJlaWFuLmd5anMgPSB0aGlzLmd5anNEYXRhTGlzdC5tYXAoaXRlbSA9PiBpdGVtLm5hbWUpOw0KICAgICAgZ29uZ3lpamlhbnNodUJlaWFuLnpmeWwgPSB0aGlzLnpmeWxEYXRhTGlzdC5tYXAoaXRlbSA9PiBpdGVtLm5hbWUpOw0KICAgICAgbGV0IHBhcmFtID0gew0KICAgICAgICBpZDogdGhpcy5mb3JtLmlkLA0KICAgICAgICBnb25neWlqaWFuc2h1OiBKU09OLnN0cmluZ2lmeShnb25neWlqaWFuc2h1QmVpYW4pLA0KICAgICAgICB0eXBlDQogICAgICB9Ow0KICAgICAgaWYodHlwZT09PTEpew0KICAgICAgICBnb25neWlqaWFuc2h1QmVpYW4uZ3lqcyA9IHRoaXMuZ3lqc0JlaWFuRGF0YUxpc3QubWFwKGl0ZW0gPT4gaXRlbS5uYW1lKTsNCiAgICAgICAgZ29uZ3lpamlhbnNodUJlaWFuLnpmeWwgPSB0aGlzLnpmeWxCZWlhbkRhdGFMaXN0Lm1hcChpdGVtID0+IGl0ZW0ubmFtZSk7DQogICAgICAgIHBhcmFtID0gew0KICAgICAgICAgIGlkOiB0aGlzLmZvcm0uaWQsDQogICAgICAgICAgZ29uZ3lpamlhbnNodUJlaWFuOiBKU09OLnN0cmluZ2lmeShnb25neWlqaWFuc2h1QmVpYW4pLA0KICAgICAgICAgIHR5cGUNCiAgICAgICAgfTsNCiAgICAgIH0NCiAgICAgIGxldCByZXMgPSBhd2FpdCBhZGRGb3JtdWxhR3lqc0JlaWFuSW5mbyhwYXJhbSk7DQogICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+S/neWtmOaIkOWKnyEnKTsNCiAgICB9LA0KICAgIGFzeW5jIGNoYW5nZUZ1bigpew0KICAgICAgdGhpcy5jaGFuZ2VUZW1wbGF0ZSgwKTsNCiAgICB9LA0KICAgIGFzeW5jIGNoYW5nZVRlbXBsYXRlKHR5cGUpIHsNCiAgICAgIGlmKHRoaXMuZm9ybS5jdXJyZW50VGVtcGxhdGVJZCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIGlmKHR5cGU9PT0xKXsNCiAgICAgICAgICAgIGF3YWl0IHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOW4puWFpSzkvJrmuIXnqbrlt7LloavmlbDmja4hJykNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5idG5Mb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldEJjcFRlbXBsYXRlKHRoaXMuZm9ybS5jdXJyZW50VGVtcGxhdGVJZCkNCiAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSAmJiByZXMuZGF0YS5pdGVtQXJyYXkpIHsNCiAgICAgICAgICAgIGxldCBpdGVtQXJyYXkgPSBKU09OLnBhcnNlKHJlcy5kYXRhLml0ZW1BcnJheSk7DQogICAgICAgICAgICBmb3IobGV0IGl0ZW0gb2YgaXRlbUFycmF5KXsNCiAgICAgICAgICAgICAgaXRlbS5zdGFuZGFyZFZhbCA9ICcnOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgdGhpcy5pdGVtQXJyYXkgPSBpdGVtQXJyYXk7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0gY2F0Y2ggKGUpIHsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBkZWxJdGVtKGkpIHsNCiAgICAgIHRoaXMuaXRlbUFycmF5LnNwbGljZShpLDEpDQogICAgfSwNCiAgICBzZWxlY3RQcm9qZWN0KCkgew0KICAgICAgdGhpcy5jYXRlZ29yeU9wZW4gPSB0cnVlDQogICAgICB0aGlzLnhtSWRzID0gW10NCiAgICB9LA0KICAgIHNlbGVjdENhdGVnb3J5KGNhdGVnb3J5KSB7DQogICAgICBmb3IgKGNvbnN0IGl0ZW0gb2YgY2F0ZWdvcnkuYXJyYXkpIHsNCiAgICAgICAgdGhpcy5zZWxlY3RYbShpdGVtLmlkKQ0KICAgICAgfQ0KICAgIH0sDQogICAgc2VsZWN0WG0oaWQpIHsNCiAgICAgIGlmKHRoaXMueG1JZHMuaW5jbHVkZXMoaWQpKSB7DQogICAgICAgIHRoaXMueG1JZHMgPSB0aGlzLnhtSWRzLmZpbHRlcihpPT5pICE9PSBpZCkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMueG1JZHMucHVzaChpZCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvbmZpcm1YbSgpIHsNCiAgICAgIGlmKHRoaXMueG1JZHMubGVuZ3RoKSB7DQogICAgICAgIGxldCBhcnIgPSB0aGlzLmpjWG1MaXN0LmZpbHRlcihpPT4gdGhpcy54bUlkcy5pbmNsdWRlcyhpLmlkKSkNCiAgICAgICAgY29uc3QgaXRlbUFycmF5ID0gdGhpcy5pdGVtQXJyYXkNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIGFycikgew0KICAgICAgICAgIGlmKCFpdGVtQXJyYXkubWFwKGk9PmkuaWQpLmluY2x1ZGVzKGEuaWQpKSB7DQogICAgICAgICAgICBjb25zdCBvID0gew0KICAgICAgICAgICAgICBpZDogYS5pZCwNCiAgICAgICAgICAgICAgbGFiZWw6IGEudGl0bGUsDQogICAgICAgICAgICAgIHR5cGU6IGEuY2F0ZWdvcnksDQogICAgICAgICAgICAgIHNlcmlhbE5vOiBhLnNlcSwNCiAgICAgICAgICAgICAgc3RhbmRhcmQ6IGEuc3RhbmRhcmQsDQogICAgICAgICAgICAgIGZyZXF1ZW5jeTogYS5mcmVxdWVuY3ksDQogICAgICAgICAgICAgIHN0YW5kYXJkVmFsOiBhLnN0YW5kYXJkVmFsLA0KICAgICAgICAgICAgICB5cUlkczogYS55cUlkcz9hLnlxSWRzLnNwbGl0KCcsJykubWFwKGk9Pk51bWJlcihpKSk6W10sDQogICAgICAgICAgICB9DQogICAgICAgICAgICBsZXQgbWV0aG9kQXJyYXkgPSBbXQ0KICAgICAgICAgICAgY29uc3QgZGF0YUFycmF5ID0gW10NCiAgICAgICAgICAgIGlmKGEubWV0aG9kRGVzYykgew0KICAgICAgICAgICAgICBjb25zdCBtZXRob2RUZW1wbGF0ZSA9IGEubWV0aG9kRGVzYy5yZXBsYWNlKC/vvIgvZywnKCcpLnJlcGxhY2UoL++8iS9nLCcpJykucmVwbGFjZSgvXHMvZywiIikNCiAgICAgICAgICAgICAgby5tZXRob2RUZW1wbGF0ZSA9IG1ldGhvZFRlbXBsYXRlLnJlcGxhY2UoL1woXHtwYXJhbX1cKS9nLCdfX19fXycpDQogICAgICAgICAgICAgIG1ldGhvZEFycmF5ID0gby5tZXRob2RUZW1wbGF0ZS5zcGxpdCgnX19fX18nKQ0KICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IG1ldGhvZEFycmF5Lmxlbmd0aCAtMTsgaSsrKSB7DQogICAgICAgICAgICAgICAgY29uc3QgbyA9IHt9DQogICAgICAgICAgICAgICAgb1sncGFyYW1fYXZhXycgKyBpXSA9ICcnDQogICAgICAgICAgICAgICAgZGF0YUFycmF5LnB1c2gobykNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgby5tZXRob2RBcnJheSA9IG1ldGhvZEFycmF5DQogICAgICAgICAgICBvLmRhdGFBcnJheSA9IGRhdGFBcnJheQ0KICAgICAgICAgICAgaXRlbUFycmF5LnB1c2gobykNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaXRlbUFycmF5LnNvcnQoKGEsYik9PmEuc2VyaWFsTm8gLSBiLnNlcmlhbE5vKQ0KICAgICAgICB0aGlzLmNhdGVnb3J5T3BlbiA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVTaGFyZShyb3cpIHsNCiAgICAgIHRoaXMuc2hhcmVPcGVuID0gdHJ1ZTsNCiAgICAgIGxldCBzaGFyZURlcHREYXRhcyA9IGF3YWl0IHF1ZXJ5Rm9ybXVsYVNoYXJlRGVwdERhdGFMaXN0KHtpZDpyb3cuZGVwdElkfSk7DQogICAgICB0aGlzLnNoYXJlRGVwdERhdGFzID0gc2hhcmVEZXB0RGF0YXM7DQogICAgICBsZXQgc2hhcmVEZXB0SWRzID0gYXdhaXQgcXVlcnlGb3JtdWxhU2hhcmVEZXB0RGF0YURldGFpbCh7aWQ6cm93LmlkfSk7DQogICAgICB0aGlzLnNoYXJlRGVwdElkcyA9IHNoYXJlRGVwdElkczsNCiAgICAgIGxldCBjaGVja1JvdyA9IHsNCiAgICAgICAgaWQ6cm93LmlkLA0KICAgICAgICBkZXB0SWQ6cm93LmRlcHRJZA0KICAgICAgfTsNCiAgICAgIHRoaXMuY2hlY2tSb3cgPSBjaGVja1JvdzsNCiAgICAgfSwNCiAgICBhc3luYyBzdWJtaXRTaGFyZUZvcm11bGFJbmZvKCl7DQogICAgICAgbGV0IHNoYXJlRGVwdElkcyA9IHRoaXMuc2hhcmVEZXB0SWRzOw0KICAgICAgIGlmKHNoYXJlRGVwdElkcyAmJiBzaGFyZURlcHRJZHMubGVuZ3RoPjApew0KICAgICAgICAgIHRoaXMuYnRuTG9hZGluZyA9IHRydWU7DQogICAgICAgICAgbGV0IGNoZWNrUm93ID0gdGhpcy5jaGVja1JvdzsNCiAgICAgICAgICBjaGVja1Jvdy5zaGFyZURlcHRJZHMgPSBzaGFyZURlcHRJZHMuam9pbigiLCIpOw0KICAgICAgICAgIGxldCByZXMgPSBhd2FpdCBhZGRGb3JtdWxhU2hhcmVEYXRhSW5mbyhjaGVja1Jvdyk7DQogICAgICAgICAgdGhpcy5zaGFyZU9wZW4gPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmJ0bkxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLm1zZ1N1Y2Nlc3MoJ+aTjeS9nOaIkOWKnycpOw0KICAgICAgIH1lbHNlIHsNCiAgICAgICAgICB0aGlzLm1zZ0Vycm9yKCfor7fpgInmi6nopoHliIbkuqvnmoTpg6jpl6ghJyk7DQogICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQ29tcG9zaXRpb25RdWVyeSgpew0KICAgICAgIGxldCBld2dDb2xvciA9IHRoaXMucXVlcnlQYXJhbXMuZXdnQ29sb3I7DQogICAgICAgbGV0IGNvbWNsdXNpb25UeXBlID0gdGhpcy5xdWVyeVBhcmFtcy5jb21jbHVzaW9uVHlwZTsNCiAgICAgICBsZXQgY29tcG9zaXRpb25UYWJsZURhdGFMaXN0ID0gdGhpcy5jb21wb3NpdGlvblRhYmxlRGF0YUxpc3RCYWNrOw0KICAgICAgIGlmKGV3Z0NvbG9yKXsNCiAgICAgICAgIGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCA9IGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdC5maWx0ZXIoaT0+aS5kYXRhT2JqLmV3Z0NvbG9yID09PSBld2dDb2xvcik7DQogICAgICAgfQ0KICAgICAgIGlmKGNvbWNsdXNpb25UeXBlKXsNCiAgICAgICAgIGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCA9IGNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdC5maWx0ZXIoaT0+aS5jb21wb25lbnRUeXBlID09PSBjb21jbHVzaW9uVHlwZSk7DQogICAgICAgfQ0KICAgICAgIHRoaXMuY29tcG9zaXRpb25UYWJsZURhdGFMaXN0ID0gY29tcG9zaXRpb25UYWJsZURhdGFMaXN0Ow0KICAgIH0sDQogICAgcmVzZXRDb21wb3NpdGlvblF1ZXJ5KCl7DQogICAgICB0aGlzLmNvbXBvc2l0aW9uVGFibGVEYXRhTGlzdCA9IHRoaXMuY29tcG9zaXRpb25UYWJsZURhdGFMaXN0QmFjazsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZXdnQ29sb3IgPSBudWxsOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21jbHVzaW9uVHlwZSA9IG51bGw7DQogICAgfSwNCiAgICBoYW5kbGVNYXRlcmlhbFF1ZXJ5KCl7DQogICAgICAgbGV0IGNvbWNsdXNpb25UeXBlID0gdGhpcy5xdWVyeVBhcmFtcy5jb21jbHVzaW9uVHlwZTsNCiAgICAgICBsZXQgZm9ybXVsYVRhYmxlRGF0YUxpc3QgPSB0aGlzLmZvcm11bGFUYWJsZURhdGFMaXN0QmFjazsNCiAgICAgICBpZihjb21jbHVzaW9uVHlwZSl7DQogICAgICAgICBmb3JtdWxhVGFibGVEYXRhTGlzdCA9IGZvcm11bGFUYWJsZURhdGFMaXN0LmZpbHRlcihpPT5pLmNvbXBvbmVudFR5cGUgPT09IGNvbWNsdXNpb25UeXBlKTsNCiAgICAgICB9DQogICAgICAgdGhpcy5mb3JtdWxhVGFibGVEYXRhTGlzdCA9IGZvcm11bGFUYWJsZURhdGFMaXN0Ow0KICAgIH0sDQogICAgcmVzZXRNYXRlcmlhbFF1ZXJ5KCl7DQogICAgICB0aGlzLmZvcm11bGFUYWJsZURhdGFMaXN0ID0gdGhpcy5mb3JtdWxhVGFibGVEYXRhTGlzdEJhY2s7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbWNsdXNpb25UeXBlID0gbnVsbDsNCiAgICB9LA0KICAgIGlzRWRpdFN0YW5kYXJkKGlkKXsNCiAgICAgICBsZXQgaXNPcHIgPSB0cnVlOw0KICAgICAgIGxldCBhcnIgPSBbMSwyLDddOw0KICAgICAgIGlmKGFyci5pbmNsdWRlcyhpZCkpew0KICAgICAgICAgaXNPcHIgPSBmYWxzZTsNCiAgICAgICB9DQogICAgICAgcmV0dXJuIGlzT3ByOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["saveOrUpdate.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA60DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "saveOrUpdate.vue", "sourceRoot": "src/views/software/softwareDevelopingFormula", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 添加或修改研发配方对话框 -->\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n      <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n        <el-tab-pane v-for=\"(formula,index) in formulaTabs\" :key=\"index\" :label=\"formula.title\"\r\n                     :name=\"formula.code\">\r\n          <template v-if=\"formula.code==='base'\">\r\n            <fieldset>\r\n              <legend>项目信息</legend>\r\n              <el-row v-if=\"form.id\">\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    {{form.projectNo}}\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row v-else>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label='项目编码' prop=\"projectNo\">\r\n                    <el-select style=\"width: 500px\" clearable filterable v-model=\"form.projectNo\" @change=\"projectChange\">\r\n                      <el-option\r\n                        v-for=\"item in projectList\"\r\n                        :key=\"item.projectNo\"\r\n                        :label=\"item.projectNo+'('+item.productName+'|'+item.customerName+')'\"\r\n                        :value=\"item.projectNo\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户名称\" prop=\"customerName\">\r\n                    <el-input disabled=\"true\" size=\"small\" v-model=\"form.customerName\" placeholder=\"请输入客户名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品名称\" prop=\"productName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.productName\" placeholder=\"请输入产品名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"品牌名称\" prop=\"brandName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.brandName\" placeholder=\"请输入品牌名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"系列名称\" prop=\"seriesName\">\r\n                    <el-input :disabled=\"isEdit\" size=\"small\" v-model=\"form.seriesName\" placeholder=\"请输入系列名称\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"子名称\" prop=\"itemName\">\r\n                    <el-select clearable filterable v-model=\"form.itemName\" @change=\"itemNameChange\">\r\n                      <el-option\r\n                        v-for=\"item in itemNames\"\r\n                        :key=\"item.id\"\r\n                        :label=\"item.text\"\r\n                        :value=\"item.id\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"用途\" prop=\"purpose\">\r\n                    <el-select clearable v-model=\"form.purpose\" placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in purposeOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :disabled=\"item.isShow===0\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n<!--              <el-row>-->\r\n<!--                <el-col :span=\"8\">-->\r\n<!--                  <el-form-item label=\"状态\" prop=\"formulaStatus\">-->\r\n<!--                    <el-radio-group v-model=\"form.formulaStatus\">-->\r\n<!--                      <el-radio :label=\"0\">正常</el-radio>-->\r\n<!--                      <el-radio :label=\"1\">停用</el-radio>-->\r\n<!--                     </el-radio-group>-->\r\n<!--                  </el-form-item>-->\r\n<!--                </el-col>-->\r\n<!--              </el-row>-->\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>编码信息</legend>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方编码\" prop=\"formulaCode\">\r\n                    {{form.formulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"产品分类代码\" prop=\"cpfldm\">\r\n                    {{form.cpfldm}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"化妆品分类\" prop=\"cosmeticClassification\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156604264.png\" style=\"height: 500px\" >\r\n                          <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156703377.png\" style=\"height: 500px\" >\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n                      化妆品分类\r\n                    </div>\r\n                    <el-select clearable v-model=\"form.cosmeticClassification\">\r\n                      <el-option\r\n                        v-for=\"item in cosmeticClassificationOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='1'\" label=\"情形\" prop=\"cosmeticCaseFirst\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseFirst\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseFirstOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2'\" label=\"情形\" prop=\"cosmeticCase\">\r\n                    <el-select clearable v-model=\"form.cosmeticCase\">\r\n                      <el-option\r\n                        v-for=\"item in caseOptions\"\r\n                        :key=\"item.dictValue\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictValue\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item v-if=\"form.cosmeticClassification==='2' && form.cosmeticCase==='1'\" label=\"情形\" prop=\"cosmeticCaseSecond\">\r\n                    <el-checkbox-group  v-model=\"form.cosmeticCaseSecond\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in cosmeticCaseSecondOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"实验室编码\" prop=\"laboratoryCode\">\r\n                    <el-input v-model=\"form.laboratoryCode\" placeholder=\"请输入实验室编码\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col v-if=\"form.oldFormulaCode\" :span=\"8\">\r\n                  <el-form-item label=\"复制的配方编码\" prop=\"oldFormulaCode\">\r\n                      {{form.oldFormulaCode}}\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"配方类别\" prop=\"categoryText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.categoryText\"\r\n                      :options=\"categoryArray\"\r\n                      :props=\"categoryProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"CIR历史用量\" prop=\"cirText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.cirText\"\r\n                      :options=\"cirDataArray\"\r\n                      :props=\"cirDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"毒理使用量参考\" prop=\"duliText\">\r\n                    <el-cascader\r\n                      clearable\r\n                      :show-all-levels=\"false\"\r\n                      v-model=\"form.duliText\"\r\n                      :options=\"duliDataArray\"\r\n                      :props=\"duliDataProps\"\r\n                    ></el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n            <fieldset>\r\n              <legend>备案相关</legend>\r\n              <el-divider content-position=\"left\">功效宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxc\" style=\"width: 1000px\"  @change=\"codeChange(1)\">\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 0)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 1)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-checkbox\r\n                    style=\"width: 80px\"\r\n                    v-for=\"dict in efficacyOptions.filter(i=> i.remark == 3)\"\r\n                    :key=\"dict.id\"\r\n                    :label=\"dict.id\" >\r\n                    <i class=\"el-icon-s-check\" v-if=\"dict.cssClass==='gz'\" style=\"margin-right: 5px;color: green;\"></i><i class=\"ali-icon ali-yiliaomeirongke\" v-if=\"dict.cssClass==='rx'\" style=\"margin-right: 5px;color: blue;\"></i>{{dict.id}}.{{dict.title}}\r\n                  </el-checkbox>\r\n                </el-row>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">其他特别宣称</el-divider>\r\n              <el-checkbox-group v-model=\"form.gxxcOther\">\r\n                <el-checkbox\r\n                  v-for=\"dict in otherSpecialClaimsOptions\"\r\n                  :key=\"dict.id\"\r\n                  :label=\"dict.id\">\r\n                  {{dict.id}}.{{ dict.title }}\r\n                </el-checkbox>\r\n              </el-checkbox-group>\r\n              <el-divider content-position=\"left\">申报类别(特殊化妆品填报)</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"染发类\">\r\n                    <el-checkbox-group @change=\"categoryChange\"  v-model=\"form.ranfalei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in rflOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"祛斑美白类\">\r\n                    <el-checkbox-group\r\n                      @change=\"categoryChange\" v-model=\"form.qubanmeibailei\">\r\n                      <el-checkbox\r\n                        v-for=\"dict in qbmblOptions\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictValue\">\r\n                        {{ dict.dictLabel }}\r\n                      </el-checkbox>\r\n                    </el-checkbox-group>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"\" prop=\"fangshailei\">\r\n                    <el-checkbox  @change=\"categoryChange\" v-model=\"form.fangshailei\">防晒类</el-checkbox>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"SPF值\" prop=\"sfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.sfa\" placeholder=\"SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"PA值\" prop=\"pa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.pa\" placeholder=\"PA值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"浴后SPF值\" prop=\"yushousfa\">\r\n                    <el-input  @input=\"categoryChange('1')\" style=\"width: 120px\" v-model=\"form.yushousfa\" placeholder=\"浴后SPF值\"/>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item prop=\"xgx\">\r\n                    <div slot=\"label\">\r\n                      <el-tooltip >\r\n                        <div slot=\"content\">\r\n                          1.特定宣称：宣传试用敏感皮肤，无泪配方<br/>\r\n                          2.特定宣称：原料功效<br/>\r\n                          3.宣称温和：无刺激<br/>\r\n                          4.宣称量化指标（时间、统计数据等）<br/>\r\n                          5.孕妇和哺乳期妇女适用\r\n                        </div>\r\n                        <i class=\"el-icon-question\" ></i>\r\n                      </el-tooltip>\r\n\r\n                      <el-checkbox  @change=\"categoryChange\" v-model=\"form.xingongxiao\">新功效</el-checkbox>\r\n                    </div>\r\n                    <el-input @input=\"categoryChange('1')\" v-model=\"form.xingongxiaocontent\" autosize type=\"textarea\" placeholder=\"请输入新功效\" style=\"width: 800px\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">作用部位</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.zybw\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in zybwOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">产品剂型</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.cpjx\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in cpjxOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">适用人群</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.syrq\" @change=\"codeChange(1)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syrqOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider content-position=\"left\">使用方法</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-checkbox-group v-model=\"form.pflx\"  @change=\"codeChange(2)\">\r\n                    <el-checkbox\r\n                      v-for=\"dict in syffOptions\"\r\n                      :key=\"dict.id\"\r\n                      :label=\"dict.id\">\r\n                      {{dict.id}}.{{ dict.title }}\r\n                    </el-checkbox>\r\n                  </el-checkbox-group>\r\n                </el-col>\r\n              </el-row>\r\n              <br />\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"制造量\" prop=\"weight\">\r\n                    <el-input v-model=\"form.weight\" placeholder=\"请输入制造量\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"稳定性结果\" prop=\"stabilityresult\">\r\n                    <el-input autosize type=\"textarea\" v-model=\"form.stabilityresult\" placeholder=\"请输入稳定性结果\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"功效概述\" prop=\"gxgs\">\r\n                    <el-input :autosize=\"{ minRows: 3, maxRows: 20}\" type=\"textarea\" v-model=\"form.gxgs\" placeholder=\"请输入功效概述\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-divider  content-position=\"left\">稳定性测试记录</el-divider>\r\n              <el-table :data=\"stabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\"  :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-divider content-position=\"left\">关联稳定性测试记录</el-divider>\r\n              <el-table :data=\"relationStabilityDataList\">\r\n                <el-table-column align=\"center\" label=\"稳定性编码\" prop=\"stabilityCode\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"实验室编码\" prop=\"labNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"稳定性状态\" prop=\"stabilityStatus\" :formatter=\"stabilityStatusFormat\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结论\" prop=\"conclusion\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"样品来源\" :formatter=\"ypFormat\" prop=\"ypFrom\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"Batch No\" prop=\"batchNo\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"配置时间\" prop=\"ypTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"开始时间\" prop=\"startTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"结束时间\" prop=\"endTime\"></el-table-column>\r\n                <el-table-column align=\"center\" label=\"创建时间\" prop=\"createdTime\"></el-table-column>\r\n              </el-table>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注\" prop=\"formulaRemark\">\r\n                    <el-input  autosize v-model=\"form.formulaRemark\" type=\"textarea\" placeholder=\"请输入内容\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </fieldset>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaMaterial'\">\r\n            <el-row v-if=\"!form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"是否复制配方\">\r\n                  <el-radio-group v-model=\"isCopy\">\r\n                    <el-radio :label=\"0\">否</el-radio>\r\n                    <el-radio :label=\"1\">是</el-radio>\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===0\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:300px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择配方\">\r\n                  <el-input v-model=\"form.formulaCodeParams\"  @keyup.enter.native=\"queryFormulaCode\" style=\"width:300px\"  placeholder=\"如果需要添加配方,请输入配方编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryFormulaCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"form.id\">\r\n              <el-col :span=\"12\">\r\n                <el-button v-if=\"isGenFormula===1\" type=\"primary\" @click=\"genPformulaInfo\">生成P配方</el-button>\r\n              </el-col>\r\n              <el-col :span=\"12\" v-if=\"form.pFormulaCount>0\">\r\n                <el-button v-if=\"isBMformula===1\" type=\"primary\" @click=\"genNewformulaInfo\">生成含B代码配方</el-button>\r\n                <div v-if=\"form.formulaCodeBuff\">已生成含B代码配方:<span v-html=\"form.formulaCodeBuff\"></span></div>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"isCopy===1\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"请输入需要复制的实验室编码\">\r\n                  <el-input v-model=\"form.formulaCodeParams\" @focus=\"toChoose\" style=\"width:350px\"  placeholder=\"请选择要复制的实验室编码\" >\r\n                    <el-button\r\n                      slot=\"append\"\r\n                      class=\"el-icon-zoom-in\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"toChoose\" />\r\n                  </el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"选择原料\">\r\n                  <el-input v-model=\"form.materialCode\"  @keyup.enter.native=\"queryMaterialCode\" style=\"width:350px\"  placeholder=\"如果需要添加原料,请输入原料编码\"  />\r\n                  &nbsp;&nbsp;<el-button type=\"primary\" @click=\"queryMaterialCode\">查找</el-button>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-table :data=\"formulaMaterialDatas\" :row-style=\"formulaMaterialBack\" show-summary :summary-method=\"getSummaries\" @selection-change=\"handleFormulaMaterialSelectionChange\">\r\n                  <el-table-column v-if=\"form.id\" align=\"center\" type=\"selection\" width=\"50\" :selectable=\"selectable\"></el-table-column>\r\n                  <el-table-column align=\"center\" width=\"60\">\r\n                    <template slot-scope=\"scope\">\r\n                      <i v-if=\"(form.isLock===1) && isLook\" class=\"el-icon-remove-outline\" @click=\"delFormulaMaterial(scope.row)\" ></i>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料代码\" align=\"center\" prop=\"materialCode\" width=\"80\">\r\n                    <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.type==0\" @click=\"materialDetails(scope.row)\" style=\"color: #00afff;cursor: pointer\">{{scope.row.materialCode}}</span>\r\n                       <span v-else >{{scope.row.materialCode}}</span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"推荐原料\" align=\"center\" prop=\"relationCode\" width=\"140\"  />\r\n                  <el-table-column label=\"商品名称\" v-if=\"isShowMaterialGoodsName===1\" align=\"center\" prop=\"materialGoodsName\" width=\"280\"  />\r\n                  <el-table-column label=\"比例(%)\" width=\"140\" align=\"center\" prop=\"percentage\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"number\" v-model=\"scope.row.percentage\" @input=\"limitDecimal(scope.row)\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"分相\" width=\"100\" align=\"center\"prop=\"subItem\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input v-model=\"scope.row.subItem\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"原料用途\" width=\"120\" align=\"center\" prop=\"designatedUse\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select @change=\"designateChange(scope.row)\" v-model=\"scope.row.designatedUse\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in useOptions\"\r\n                          :key=\"item.value\"\r\n                          :label=\"item.value\"\r\n                          :value=\"item.value\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"指定代码\" width=\"150\" align=\"center\" prop=\"appointCode\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-select clearable v-model=\"scope.row.appointCode\" placeholder=\"请选择\">\r\n                        <el-option\r\n                          v-for=\"item in scope.row.materialCodes\"\r\n                          :key=\"item.materialSubCode\"\r\n                          :label=\"item.materialSubCode\"\r\n                          :value=\"item.materialSubCode\">\r\n                        </el-option>\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"使用目的\" width=\"200\" align=\"center\" prop=\"symdInfo\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input style=\"width: 190px\" v-model=\"scope.row.symdInfo\"/>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"周期(天)\" align=\"center\" prop=\"orderingCycle\" />\r\n                  <el-table-column label=\"原料认证\" align=\"center\" prop=\"certification\">\r\n                      <template slot-scope=\"scopoe\">\r\n                        {{selectDictLabel(certificationOptions,scopoe.row.certification)}}\r\n                      </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"进口国家\" align=\"center\" prop=\"importCountry\" />\r\n                  <el-table-column label=\"备注\" align=\"center\" width=\"300\" prop=\"remark\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isRelation==1\"><el-input v-model=\"scope.row.remark\"/></span>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-col>\r\n            </el-row>\r\n            <br />\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"配方图片\">\r\n                  <imageUpload v-model=\"form.formulaImage\" :limit=\"3\"></imageUpload>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"配方搭建思路\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.formulaConstructionIdeas\" placeholder=\"请输入配方搭建思路\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"备注\" prop=\"remark\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                 <div style=\"text-align: center\">\r\n                   <el-button type=\"primary\" @click=\"submitUploadForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                 </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaFile'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"检测标准\" prop=\"introFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.introFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试结果\" prop=\"wendingxingResult\">\r\n                  <el-select v-model=\"form.wendingxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"稳定性测试备注\" prop=\"wendingxingRemark\">\r\n                   <el-input type=\"textarea\" v-model=\"form.wendingxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"稳定性检测报告\" prop=\"wendingxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.wendingxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"工艺\" prop=\"gongyiFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.gongyiFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性结果\" prop=\"xiangrongxingResult\">\r\n                  <el-select v-model=\"form.xiangrongxingResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in wdxOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"相容性备注\" prop=\"xiangrongxingRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.xiangrongxingRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"相容性测试报告\" prop=\"xiangrongxingFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiangrongxingFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战结果\" prop=\"weishenwuResult\">\r\n                  <div slot=\"label\">\r\n                    <el-tooltip>\r\n                      <i class=\"el-icon-question\" ></i>\r\n                      <div slot=\"content\">\r\n                        <p>高风险(没有测试过相关防腐体系)</p>\r\n                        <p>中风险(测试进行中,有一定数据量)</p>\r\n                        <p>低风险(有相似配方的测试数据,且测试通过)</p>\r\n                        <p>无风险(测试通过)</p>\r\n                        <p>测试没通过(不能释放)</p>\r\n                      </div>\r\n                    </el-tooltip>\r\n                    防腐挑战结果\r\n                  </div>\r\n                  <el-select v-model=\"form.weishenwuResult\" placeholder=\"请选择\" clearable size=\"small\">\r\n                    <el-option v-for=\"item in ffjtxfxpgOptions\" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"防腐挑战备注\" prop=\"weishenwuRemark\">\r\n                  <el-input type=\"textarea\" v-model=\"form.weishenwuRemark\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"防腐实验报告\" prop=\"weishenwuFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.weishenwuFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"消费者测试报告\" prop=\"xiaofeizheFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.xiaofeizheFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"其它\" prop=\"qitaFile\">\r\n                  <SoftwareFileUpload :op-type=\"1\" v-model=\"form.qitaFile\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-row v-if=\"!((form.isLock===1 || form.isLock===2) && isLook)\">\r\n              <el-col :span=\"24\">\r\n                <div style=\"text-align: center\">\r\n                  <el-button type=\"primary\" @click=\"submitUploadFileForm\" :loading=\"btnLoading\" >确定修改</el-button>\r\n                </div>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='recipeChangeHistory'\">\r\n            <el-table :data=\"recipeChangeHistoryData\">\r\n              <el-table-column align=\"center\" prop=\"modifiedTime\" label=\"更改日期\" />\r\n              <el-table-column align=\"center\" prop=\"materialCode\" label=\"原料代码\" />\r\n              <el-table-column align=\"center\" prop=\"percentageOld\" label=\"原始比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"percentageNew\" label=\"新比例(%)\" />\r\n              <el-table-column align=\"center\" prop=\"remark\" label=\"更改内容\" />\r\n              <el-table-column align=\"center\" prop=\"inciName\" label=\"INCI中文名\" />\r\n              <el-table-column align=\"center\" prop=\"operator\" label=\"编辑人\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='spec'\">\r\n            <el-divider content-position=\"left\">检测标准</el-divider>\r\n            <table class=\"base-table\"   style=\"margin-top: 0 !important;\">\r\n              <tr>\r\n                <td style=\"width:70px\" rowspan=\"5\">执行标准</td>\r\n              </tr>\r\n              <tr>\r\n                <td>标准名称</td>\r\n                <td>\r\n                  <el-select clearable filterable v-model=\"form.execNumberId\" @change=\"zxbzChange\">\r\n                    <el-option\r\n                      v-for=\"item in zxbzList\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.zxbzh+'('+item.bzmc+')'\"\r\n                      :value=\"item.id\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </td>\r\n                <td>执行标准号</td>\r\n                <td id=\"zxbzhTd\">{{zxbzDetail.zxbzh}}({{zxbzDetail.bzmc}})</td>\r\n                <td>定义</td>\r\n                <td id=\"dingyiTd\">{{zxbzDetail.dingyi}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>外观及pH</td>\r\n                <td  id=\"waiguanjiphTd\">{{zxbzDetail.waiguan}}{{zxbzDetail.ph}}</td>\r\n                <td >耐热</td>\r\n                <td  id=\"naireTd\">{{zxbzDetail.naire}}</td>\r\n                <td >耐寒</td>\r\n                <td  id=\"naihanTd\">{{zxbzDetail.naihan}}</td>\r\n              </tr>\r\n              <tr>\r\n                <td>归属公司</td>\r\n                <td  id=\"gsgsTd\">\r\n                  {{selectDictLabel(ownershopCompanyOptions,zxbzDetail.ownershopCompany)}}\r\n                </td>\r\n                <td >状态</td>\r\n                <td  id=\"zhtTd\">\r\n                  {{selectDictLabel(statusOptions,zxbzDetail.status)}}\r\n                </td>\r\n                <td >标准性质</td>\r\n                <td  id=\"bzxzTd\">\r\n                  {{selectDictLabel(bzxzOptions,zxbzDetail.bzxz)}}\r\n                </td>\r\n              </tr>\r\n              <!--                <tr>-->\r\n              <!--                  <td colspan=\"7\">-->\r\n              <!--                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>-->\r\n              <!--                  </td>-->\r\n              <!--                </tr>-->\r\n            </table>\r\n            <el-divider content-position=\"left\">检测项目</el-divider>\r\n            <el-tabs v-model=\"currentTab\" >\r\n              <el-tab-pane key=\"base\" label=\"常规检测\" name=\"base\" >\r\n                <div class=\"cell-wrapper\" >\r\n                  <div class=\"label\">选择模板:</div>\r\n                  <div class=\"content\">\r\n                    <el-select @change=\"changeFun\" v-model=\"form.currentTemplateId\" filterable size=\"mini\" >\r\n                      <el-option\r\n                        v-for=\"item in templateList\"\r\n                        :key=\"item.id\"\r\n                        :value=\"item.id\"\r\n                        :label=\"item.name\" />\r\n                    </el-select>\r\n                    <el-button icon=\"el-icon-search\" size=\"mini\" @click=\"changeTemplate(1)\" :loading=\"btnLoading\" />\r\n                  </div>\r\n                </div>\r\n                <div class=\"table-wrapper\" style=\"text-align: center;\" v-if=\"itemArray.length\">\r\n                  <table class=\"base-table small-table\">\r\n                    <tr>\r\n                      <th style=\"width: 50px\">\r\n                        <i @click=\"selectProject\" class=\"el-icon-circle-plus\" />\r\n                      </th>\r\n                      <th style=\"width: 120px\">类型</th>\r\n                      <th style=\"width: 120px\">检测项目</th>\r\n                      <th style=\"width: 320px\">检验标准</th>\r\n                      <th style=\"width: 320px\">检验方法</th>\r\n                      <th style=\"width: 320px\">标准值</th>\r\n                      <th style=\"width: 120px\">检验频次</th>\r\n                    </tr>\r\n                    <tr v-for=\"(item,i) in itemArray\" :key=\"item.id\" >\r\n                      <td><i v-if=\"(form.isLock===1 && isLook)\" class=\"el-icon-remove-outline\" @click=\"delItem(i)\"/></td>\r\n                      <td>{{item.type}}</td>\r\n                      <td>{{item.label}}</td>\r\n                      <td>{{item.standard}}</td>\r\n                      <td>{{item.methodTemplate}}</td>\r\n                      <td>\r\n                        <span v-if=\"isEditStandard(item.id)\"><el-input v-model=\"item.standardVal\" /> </span>\r\n                        <span v-else>\r\n                      <span v-if=\"(form.isLock===1 && isLook)\"><el-input v-model=\"item.standardVal\" /></span>\r\n                      <span v-else>{{item.standardVal}}</span>\r\n                    </span>\r\n                      </td>\r\n                      <td>{{item.frequency}}</td>\r\n                    </tr>\r\n                  </table>\r\n                  <br /><br />\r\n                  <div style=\"text-align: center;\">\r\n                    <el-button @loading=\"btnLoading\" @click=\"submitSpec\" type=\"primary\">提 交</el-button>\r\n                  </div>\r\n                </div>\r\n              </el-tab-pane>\r\n              <el-tab-pane key=\"special\" label=\"微生物检测\" name=\"microbe\" >\r\n                <el-row>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"样品物性\" prop=\"wxId\">\r\n                      <div style=\"cursor: pointer\" @click=\"showWx\" >\r\n                        <span v-if=\"form.wxId\" >{{wxLabel(form.wxId)}}</span>\r\n                        <i v-else style=\"color: #00afff;\">请选择</i>\r\n                      </div>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\" >\r\n                    <el-form-item label=\"检验依据\" prop=\"inspectBasis\">\r\n                      <el-input v-model=\"form.inspectBasis\" />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-tab-pane>\r\n\r\n              <el-form-item label=\"备注\" style=\"margin-top: 20px\" prop=\"microbeRemark\">\r\n                <el-input v-model=\"form.microbeRemark\" type=\"textarea\" autosize />\r\n              </el-form-item>\r\n\r\n\r\n            </el-tabs>\r\n\r\n            <br /><br />\r\n            <el-divider content-position=\"left\">检测记录</el-divider>\r\n            <el-row :gutter=\"10\" class=\"mb8\">\r\n              <el-col :span=\"1.5\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  plain\r\n                  icon=\"el-icon-plus\"\r\n                  size=\"mini\"\r\n                  v-if=\"form.isLock===1 && isLook\"\r\n                  @click=\"handleFormulaSpecAdd\"\r\n                >新增</el-button>\r\n              </el-col>\r\n            </el-row>\r\n            <el-table v-loading=\"loading\" :data=\"softwareFormulaSpecList\" style=\"overflow: scroll\">\r\n              <el-table-column label=\"样品来源\" align=\"center\" prop=\"type\">\r\n                <template slot-scope=\"scope\">\r\n                  {{selectDictLabel(yplyOptions,scope.row.type)}}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"测试样批号\" width=\"130\" align=\"center\" prop=\"ceshiyangpihao\" />\r\n              <el-table-column label=\"外观\" align=\"center\" prop=\"waiguan\" />\r\n              <el-table-column label=\"颜色\" align=\"center\" prop=\"yanse\" />\r\n              <el-table-column label=\"气味\" align=\"center\" prop=\"qiwei\" />\r\n              <el-table-column label=\"PH\" align=\"center\" prop=\"ph\" />\r\n              <el-table-column label=\"耐热\" align=\"center\" prop=\"naire\" />\r\n              <el-table-column label=\"耐寒\" align=\"center\" prop=\"naihan\" />\r\n              <el-table-column label=\"创建时间\" align=\"center\" prop=\"createdTime\" width=\"180\" />\r\n              <el-table-column label=\"操作人\" align=\"center\" prop=\"operator\" />\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template v-slot=\"scope\">\r\n                  <el-tooltip content=\"修改\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      icon=\"el-icon-edit\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      @click=\"handleFormulaSpecEdit(scope.row)\"\r\n                    />\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='workmanship'\">\r\n            <div style=\"width: 100%\">\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>工艺简述</span>\r\n                  <el-tooltip content=\"更新工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"refreshFormulaLegalGy('1')\"\r\n                    />\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(0)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n              <div style=\"width: 50%;float: left\">\r\n                <div style=\"text-align: center;\">\r\n                  <span>备案工艺</span>\r\n                  <el-tooltip content=\"复制工艺简述\" placement=\"top\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      v-if=\"form.isLock===1 && isLook\"\r\n                      icon=\"el-icon-refresh\"\r\n                      :loading=\"btnLoading\"\r\n                      @click=\"copyGongyi()\"\r\n                    >\r\n                      复制工艺简述\r\n                    </el-button>\r\n                  </el-tooltip>\r\n                </div>\r\n                <el-divider content-position=\"left\">工艺简述</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in gyjsBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <el-divider content-position=\"left\">组分原料备注</el-divider>\r\n                <table class=\"base-table\">\r\n                  <tr v-for=\"data in zfylBeianDataList\">\r\n                    <el-input v-model=\"data.name\" type=\"textarea\"/>\r\n                  </tr>\r\n                </table>\r\n                <br /><br />\r\n                <div style=\"text-align:center\">\r\n                  <el-button v-if=\"(form.isLock===1 || form.isLock===2) && isLook\" @loading=\"btnLoading\" @click=\"addFormulaGyjsBeianInfo(1)\" type=\"primary\">保存更改</el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"formula.code==='compositionTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\" :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"INCI 中文名\" prop=\"chiName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                    <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">\r\n                       <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                          <div slot=\"content\">\r\n                            <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                          </div>\r\n                          <i class=\"el-icon-question\" ></i>\r\n                        </el-tooltip>\r\n                       <span v-html=\"scope.row.chiNameNew\"></span>\r\n                    </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" prop=\"engName\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\">{{scope.row.engName}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\" prop=\"percert\" align=\"center\"  width=\"150\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{totalPercent}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?'color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"国家简化版安评要求\" align=\"center\">\r\n                <el-table-column label=\"驻留类产品最高历史使用量\" prop=\"zllzglssyl\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"淋洗类产品最高历史使用量\" prop=\"lxlzglssyl\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"法规要求\" align=\"center\">\r\n                <el-table-column label=\"备案(明细)\" prop=\"bzmx\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"化妆品使用时的最大允许浓度\" prop=\"maxAllowConcentration\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"180\" />\r\n                <el-table-column label=\"适用及(或)使用范围\" prop=\"scopeOfApplication\" align=\"center\"  width=\"220\"  />\r\n                <el-table-column label=\"其他限制和要求\" prop=\"otherLimit\" align=\"center\"  width=\"220\" />\r\n                <el-table-column label=\"标签上必须标印的 使用条件和注意事项\" prop=\"labelCondition\" align=\"center\"  width=\"180\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n            </el-table>\r\n            <br />\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以上：</td>\r\n                <td align=\"left\">\r\n                   <span v-html=\"gtNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">全成分标识 0.1%（w/w）以下：</td>\r\n                <td align=\"left\">\r\n                  <span v-html=\"ltNumStr\"></span>\r\n                </td>\r\n              </tr>\r\n            </table>\r\n          </template>\r\n          <template v-if=\"formula.code==='productSafetyAssessmentReport'\">\r\n            <el-row>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"产品评估结论\" prop=\"aqpgjl\">\r\n                  <el-input type=\"textarea\" autosize v-model=\"form.aqpgjl\" />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n            <br />\r\n            <el-table height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n              <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                         <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI名称/英文民称\" prop=\"engName\" align=\"center\" fixed  width=\"200\" />\r\n              <el-table-column label=\"含量(%)\" prop=\"percert\" align=\"center\"  width=\"120\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isGt==1?';color:red':''\">{{scope.row.percert}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                <template slot-scope=\"scope\">\r\n                  <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"在《已使用原料目录》中的序号\" prop=\"cppfSyylxh\" align=\"center\"  width=\"220\" />\r\n              <el-table-column label=\"产品配方表备注\" prop=\"cppfRemark\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"《化妆品安全技术规范》要求\" prop=\"gcfJsgf\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"权威机构评估结论\" prop=\"gcfQwjgpgjl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"本企业原料历史使用量(%)\" prop=\"gcfBqyysyl\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"最高历史使用量(%)\" prop=\"gcfZglssy\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"评估结论\" prop=\"gcfPgjl\" align=\"center\"  width=\"200\">\r\n                <template slot-scope=\"scope\">\r\n                  <span :style=\"scope.row.isColor==1?'color:red':''\">{{scope.row.gcfPgjl}}</span>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"参考文献序号\" prop=\"gcfCkwx\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献内容\" prop=\"gcfCkwxnr\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"参考文献下载链接\" prop=\"gcfCkwxlj\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"可能含有的风险物质\" prop=\"aqxKnhyfxw\" align=\"center\"  width=\"200\" />\r\n              <el-table-column label=\"风险物质备注\" prop=\"aqxRemark\" align=\"center\"  width=\"200\" />\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessment'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                   <span v-if=\"form.isCiji>0\" style=\"color:red;\">配方需测刺激性</span>\r\n                   <span v-if=\"form.isZhimin>0\" style=\"color:red;\"><span v-if=\"form.isCiji>0\">、</span><span v-else>配方需测</span>致敏性</span>\r\n                   <span v-if=\"form.zmCjTips\" style=\"color:red;\">。({{form.zmCjTips}})</span>\r\n                </el-row>\r\n                <div v-if=\"form.isLock===1  && isLook && compositionTableDataList.length>0\" style=\"text-align:center;margin-top:10px\">\r\n                  <el-button v-hasPermi=\"['software:softwareDevelopingFormula:editSymd']\" @loading=\"btnLoading\" @click=\"submitSymdInfo\" type=\"primary\">更新使用目的</el-button>\r\n                </div>\r\n                <br />\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"   :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"使用目的\" prop=\"cppfSymd\" align=\"center\"  width=\"220\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                      <el-input type=\"textarea\"  :autosize=\"{ minRows: 3, maxRows: 6}\" v-model=\"scope.row.cppfSymd\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"dataObj.zl\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"dataObj.newTotal\" align=\"center\" />\r\n                    <el-table-column label=\"适用及(或)使用范围\" :show-overflow-tooltip=\"true\"  width=\"220\" prop=\"dataObj.newRange\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"化妆品安全卫生规范\" align=\"center\">\r\n                    <el-table-column label=\"符合卫生规范\" width=\"120\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.dataObj.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.dataObj.bzmx  === '否'\"></span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"备案明细\" width=\"220\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                       <span v-if=\"scope.row.dataObj.bzmx  === '是'\">\r\n                         {{scope.row.dataObj.bzmxDetail}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"dataObj.syjsyfw\"  width=\"260\" :show-overflow-tooltip=\"true\"  label=\"适用及(或)使用范围\" align=\"center\" />\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\"  prop=\"dataObj.zdsynd\" align=\"center\" />\r\n                    <el-table-column prop=\"otherLimit\"  width=\"260\"  :show-overflow-tooltip=\"true\" label=\"其他限制和要求\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" width=\"140\" prop=\"zlxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" width=\"140\" prop=\"lxxData\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"药食同源\" align=\"center\">\r\n                    <el-table-column label=\"类型\" prop=\"ystyType\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{selectDictLabel(typeOptions,scope.row.dataObj.ystyType)}}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"使用限制\" prop=\"\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{scope.row.dataObj.ystyMax}}\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"CIR安全级别\" prop=\"finding\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      {{scope.row.dataObj.finding}}\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"已上市产品原料使用信息2025/《国际化妆品安全评估数据索引》收录的部分原料使用信息\" width=\"800\" align=\"center\">\r\n                    <el-table-column align=\"center\" label=\"驻留\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='驻留').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='驻留')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"淋洗\" width=\"400\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-divider v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">已上市产品原料使用信息</el-divider>\r\n                        <table v-if=\"scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                        <el-divider v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" content-position=\"left\">《国际化妆品安全评估数据索引》</el-divider>\r\n                        <table v-if=\"scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0\" class=\"base-table\">\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.parts}}\r\n                            </td>\r\n                          </tr>\r\n                          <tr>\r\n                            <td v-for=\"(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')\">\r\n                              {{zjy.usage}}\r\n                            </td>\r\n                          </tr>\r\n                        </table>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"total(中检院)\" prop=\"maxTotals\" />\r\n                    <el-table-column align=\"center\" label=\"total(国际化妆品)\" prop=\"aqpgMaxTotals\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"权威评估结论\" :show-overflow-tooltip=\"true\"  width=\"400\" prop=\"dataObj.conclusion\" align=\"center\" />\r\n                  <el-table-column label=\"评估结论\"  width=\"400\" prop=\"finalConclusion\" align=\"center\" />\r\n<!--                  <el-table-column label=\"已使用化妆品原料目录(2021年版)\"   align=\"center\">-->\r\n<!--                    <el-table-column label=\"驻留类\" prop=\"dataObj.zllzglssyl\" align=\"center\" />-->\r\n<!--                    <el-table-column label=\"淋洗类\" prop=\"dataObj.lxlzglssyl\" align=\"center\" />-->\r\n<!--                  </el-table-column>-->\r\n                  <el-table-column label=\"EWG\" align=\"center\">\r\n                    <el-table-column label=\"EWG分值\" prop=\"ewgScore\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                        <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致癌性\" prop=\"dataObj.cancer\" align=\"center\" />\r\n                    <el-table-column label=\"过敏/免疫毒性\" width=\"160\" prop=\"dataObj.allergies\" align=\"center\" />\r\n                    <el-table-column label=\"发育/生殖毒性\" width=\"160\" prop=\"dataObj.developmental\" align=\"center\" />\r\n                    <el-table-column label=\"使用限制\" prop=\"dataObj.useRestrictions\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n                  <el-table-column label=\"是否新原料\" align=\"center\">\r\n                    <el-table-column label=\"是否新原料\" width=\"120\" prop=\"isNewMaterial\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.isNewMaterial  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"驻留类\" prop=\"cirzlx\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"cirlxx\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"安全技术规范\" align=\"center\">\r\n                    <el-table-column label=\"规范\" prop=\"bzmx\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <i v-if=\"scope.row.bzmx  === '是'\" class=\"ali-icon ali-weixuanzhongyuanquan\" style=\"font-size: 20px\"></i>\r\n                        <span v-else-if=\"scope.row.bzmx  === '否'\"></span>\r\n                        <span v-else>\r\n                        {{scope.row.bzmx}}\r\n                      </span>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"最大允许浓度\" width=\"120\" prop=\"zdsynd\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"权威机构\" align=\"center\">\r\n                    <el-table-column label=\"CIR驻留类\" prop=\"zlxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR淋洗类\" prop=\"lxxData_\" align=\"center\" />\r\n                    <el-table-column label=\"CIR total\" prop=\"totalsData_\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"毒理(欧标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliOuBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliOuBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliOuBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"毒理(日标)\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"duliRiBiaoLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"duliRiBiaoRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"duliRiBiaoTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n                  <el-table-column label=\"IFRA\" prop=\"isIfra\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span v-if=\"scope.row.isEssence===1 && scope.row.isIfra===0\">+</span>\r\n                    </template>\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"公司内部\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"companyLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"companyRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"companyTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"供应商\" align=\"center\">\r\n                    <el-table-column label=\"驻留类\" prop=\"supplierLeaveOn\" align=\"center\" />\r\n                    <el-table-column label=\"淋洗类\" prop=\"supplierRinseOff\" align=\"center\" />\r\n                    <el-table-column label=\"total\" prop=\"supplierTotals\" align=\"center\" />\r\n                  </el-table-column>\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='conclusionOfSafetyAssessmentSimple'\">\r\n            <el-collapse v-model=\"activeNames\">\r\n              <el-collapse-item title=\"判断结果定义\" name=\"1\">\r\n                <div class=\"tip\">\r\n                  <span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>\r\n                  <span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>\r\n                  <span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" />:不满足以上两个条件;含有安全级别为I/Z<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" />:含有禁用成分;含有安全级别为U的成分<br/>\r\n                  <span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" />:含有安全级别为UNS的成分<br/>\r\n                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>\r\n                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>\r\n                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>\r\n                  <span>I:可用数据不足以支持安全性<br/></span>\r\n                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>\r\n                  <span>U:该成分用于化妆品不安全<br/></span>\r\n                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>\r\n                  <span>无:无权威机构数据</span>\r\n                </div>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n            <el-tabs v-model=\"conclusionOfSafetyAssessmentName\">\r\n              <el-tab-pane label=\"成分纬度\" name=\"first\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item label=\"EWG\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.ewgColor\">\r\n                        <el-radio label=\"green\">绿色</el-radio>\r\n                        <el-radio label=\"orange\">橙色</el-radio>\r\n                        <el-radio label=\"red\">红色</el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleCompositionQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetCompositionQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table  height=\"65vh\" border :data=\"compositionTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed />\r\n                  <el-table-column label=\"中文名称\" prop=\"chiName\" align=\"center\"  width=\"200\" fixed>\r\n                    <template slot-scope=\"scope\">\r\n                        <span :style=\"scope.row.isTips==1?'background-color:#9966FF':''\">\r\n                           <el-tooltip v-if=\"scope.row.isTips===1\">\r\n                            <div slot=\"content\">\r\n                              <img src=\"https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png\" >\r\n                            </div>\r\n                            <i class=\"el-icon-question\" ></i>\r\n                          </el-tooltip>\r\n                          <span v-html=\"scope.row.chiNameNew\"></span>\r\n                        </span>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"成分含量\" prop=\"percert\" align=\"center\"  width=\"100\" fixed />\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"EWG成分安全分\" prop=\"ewgScore\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <div v-if=\"scope.row.dataObj.isSplit==0\" style=\"height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                      <div v-else-if=\"scope.row.dataObj.isSplit==1\" style=\"height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;\" :style=\"{backgroundColor:scope.row.dataObj.ewgColor}\">{{scope.row.dataObj.ewgScore}}</div>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"美修参考\"  align=\"center\">\r\n                    <el-table-column label=\"活性成分\" prop=\"activity\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.activity\" :src=\"require('@/assets/images/formula/huoxing.png')\" width=\"40\" height=\"50\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"致痘风险\" prop=\"pox\" align=\"center\">\r\n                      <template slot-scope=\"scope\">\r\n                        <img v-if=\"scope.row.dataObj.pox==1\" :src=\"require('@/assets/images/formula/di.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==2\" :src=\"require('@/assets/images/formula/zhong.png')\" width=\"40\" height=\"40\" >\r\n                        <img v-else-if=\"scope.row.dataObj.pox==3\" :src=\"require('@/assets/images/formula/gao.png')\" width=\"40\" height=\"40\" >\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"孕妇慎用\" prop=\"dataObj.yfSy\" align=\"center\" />\r\n                    <el-table-column label=\"安全风险\" prop=\"dataObj.risk\" align=\"center\" />\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n              <el-tab-pane label=\"原料纬度\" name=\"second\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-form-item label=\"判断结果\" label-width=\"120\" prop=\"materialCode\">\r\n                      <el-radio-group v-model=\"queryParams.comclusionType\">\r\n                        <el-radio :label=\"1\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"4\"><span style=\"color: green;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"2\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-success\" /></el-radio>\r\n                        <el-radio :label=\"5\"><span style=\"color: blue;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"3\"><span style=\"color: orange;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                        <el-radio :label=\"6\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-error\" /></el-radio>\r\n                        <el-radio :label=\"7\"><span style=\"color: red;font-size: 22px\" class=\"el-icon-question\" /></el-radio>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-form-item>\r\n                      <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleMaterialQuery\">搜索</el-button>\r\n                      <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetMaterialQuery\">重置</el-button>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-table   height=\"65vh\" border :data=\"formulaTableDataList\"  :cell-style=\"compositionCellTableStyle\"  :row-style=\"compositionTableStyle\">\r\n                  <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" fixed   />\r\n                  <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"80\" fixed />\r\n                  <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\" fixed />\r\n\r\n                  <el-table-column label=\"判断结果\" prop=\"componentType\" align=\"center\">\r\n                    <template slot-scope=\"scope\">\r\n                      <span style=\"color: green;font-size: 22px\" v-if=\"scope.row.componentType===1\" class=\"el-icon-success\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===2\" class=\"el-icon-success\" />\r\n                      <span style=\"color: orange;font-size: 22px\" v-else-if=\"scope.row.componentType===3\" class=\"el-icon-question\" />\r\n                      <span style=\"color: green;font-size: 22px\" v-else-if=\"scope.row.componentType===4\" class=\"el-icon-error\" />\r\n                      <span style=\"color: blue;font-size: 22px\" v-else-if=\"scope.row.componentType===5\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===6\" class=\"el-icon-error\" />\r\n                      <span style=\"color: red;font-size: 22px\" v-else-if=\"scope.row.componentType===7\" class=\"el-icon-question\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-tab-pane>\r\n            </el-tabs>\r\n          </template>\r\n          <template v-if=\"formula.code==='pFomula'\">\r\n            <el-row v-for=\"(item, index) in pFormulaMapData\">\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"配方编码\">\r\n                  {{item.formulaCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-form-item label=\"实验室编码\">\r\n                  {{item.laboratoryCode}}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"8\">\r\n                <el-button  v-hasPermi=\"['software:softwareDevelopingFormula:genBMaterialInfo']\" type=\"primary\" @click=\"generBMaterialInfo(item.id)\">生成B代码</el-button>\r\n                <span v-if=\"item.materialCode\">该配方已生成了B代码,代码为:{{item.materialCode }}</span>\r\n              </el-col>\r\n              <el-table :data=\"item.materialDatas\" show-summary :summary-method=\"getSummariesPFormula\">\r\n                <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n                <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"200\" />\r\n                <el-table-column v-hasPermi=\"['software:softwareDevelopingFormula:lookMaterialGoodsName']\" label=\"商品名称\" prop=\"materialChiName\" align=\"center\"  width=\"400\" />\r\n                <el-table-column label=\"比例\" prop=\"percentage\" align=\"center\"  width=\"200\" />\r\n              </el-table>\r\n              <br /><br />\r\n            </el-row>\r\n          </template>\r\n          <template v-if=\"formula.code==='formulaTable'\">\r\n            <table class=\"base-table\">\r\n              <tr>\r\n                <td align=\"right\">客户名称:</td>\r\n                <td style=\"width: 200px\">{{form.customerName }}</td>\r\n                <td align=\"right\">品牌名称:</td>\r\n                <td style=\"width: 200px\">{{form.brandName }}</td>\r\n                <td align=\"right\">产品名称:</td>\r\n                <td style=\"width: 200px\">{{form.productName }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td align=\"right\">实验室编码:</td>\r\n                <td style=\"width: 300px\">{{form.laboratoryCode }}</td>\r\n                <td align=\"right\">配方编码:</td>\r\n                <td style=\"width: 200px\">{{form.formulaCode }}</td>\r\n                <td align=\"right\">执行标准号:</td>\r\n                <td style=\"width: 200px\">{{form.execNumber }}</td>\r\n              </tr>\r\n              <tr>\r\n                <td colspan=\"6\" style=\"text-align: left\">Material contained in 100 grams</td>\r\n              </tr>\r\n            </table>\r\n            <br />\r\n            <el-table border :data=\"formulaTableDataList\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"50\" />\r\n              <el-table-column label=\"分相\" prop=\"subItem\" align=\"center\"  width=\"60\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\" width=\"80\" />\r\n              <el-table-column label=\"INCI 中文名\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciName}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"INCI NAME\" width=\"350px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div :style=\"scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''\" v-for=\"(item,index) in scope.row.inicDataList\">{{item.inciNameEng}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"比例\" width=\"120px\" prop=\"percentage\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  比例({{totalPercentVal}}%)\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"复配百分比(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量(%)\"  width=\"150px\" prop=\"subItem\" align=\"center\">\r\n                <template slot=\"header\">\r\n                  实际成分含量({{sjTotalPercet}}%)\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.proportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"最低纯度(%)\"  width=\"140px\" prop=\"subItem\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div  v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportionSingle}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"实际成分含量\"  width=\"140px\" prop=\"sjProportion\" align=\"center\">\r\n                <template slot=\"header\" slot-scope=\"scope\">\r\n                  <el-tooltip content=\"按原料最低比例计算\" >\r\n                    <i class=\"el-icon-question\" ></i>\r\n                  </el-tooltip>\r\n                  实际成分含量\r\n                </template>\r\n                <template slot-scope=\"scope\">\r\n                  <div v-for=\"(item,index) in scope.row.inicDataList\">{{item.sjProportion}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"CIR历史使用量\" align=\"center\">\r\n                <el-table-column label=\"CIR\" align=\"center\" prop=\"cirData\"   width=\"110\" />\r\n                <el-table-column label=\"驻留型\" align=\"center\" prop=\"zlxData\"   width=\"110\" />\r\n                <el-table-column label=\"淋洗型\" align=\"center\" prop=\"lxxData\"   width=\"110\" />\r\n                <el-table-column label=\"婴儿产品/婴儿护理\" align=\"center\" prop=\"babyData\"   width=\"110\" />\r\n                <el-table-column label=\"Totals\" align=\"center\" prop=\"totalsData\"   width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"毒理/供应商使用量参考\"  align=\"center\">\r\n                <el-table-column label=\"欧标\" prop=\"ouBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"日标\" prop=\"riBiao\" align=\"center\"  width=\"110\" />\r\n                <el-table-column label=\"供应商数据\" prop=\"supplieData\" align=\"center\"  width=\"110\" />\r\n              </el-table-column>\r\n              <el-table-column label=\"周期(天)\" prop=\"orderingcycle\" align=\"center\" />\r\n              <el-table-column label=\"备注\"  width=\"220px\" prop=\"inicRemark\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <span v-html=\"scope.row.inicRemark\"></span>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </template>\r\n          <template v-if=\"formula.code==='specMaterial'\">\r\n            <el-table :data=\"specMaterialDatas\">\r\n              <el-table-column label=\"序号\" type=\"index\" align=\"center\"  width=\"100\" />\r\n              <el-table-column label=\"原料代码\" prop=\"materialCode\" align=\"center\"  width=\"150\" />\r\n              <el-table-column label=\"原始INCI 中文名\" prop=\"inciName\" align=\"center\" />\r\n              <el-table-column label=\"替换为\" prop=\"replaceInciName\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  <div v-if=\"scope.row.inciName==='二氧化钛' || scope.row.inciName==='CI 77891'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas1\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                  <div v-else-if=\"scope.row.inciName==='氧化锌' || scope.row.inciName==='CI 77947'\">\r\n                    <el-select v-model=\"scope.row.replaceInciName\" clearable placeholder=\"请选择\">\r\n                      <el-option\r\n                        v-for=\"item in specMaterialDatas2\"\r\n                        :key=\"item.value\"\r\n                        :label=\"item.value\"\r\n                        :value=\"item.value\">\r\n                      </el-option>\r\n                    </el-select>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <div v-if=\"form.isLock===1  && isLook && specMaterialDatas.length>0\" style=\"text-align:center;margin-top:10px\">\r\n              <el-button @loading=\"btnLoading\" @click=\"submitTipsMaterialFormulaInfo\" type=\"primary\">提 交</el-button>\r\n            </div>\r\n          </template>\r\n        </el-tab-pane>\r\n      </el-tabs>\r\n    </el-form>\r\n\r\n    <div v-if=\"(form.isLock===1 || form.isLock===2) && isLook && activeName!=='workmanship'\" slot=\"footer\" class=\"dialog-footer\" style=\"margin-top:10px\">\r\n      <el-button @loading=\"btnLoading\" v-if=\"form.isDraft===1\" type=\"primary\" @click=\"submitForm(1)\">保存草稿</el-button>\r\n      <el-button @loading=\"btnLoading\" type=\"primary\" @click=\"submitForm(0)\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n\r\n    <el-dialog title=\"选择配方\" :visible.sync=\"visible\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n       <selectFormula @selected=\"selected\"/>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"选择项目\" :visible.sync=\"categoryOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <template v-for=\"category in categoryList\" >\r\n        <el-divider content-position=\"left\">\r\n          {{category.category}}\r\n          <el-tooltip content=\"全选/反选\" >\r\n            <i class=\"el-icon-circle-check\" @click=\"selectCategory(category)\" />\r\n          </el-tooltip>\r\n        </el-divider>\r\n\r\n        <el-row :gutter=\"20\" class=\"select-wrapper\" >\r\n          <el-col v-for=\"item in category.array\" :key=\"item.id\" :span=\"4\" style=\"display: flex;align-items: center\" >\r\n            <div class=\"item\" @click=\"selectXm(item.id)\" :class=\"xmIds.includes(item.id) ? 'selected':''\">\r\n              {{item.title}}\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </template>\r\n      <div class=\"dialog-footer\" style=\"margin-top: 10px\" >\r\n        <el-button type=\"primary\" size=\"mini\" @click=\"confirmXm\" >确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"添加SPEC\" :visible.sync=\"specOpen\" width=\"1200px\" top=\"5vh\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-row v-if=\"specId\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n              {{selectDictLabel(yplyOptions,form.type)}}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row v-else>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"样品来源\" prop=\"type\">\r\n               <el-select v-model=\"form.type\" clearable>\r\n                 <el-option\r\n                   v-for=\"dict in yplyOptions\"\r\n                   :key=\"dict.dictValue\"\r\n                   :label=\"dict.dictLabel\"\r\n                   :value=\"dict.dictValue\"\r\n                 ></el-option>\r\n               </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <table class=\"base-table small-table\">\r\n            <tr>\r\n              <th style=\"width: 120px\">类型</th>\r\n              <th style=\"width: 120px\">检测项目</th>\r\n              <th style=\"width: 320px\">检验标准</th>\r\n              <th style=\"width: 320px\">标准值</th>\r\n              <th style=\"width: 120px\">检验频次</th>\r\n            </tr>\r\n            <tr v-for=\"(item,i) in userItemArray\" :key=\"item.id\" >\r\n              <td>{{item.type}}</td>\r\n              <td>{{item.label}}</td>\r\n              <td>{{item.standard}}</td>\r\n              <td><el-input v-model=\"item.standardVal\" /></td>\r\n              <td>{{item.frequency}}</td>\r\n            </tr>\r\n          </table>\r\n        </el-row>\r\n         <div style=\"text-align: center;margin-top:10px\">\r\n          <el-button @loading=\"btnLoading\" @click=\"submitUserSpec\" type=\"primary\">提 交</el-button>\r\n         </div>\r\n       </el-form>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"分享配方\" :visible.sync=\"shareOpen\" width=\"800px\" top=\"5vh\" append-to-body>\r\n      <el-checkbox-group v-model=\"shareDeptIds\">\r\n        < <el-checkbox\r\n          v-for=\"dict in shareDeptDatas\"\r\n        :key=\"dict.id\"\r\n        :label=\"dict.id\">\r\n          {{ dict.name }}\r\n      </el-checkbox>\r\n      </el-checkbox-group>\r\n      <div style=\"margin-top: 10px;text-align: center\">\r\n        <el-button  @loading=\"btnLoading\" @click=\"submitShareFormulaInfo\" type=\"primary\">提 交</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n\r\n    <el-dialog title=\"样品物性\" :visible.sync=\"wxOpen\" width=\"1200px\" :close-on-click-modal=\"false\" append-to-body>\r\n      <el-tree\r\n        :data=\"wxTree\"\r\n        @node-click=\"handleNodeClick\"\r\n        node-key=\"id\"\r\n        default-expand-all\r\n      />\r\n    </el-dialog>\r\n\r\n    <SoftwareMaterialSave ref=\"softwareMaterialSave\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listSoftwareDevelopingFormula,\r\n  getSoftwareDevelopingFormula,\r\n  delSoftwareDevelopingFormula,\r\n  addSoftwareDevelopingFormula,\r\n  updateSoftwareDevelopingFormula,\r\n  exportSoftwareDevelopingFormula,\r\n  querySoftwareDevelopingFormulaDict,\r\n  queryFormulaClassifyData,\r\n  getFormulaCategoryTree,\r\n  queryFormulaMaterialData,\r\n  getFormulaLabNoInfoByCode,\r\n  queryFormualMaterialRecipeChangeHistoryData,\r\n  queryFormulaZxbzDataList,\r\n  queryFormulaZxbzDataDetail,\r\n  getSoftwareDevelopingFormulaDetail,\r\n  addSoftwareDevelopingFormulaSpecZxbz,\r\n  queryCirHistoryData,\r\n  getCirDataTree,\r\n  queryDuliHistoryData,\r\n  getDuliDataTree,\r\n  addFormulaSpecMaterialData,\r\n  addFormulaSymdForm,\r\n  generatePFormulaInfo,\r\n  generateBMaterialInfo,\r\n  generateNewformulaInfo,\r\n  addSoftwareDevelopingUserFormulaSpecZxbz,\r\n  queryMaterialFormulaSpecDataList,\r\n  queryMaterialFormulaSpecDataDetail,\r\n  addFormulaGyjsBeianInfo,\r\n  queryFormulaLegalGy,\r\n  queryFormulaShareDeptDataList,\r\n  queryFormulaShareDeptDataDetail,\r\n  addFormulaShareDataInfo,\r\n  queryLookFormulaTabs,\r\n  updateSoftwareDevelopingFormulaImg,\r\n  queryFormulaStabilityRecordDataList,\r\n  updateSoftwareDevelopingFormulaFileImg, queryFormulaAppointMaterialDataList\r\n} from \"@/api/software/softwareDevelopingFormula\";\r\nimport {formualList, formualProjectDetail} from \"@/api/project/project\";\r\nimport {getFormulaInfoByCode, getRawMaterialInfoByCode} from \"@/api/software/softwareMaterial\";\r\nimport {isArray, isString} from \"@/utils/validate\";\r\nimport selectFormula from \"@/views/software/formula/components/selectFormula\";\r\nimport {allBcpTemplate, getBcpTemplate} from \"@/api/qc/bcpTemplate\";\r\nimport {allJcxm} from \"@/api/qc/jcxm\";\r\nimport {checkPermi} from \"@/utils/permission\";\r\nimport SoftwareMaterialSave from \"@/views/software/softwareMaterial/save\";\r\nimport { Base64 } from 'js-base64'\r\nimport {selectDictLabel} from \"../../../utils/ruoyi\";\r\nimport {allTreeData} from \"@/api/system/treeData\";\r\n\r\nexport default {\r\n  name: \"SoftwareDevelopingFormulaSave\",\r\n  components:{selectFormula,SoftwareMaterialSave},\r\n  props: {\r\n    readonly: {\r\n      type: Boolean,\r\n      default: false,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      wxOpen:false,\r\n      activeName: \"base\",\r\n      currentTab: 'base',\r\n      wxOptions: [],\r\n      conclusionOfSafetyAssessmentName: \"first\",\r\n      loading: false,\r\n      visible: false,\r\n      btnLoading: false,\r\n      categoryOpen: false,\r\n      exportLoading: false,\r\n      fullscreenFlag: false,\r\n      shareOpen: false,\r\n      specOpen: false,\r\n      totalPercentVal:0,\r\n      sjTotalPercet:0,\r\n      isShowMaterialGoodsName:0,\r\n      isGenFormula:0,\r\n      isBMformula:0,\r\n      activeNames: [],\r\n      ids: [],\r\n      statusOptions: [],\r\n      shareDeptDatas: [],\r\n      shareDeptIds: [],\r\n      specId: null,\r\n      ownershopCompanyOptions: [],\r\n      bzxzOptions: [],\r\n      projectList: [],\r\n      typeOptions: [],\r\n      categoryList: [],\r\n      zxbzList: [],\r\n      xmIds: [],\r\n      recipeChangeHistoryData: [],\r\n      formulaTableDataList: [],\r\n      formulaTableDataListBack: [],\r\n      compositionTableDataList: [],\r\n      compositionTableDataListBack: [],\r\n      softwareFormulaSpecList: [],\r\n      specMaterialDatas: [],\r\n      itemArray: [],\r\n      userItemArray: [],\r\n      gtNumStr: '',\r\n      ltNumStr: '',\r\n      itemNames: [],\r\n      zxbzDetail:{},\r\n      gyjsData:{},\r\n      gyjsDataList:[],\r\n      zfylDataList:[],\r\n      gyjsBeianDataList:[],\r\n      zfylBeianDataList:[],\r\n      specObj:{},\r\n      single: true,\r\n      showSearch: false,\r\n      total: 0,\r\n      isCopy: 0,\r\n      softwareDevelopingFormulaList: [],\r\n      efficacyOptions: [],\r\n      otherSpecialClaimsOptions: [],\r\n      formulaMaterialDatas: [],\r\n      chooseFormulaMaterialDatas: [],\r\n      pFormulaMapData: [],\r\n      zybwOptions: [],\r\n      templateList: [],\r\n      cpjxOptions: [],\r\n      syrqOptions: [],\r\n      syffOptions: [],\r\n      wxTree: [],\r\n      purposeOptions: [],\r\n      categoryArray: [],\r\n      jcXmList: [],\r\n      stabilityDataList: [],\r\n      relationStabilityDataList: [],\r\n      jlOptions: [\r\n        10,50,100,500,1000,\r\n      ],\r\n      mjOptions: [\r\n        10,50,100,\r\n      ],\r\n      plOptions: [],\r\n      checkRow: {\r\n        id:null,\r\n        deptId:null\r\n      },\r\n      categoryProps: {\r\n        label: 'categoryName',\r\n        value: 'categoryId'\r\n      },\r\n      cirDataArray: [],\r\n      isLook:false,\r\n      cirDataProps: {\r\n        label: 'label',\r\n        value: 'id'\r\n      },\r\n      duliDataArray: [],\r\n      cosmeticCaseFirstOptions: [],\r\n      cosmeticCaseSecondOptions: [],\r\n      duliDataProps: {\r\n        label: 'zhType',\r\n        value: 'id'\r\n      },\r\n      formulaTabs:[{\r\n        title:'基础信息',\r\n        code:'base'\r\n      },{\r\n        title:'配方页面',\r\n        code:'formulaMaterial'\r\n      },{\r\n        title:'附件',\r\n        code:'formulaFile'\r\n      }],\r\n      title: \"\",\r\n      open: false,\r\n      isEdit: true,\r\n      certificationOptions:[],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        laboratoryCode: null,\r\n        formulaName: null,\r\n        ewgColor: null,\r\n        comclusionType:null\r\n      },\r\n      form: {},\r\n      rules: {\r\n        projectNo: [\r\n          { required: true, message: '请选择项目编码'},\r\n        ],\r\n        customerName: [\r\n          { required: true, message: '客户名称不允许为空'},\r\n        ],\r\n        productName: [\r\n          { required: true, message: '产品名称不允许为空'},\r\n        ],\r\n        cirText: [\r\n          { required: true, message: '请选择CIR历史用量'},\r\n        ],\r\n        duliText: [\r\n          { required: true, message: '请选择毒理使用量参考'},\r\n        ],\r\n        laboratoryCode: [\r\n          { required: true, message: '实验室编码不允许为空'},\r\n        ],\r\n        categoryText: [\r\n          { required: true, message: '配方类别不允许为空'},\r\n        ],\r\n        pflx: [\r\n          { required: true, message: '使用方法不允许为空'},\r\n        ],\r\n      },\r\n      rflOptions:[\r\n        {\r\n          dictValue:'3',\r\n          dictLabel:'染发类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'防脱类'\r\n        }, {\r\n          dictValue:'1',\r\n          dictLabel:'烫发类'\r\n        }\r\n      ],\r\n      yplyOptions:[\r\n        {\r\n          dictValue:'0',\r\n          dictLabel:'检测方法'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'实验室小样'\r\n        }, {\r\n          dictValue:'3',\r\n          dictLabel:'中试样品'\r\n        }, {\r\n          dictValue:'4',\r\n          dictLabel:'大货样品'\r\n        }\r\n      ],\r\n      qiOptions:[\r\n        {\r\n          dictValue:'有香味',\r\n         }, {\r\n          dictValue:'有原料特征性气味',\r\n         }, {\r\n          dictValue:'无味',\r\n         }\r\n      ],\r\n      qbmblOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'祛斑美白类'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'祛斑美白类(仅具物理遮盖作用)'\r\n        }\r\n      ],\r\n      cosmeticClassificationOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'第一类化妆品'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'第二类化妆品'\r\n        }\r\n      ],\r\n      caseOptions:[\r\n        {\r\n          dictValue:'1',\r\n          dictLabel:'情形一'\r\n        }, {\r\n          dictValue:'2',\r\n          dictLabel:'情形二'\r\n        }\r\n      ],\r\n      useOptions:[\r\n        {\r\n          value:'/',\r\n        },  {\r\n          value:'指定',\r\n        }\r\n      ],\r\n      specMaterialDatas1:[\r\n        {\r\n          value:'二氧化钛'\r\n        },\r\n        {\r\n          value:'CI 77891'\r\n        }\r\n      ],\r\n      specMaterialDatas2:[\r\n        {\r\n          value:'氧化锌'\r\n        },\r\n        {\r\n          value:'CI 77947'\r\n        }\r\n      ],\r\n      ffjtxfxpgOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'高风险'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'中风险'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'低风险'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'无风险'\r\n      },{\r\n        dictValue:'4',\r\n        dictLabel:'测试没通过'\r\n      }],\r\n      wdxOptions: [{\r\n        dictValue:'0',\r\n        dictLabel:'进行中'\r\n      },{\r\n        dictValue:'1',\r\n        dictLabel:'测试通过'\r\n      },{\r\n        dictValue:'2',\r\n        dictLabel:'测试失败'\r\n      },{\r\n        dictValue:'3',\r\n        dictLabel:'条件接受'\r\n      }],\r\n      ypFromOptions: [\r\n        {label: '实验室',value: 0},\r\n        {label: '中试',value: 1},\r\n        {label: '生产',value: 2},\r\n        {label: '生技复样',value: 3},\r\n      ],\r\n      stabilityStatusOptions: [\r\n        {label: '进行中',value: 0},\r\n        {label: '测试通过',value: 1},\r\n        {label: '测试失败',value: 2},\r\n        {label: '条件接受',value: 3},\r\n      ],\r\n    };\r\n  },\r\n  async created() {\r\n    this.wxTree = this.toTree(this.wxOptions, 0)\r\n\r\n    //使用目的\r\n    let certificationRes = await this.getDicts(\"SOFTWARE_CERTIFICATION\")\r\n    this.certificationOptions = certificationRes.data\r\n\r\n    this.getDicts(\"qc_jypl\").then(response => {\r\n      this.plOptions = response.data\r\n    })\r\n  },\r\n  watch: {\r\n    \"$route.query.params\": {\r\n      immediate: true,\r\n      handler() {\r\n        let params = this.$route.query.params;\r\n        if(params) {\r\n          let query = Base64.decode(Base64.decode(params));\r\n          if(query){\r\n            query = JSON.parse(query);\r\n            this.reset();\r\n            this.init(1);\r\n            this.handleUpdate(query.id,query.shareType===1?true:false);\r\n            this.btnLoading = true;\r\n            this.title = query.shareType===1?'修改配方':'查看配方';\r\n          }\r\n        }else{\r\n          this.reset();\r\n          this.init(2);\r\n          this.queryProjectList();\r\n          this.handleAdd();\r\n          this.isLook = true;\r\n        }\r\n      },\r\n    }\r\n  },\r\n  methods: {\r\n    async showWx() {\r\n      this.wxOpen = true\r\n    },\r\n    selectDictLabel,\r\n    async designateChange(row){\r\n       let designatedUse = row.designatedUse;\r\n       if('指定'===designatedUse){  //指定原料\r\n          let materialId = row.materialId;\r\n          let res = await queryFormulaAppointMaterialDataList({id:materialId});\r\n          row.materialCodes = res;\r\n       }else{\r\n          row.appointCode = '';\r\n          row.materialCodes = [];\r\n       }\r\n    },\r\n    async queryProjectList(){\r\n      let projectList  = await formualList();\r\n      this.projectList = projectList;\r\n    },\r\n    async init(type){\r\n      if(checkPermi(['software:softwareDevelopingFormula:lookMaterialGoodsName'])) {\r\n        this.isShowMaterialGoodsName = 1;\r\n      }else{\r\n        this.isShowMaterialGoodsName = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genPformulaInfo'])) {\r\n        this.isGenFormula = 1;\r\n      }else{\r\n        this.isGenFormula = 0;\r\n      }\r\n      if(checkPermi(['software:softwareDevelopingFormula:genBMFormulaInfo'])) {\r\n        this.isBMformula = 1;\r\n      }else{\r\n        this.isBMformula = 0;\r\n      }\r\n      this.getDicts(\"ZXBZ_STATUS\").then(response => {\r\n        this.statusOptions = response.data;\r\n      })\r\n      this.getDicts(\"OWNERSHOP_COMPANY\").then(response => {\r\n        this.ownershopCompanyOptions = response.data;\r\n      })\r\n      this.getDicts(\"BZXZ\").then(response => {\r\n        this.bzxzOptions = response.data;\r\n      })\r\n      this.getDicts(\"rd_ysty_type\").then(response => {\r\n        this.typeOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE1\").then(response => {\r\n        this.cosmeticCaseFirstOptions = response.data;\r\n      })\r\n      this.getDicts(\"SOFTWARE_FORMULA_CASE2\").then(response => {\r\n        this.cosmeticCaseSecondOptions = response.data;\r\n      })\r\n      const categorySet = new Set()\r\n      const jcXmList = await allJcxm({type:1})\r\n      this.jcXmList = jcXmList\r\n      for (const item of jcXmList) {\r\n        categorySet.add(item.category)\r\n      }\r\n      const categoryList = []\r\n      for (const category of categorySet) {\r\n        categoryList.push({\r\n          category,\r\n          array: jcXmList.filter(i=>i.category === category),\r\n        })\r\n      }\r\n      this.categoryList = categoryList.filter(i=>i.category !== '微生物')\r\n      this.templateList = await allBcpTemplate();\r\n      //配方使用用途\r\n      let purposeRes =  await this.getDicts(\"SOFTWARE_FORMULA_PURPOSE\")\r\n      this.purposeOptions = purposeRes.data\r\n      //获取字典数据\r\n      let dictObj = await querySoftwareDevelopingFormulaDict();\r\n      this.efficacyOptions = dictObj.GXXC_DATA_LIST;\r\n      this.otherSpecialClaimsOptions = dictObj.GXXC_DATA_LIST_OTHER;\r\n      this.zybwOptions = dictObj.ZYBW_DATA_LIST;\r\n      this.cpjxOptions = dictObj.CPJX_DATA_LIST;\r\n      this.syrqOptions = dictObj.SYRQ_DATA_LIST;\r\n      this.syffOptions = dictObj.PFLX_DATA_LIST;\r\n      let categoryAllArray = await queryFormulaClassifyData()\r\n      if(type==2){\r\n        let datas = [22,23,24,25,26,27,43,50,61];\r\n        categoryAllArray = categoryAllArray.filter(i=>!datas.includes(i.categoryId));\r\n      }\r\n      this.categoryArray = await getFormulaCategoryTree(categoryAllArray)\r\n      let zxbzList = await queryFormulaZxbzDataList();\r\n      this.zxbzList = zxbzList;\r\n      //获取cir历史使用量\r\n      let cirDataAllArray = await queryCirHistoryData();\r\n      this.cirDataArray = await getCirDataTree(cirDataAllArray);\r\n      //获取毒理/供应商使用量参考\r\n      let duliDataAllArray = await queryDuliHistoryData();\r\n      this.duliDataArray = await getDuliDataTree(duliDataAllArray);\r\n    },\r\n    async itemNameChange(itemName){\r\n      let itemNames = this.itemNames;\r\n      let arr = itemNames.filter(i=> i.id === itemName)\r\n      let itemNameText = '';\r\n      if(arr && arr[0]) {\r\n        itemNameText = arr[0].text;\r\n      }\r\n       this.form.itemNameText = itemNameText;\r\n     },\r\n    async projectChange(projectNo) {\r\n      //获取项目详情\r\n      let projectDetail = await formualProjectDetail({projectNo});\r\n      let isEdit = true;\r\n      if(projectNo.indexOf(\"P\")!=-1 || projectNo=='210002089' ||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n         isEdit = false;\r\n      }\r\n      this.isEdit = isEdit;\r\n      this.form.itemName = '';\r\n      this.form.itemNameText = '';\r\n      this.itemNames = [];\r\n      if(projectDetail!=null && projectDetail.id){\r\n        this.form.customerName = projectDetail.customerName;\r\n        this.form.productName = projectDetail.productName;\r\n        this.form.brandName = projectDetail.brandName;\r\n        this.form.seriesName = projectDetail.seriesName;\r\n        let itemNames = projectDetail.itemNames;\r\n        if(itemNames){\r\n          itemNames = JSON.parse(itemNames);\r\n          this.itemNames = itemNames;\r\n        }\r\n       }else{\r\n        this.form.customerName = '';\r\n        this.form.productName = '';\r\n        this.form.brandName = '';\r\n        this.form.seriesName = '';\r\n       }\r\n    },\r\n    async zxbzChange(id){\r\n      let zxbzDetail = await queryFormulaZxbzDataDetail({id});\r\n      this.form.execNumber = zxbzDetail.zxbzh;\r\n      this.zxbzDetail = zxbzDetail;\r\n    },\r\n    async getList() {\r\n      let params = Object.assign({},this.queryParams)\r\n      this.loading = true\r\n      let res = await listSoftwareDevelopingFormula(params)\r\n      this.loading = false\r\n      this.softwareDevelopingFormulaList = res.rows\r\n      this.total = res.total\r\n    },\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    async reset() {\r\n      this.currentTab = 'base';\r\n      this.stabilityDataList = [];\r\n      this.relationStabilityDataList = [];\r\n      this.activeNames = [];\r\n      this.isCopy = 0;\r\n      this.isLook = false;\r\n      this.activeName = 'base';\r\n      this.itemNames = [];\r\n      this.formulaMaterialDatas = [];\r\n      this.chooseFormulaMaterialDatas = [];\r\n      this.pFormulaMapData = [];\r\n      this.recipeChangeHistoryData = [];\r\n      this.gyjsData = {};\r\n      this.gyjsDataList = [];\r\n      this.zfylDataList = [];\r\n      this.gyjsBeianDataList = [];\r\n      this.zfylBeianDataList = [];\r\n      this.softwareFormulaSpecList = [];\r\n      this.formulaTableDataList = [];\r\n      this.formulaTableDataListBack = [];\r\n      this.compositionTableDataList = [];\r\n      this.compositionTableDataListBack = [];\r\n      this.specMaterialDatas = [];\r\n      this.itemArray = [];\r\n      this.userItemArray = [];\r\n      this.zxbzDetail = {};\r\n      this.gtNumStr = '';\r\n      this.ltNumStr = '';\r\n      this.specObj = {};\r\n      this.totalPercentVal = 0;\r\n      this.specId = null;\r\n      this.sjTotalPercet = 0;\r\n      this.form = {\r\n        id: null,\r\n        currentTemplateId: null,\r\n        relationMaterialCode: null,\r\n        formulaName: null,\r\n        englishName: null,\r\n        materialCode: null,\r\n        formulaCodeParams: null,\r\n        price: null,\r\n        weight: 100,\r\n        isLock: 1,\r\n        isMateral: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        operator: null,\r\n        createdTime: null,\r\n        isDel: null,\r\n        lastModifiedTime: null,\r\n        note: null,\r\n        formulaCode: null,\r\n        duliId: null,\r\n        cirId: null,\r\n        cirText: null,\r\n        duliText: null,\r\n        laboratoryCode: null,\r\n        productName: null,\r\n        brandId: null,\r\n        customerCode: null,\r\n        customerName: null,\r\n        seriesName: null,\r\n        appearance: null,\r\n        colour: null,\r\n        ph: null,\r\n        viscosity: null,\r\n        stabilityresult: null,\r\n        gxgs: null,\r\n        category: null,\r\n        brandName: null,\r\n        standard: null,\r\n        introFile: [],\r\n        organizationId: null,\r\n        oldFormulaCode: null,\r\n        copyFormulaId: null,\r\n        addTips: null,\r\n        wendingxingFile: [],\r\n        gongyiFile: [],\r\n        xiangrongxingFile: [],\r\n        weishenwuFile: [],\r\n        xiaofeizheFile: [],\r\n        qitaFile: [],\r\n        execNumber: null,\r\n        isDraft: 1,\r\n        gxxc: [],\r\n        gxxcOther: [],\r\n        zybw: [],\r\n        syrq: [],\r\n        cpjx: [],\r\n        pflx: [],\r\n        cpfldm: null,\r\n        cosmeticClassification: null,\r\n        cosmeticCase: null,\r\n        execNumberId: null,\r\n        aqpgjl: null,\r\n        gongyijianshu: null,\r\n        gongyijianshuBeian: null,\r\n        ranfalei: [],\r\n        cosmeticCaseFirst: [],\r\n        cosmeticCaseSecond: [],\r\n        qubanmeibailei: [],\r\n        fangshailei: false,\r\n        sfa: null,\r\n        pa: null,\r\n        yushousfa: null,\r\n        xingongxiao: false,\r\n        xingongxiaocontent: null,\r\n        ftlTime: null,\r\n        filCode: null,\r\n        baCode: null,\r\n        baTime: null,\r\n        filCodeNote: null,\r\n        baCodeNote: null,\r\n        waxcName: null,\r\n        waxcOthername: null,\r\n        waxcStatus: null,\r\n        baStatus: null,\r\n        formulaPid: null,\r\n        bpNote: null,\r\n        zsTime: null,\r\n        zsCode: null,\r\n        gongyijianshuZs: null,\r\n        yfFile: null,\r\n        zsFile: null,\r\n        isLove: null,\r\n        upRate: null,\r\n        oriPrice: null,\r\n        levelNum: null,\r\n        purpose: '普通',\r\n        formulaStatus: 0,\r\n        formulaRemark: null,\r\n        projectNo: null,\r\n        itemName: null,\r\n        itemNameText: null,\r\n        formulaImage: null,\r\n        formulaConstructionIdeas: null,\r\n        isRealse: null,\r\n        materialStatusInfo: null,\r\n        importCountryInfo: null,\r\n        operatorName: null,\r\n        stabilityStatus: null,\r\n        isResult: null,\r\n        isGt: null,\r\n        type: null,\r\n        weishenwuResult: null,\r\n        weishenwuRemark: null,\r\n        xiangrongxingResult: null,\r\n        xiangrongxingRemark: null,\r\n        wendingxingResult: null,\r\n        wendingxingRemark: null,\r\n        materialCycle: null\r\n      };\r\n      this.resetForm(\"form\");\r\n\r\n      if (!this.wxOptions.length) {\r\n        this.wxOptions = await allTreeData({type: 9})\r\n      }\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n    },\r\n    handleFormulaMaterialSelectionChange(selection) {\r\n      this.chooseFormulaMaterialDatas = selection;\r\n    },\r\n    async genPformulaInfo() {\r\n      let chooseFormulaMaterialDatas = this.chooseFormulaMaterialDatas;\r\n      if (chooseFormulaMaterialDatas && chooseFormulaMaterialDatas.length > 0) {\r\n        for (let item of chooseFormulaMaterialDatas) {\r\n          let type = item.type;\r\n          if (type == 1) {\r\n            this.msgError('生成错误,请选择原料编码信息');\r\n            return;\r\n          }\r\n        }\r\n        let id = this.form.id;\r\n        let data = await generatePFormulaInfo({id,formulaMaterialDatas:JSON.stringify(chooseFormulaMaterialDatas)});\r\n        this.msgSuccess(\"操作成功\");\r\n      } else {\r\n        this.msgError('请选择原料信息!');\r\n      }\r\n    },\r\n    limitDecimal(row){\r\n       const inputValue = row.percentage;\r\n      // 正则表达式匹配最多6位小数的数字\r\n      const regex = /^\\d*\\.?\\d{0,6}$/;\r\n      if (regex.test(inputValue)) {\r\n        this.lastValue = inputValue;\r\n      } else {\r\n        // 如果不符合条件，回退到上一个有效值\r\n        row.percentage = this.lastValue;\r\n      }\r\n    },\r\n    async generBMaterialInfo(id) {\r\n       this.$confirm('您确定要生成B代码吗?', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"info\"\r\n      }).then(async function () {\r\n         let data = await generateBMaterialInfo({id});\r\n       }).then(() => {\r\n         this.msgSuccess(\"操作成功\");\r\n      }).catch(() => {});\r\n    },\r\n    async genNewformulaInfo() {\r\n      let id = this.form.id;\r\n      let res = await generateNewformulaInfo({id});\r\n      this.msgSuccess(\"操作成功\");\r\n    },\r\n    handleAdd() {\r\n      this.reset();\r\n      this.formulaTabs = [{\r\n        title: '基础信息',\r\n        code: 'base'\r\n      }, {\r\n        title: '配方页面',\r\n        code: 'formulaMaterial'\r\n      }, {\r\n        title: '附件',\r\n        code: 'formulaFile'\r\n      }];\r\n      this.open = true;\r\n      this.title = \"创建配方\";\r\n    },\r\n    async handleUpdate(id,isLook) {\r\n      this.reset();\r\n      let formulaTabs = await queryLookFormulaTabs();\r\n      this.formulaTabs = formulaTabs;\r\n      this.btnLoading = true;\r\n      getSoftwareDevelopingFormulaDetail(id).then(async response => {\r\n        let form = response.data;\r\n        let isEdit = true;\r\n        let projectNo = form.projectNo;\r\n        if(projectNo.indexOf(\"P\")!=-1 ||projectNo=='210002089'||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){\r\n          isEdit = false;\r\n        }\r\n        if(form.isLock===2){\r\n          isEdit = false;\r\n        }\r\n        this.isEdit = isEdit;\r\n        if(form.fangshailei){\r\n          form.fangshailei = form.fangshailei==1?true:false;\r\n        }else{\r\n          form.fangshailei = false;\r\n        }\r\n        if(form.xingongxiao){\r\n          form.xingongxiao =form.xingongxiao==1?true:false;\r\n        }else{\r\n          form.xingongxiao = false;\r\n        }\r\n        let specObj = form.specObj;\r\n        if(specObj){\r\n          this.specObj = JSON.parse(specObj);\r\n        }else{\r\n          this.specObj = {};\r\n        }\r\n        let formulaObj = form.formulaObj;\r\n        if(formulaObj){\r\n          formulaObj = JSON.parse(formulaObj);\r\n          this.totalPercentVal = formulaObj.totalPercentVal;\r\n          this.sjTotalPercet = formulaObj.sjTotalPercet;\r\n          if(formulaObj.dataList){\r\n            this.formulaTableDataList = formulaObj.dataList;\r\n            this.formulaTableDataListBack = formulaObj.dataList;\r\n          }else{\r\n            this.formulaTableDataList = [];\r\n            this.formulaTableDataListBack = [];\r\n          }\r\n          if(formulaObj.allList){\r\n            this.compositionTableDataList = formulaObj.allList;\r\n            this.compositionTableDataListBack = formulaObj.allList;\r\n          }else{\r\n            this.compositionTableDataList = [];\r\n            this.compositionTableDataListBack = [];\r\n          }\r\n          if(formulaObj.specMaterialData){\r\n            this.specMaterialDatas = formulaObj.specMaterialData;\r\n          }else{\r\n            this.specMaterialDatas = [];\r\n          }\r\n          if(formulaObj.ltNumStr){\r\n            this.ltNumStr = formulaObj.ltNumStr;\r\n          }else{\r\n            this.ltNumStr = '';\r\n          }\r\n          if(formulaObj.gtNumStr){\r\n            this.gtNumStr = formulaObj.gtNumStr;\r\n          }else{\r\n            this.gtNumStr = '';\r\n          }\r\n        }\r\n        //获取配方原料信息\r\n        let formulaMaterialDatas = form.formulaMaterialDatas;\r\n        if(formulaMaterialDatas){\r\n           this.formulaMaterialDatas = JSON.parse(formulaMaterialDatas);\r\n        }else{\r\n          this.formulaMaterialDatas = [];\r\n        }\r\n        if (form.categoryText) {\r\n          let categoryTextList = form.categoryText.split(',');\r\n          let categoryIds = []\r\n          for (let t of categoryTextList) {\r\n            categoryIds.push(parseInt(t))\r\n          }\r\n          form.categoryText = categoryIds\r\n        } else {\r\n          form.categoryText = [];\r\n        }\r\n        if (form.cirText) {\r\n          let cirTextList = form.cirText.split(',');\r\n          let cirId = []\r\n          for (let t of cirTextList) {\r\n            cirId.push(parseInt(t))\r\n          }\r\n          form.cirText = cirId\r\n        } else {\r\n          form.cirText = [];\r\n        }\r\n        if (form.duliText) {\r\n          let duliTextList = form.duliText.split(',');\r\n          let duliId = []\r\n          for (let t of duliTextList) {\r\n            duliId.push(parseInt(t))\r\n          }\r\n          form.duliText = duliId\r\n        } else {\r\n          form.duliText = [];\r\n        }\r\n        if (form.gxxc) {\r\n          form.gxxc = form.gxxc.split(\",\");\r\n        } else {\r\n          form.gxxc = [];\r\n        }\r\n        if (form.gxxcOther) {\r\n          form.gxxcOther = form.gxxcOther.split(\",\");\r\n        } else {\r\n          form.gxxcOther = [];\r\n        }\r\n        if (form.zybw) {\r\n          form.zybw = form.zybw.split(\",\");\r\n        } else {\r\n          form.zybw = [];\r\n        }\r\n        if (form.syrq) {\r\n          form.syrq = form.syrq.split(\",\");\r\n        } else {\r\n          form.syrq = [];\r\n        }\r\n        if (form.cpjx) {\r\n          form.cpjx = form.cpjx.split(\",\");\r\n        } else {\r\n          form.cpjx = [];\r\n        }\r\n        if (form.pflx) {\r\n          form.pflx = form.pflx.split(\",\");\r\n        } else {\r\n          form.pflx = [];\r\n        }\r\n        if (form.ranfalei) {\r\n          form.ranfalei = form.ranfalei.split(\",\");\r\n        } else {\r\n          form.ranfalei = [];\r\n        }\r\n        if (form.cosmeticCaseFirst) {\r\n          form.cosmeticCaseFirst = form.cosmeticCaseFirst.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseFirst = [];\r\n        }\r\n        if (form.cosmeticCaseSecond) {\r\n          form.cosmeticCaseSecond = form.cosmeticCaseSecond.split(\",\");\r\n        } else {\r\n          form.cosmeticCaseSecond = [];\r\n        }\r\n        if (form.qubanmeibailei) {\r\n          form.qubanmeibailei = form.qubanmeibailei.split(\",\");\r\n        } else {\r\n          form.qubanmeibailei = [];\r\n        }\r\n        if (form.introFile) {\r\n          form.introFile = JSON.parse(form.introFile);\r\n        } else {\r\n          form.introFile = [];\r\n        }\r\n        if (form.wendingxingFile) {\r\n          form.wendingxingFile = JSON.parse(form.wendingxingFile);\r\n        } else {\r\n          form.wendingxingFile = [];\r\n        }\r\n        if (form.gongyiFile) {\r\n          form.gongyiFile = JSON.parse(form.gongyiFile);\r\n        } else {\r\n          form.gongyiFile = [];\r\n        }\r\n        if (form.xiangrongxingFile) {\r\n          form.xiangrongxingFile = JSON.parse(form.xiangrongxingFile);\r\n        } else {\r\n          form.xiangrongxingFile = [];\r\n        }\r\n        if (form.weishenwuFile) {\r\n          form.weishenwuFile = JSON.parse(form.weishenwuFile);\r\n        } else {\r\n          form.weishenwuFile = [];\r\n        }\r\n        if (form.xiaofeizheFile) {\r\n          form.xiaofeizheFile = JSON.parse(form.xiaofeizheFile);\r\n        } else {\r\n          form.xiaofeizheFile = [];\r\n        }\r\n        if (form.qitaFile) {\r\n          form.qitaFile = JSON.parse(form.qitaFile);\r\n        } else {\r\n          form.qitaFile = [];\r\n        }\r\n        let itemNames = [];\r\n        if(form.itemArr){\r\n          itemNames = form.itemArr;\r\n        }\r\n        this.itemNames = itemNames;\r\n\r\n        let pFormulaMapData = [];\r\n        if(form.pFormulaMapData){\r\n          pFormulaMapData = form.pFormulaMapData;\r\n        }\r\n        this.pFormulaMapData = pFormulaMapData;\r\n        //获取信息数据\r\n        let execNumberId = form.execNumberId;\r\n        if(execNumberId){\r\n          this.zxbzChange(execNumberId);\r\n        }else{\r\n          this.zxbzDetail = {};\r\n        }\r\n        let jcxmJson = form.jcxmJson;\r\n        if(jcxmJson){\r\n           this.itemArray = JSON.parse(jcxmJson);\r\n        }else{\r\n          this.itemArray = [];\r\n        }\r\n        this.form = form;\r\n        let gongyijianshu = form.gongyijianshu;\r\n        if(gongyijianshu){\r\n          let gyjsData = JSON.parse(gongyijianshu);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.refreshFormulaLegalGy('0');\r\n            this.gyjsDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsDataList = [];\r\n          this.zfylDataList = [];\r\n          this.refreshFormulaLegalGy('0');\r\n        }\r\n        let gongyijianshuBeian = form.gongyijianshuBeian;\r\n        if(gongyijianshuBeian){\r\n          let gyjsData = JSON.parse(gongyijianshuBeian);\r\n          let gyjs = gyjsData.gyjs;\r\n          if(gyjs){\r\n            this.gyjsBeianDataList = gyjs.map(name => ({ name }));;\r\n          }else{\r\n            this.gyjsBeianDataList = [];\r\n          }\r\n          let zfyl = gyjsData.zfyl;\r\n          if(gyjs){\r\n            this.zfylBeianDataList = zfyl.map(name => ({ name }));;\r\n          }else{\r\n            this.zfylBeianDataList = [];\r\n          }\r\n        }else{\r\n          this.gyjsBeianDataList = [];\r\n          this.zfylBeianDataList = [];\r\n        }\r\n        this.open = true;\r\n        if(isLook){\r\n          this.title = \"修改配方\";\r\n        }else{\r\n          this.title = \"查看配方\";\r\n        }\r\n        this.isLook = isLook;\r\n        this.btnLoading = false;\r\n      });\r\n      let recipeChangeHistoryData = await queryFormualMaterialRecipeChangeHistoryData({id});\r\n      this.recipeChangeHistoryData = recipeChangeHistoryData;\r\n      //获取spec内容\r\n      this.queryMaterialFormulaSpecDataList(id);\r\n      //获取关联稳定性记录内容\r\n      this.queryFormulaStabilityRecordDataList(id);\r\n    },\r\n    stabilityStatusFormat(row) {\r\n      const arr = this.stabilityStatusOptions.filter(i=> i.value === row.stabilityStatus)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    ypFormat(row) {\r\n      const arr = this.ypFromOptions.filter(i=> i.value === row.ypFrom)\r\n      if(arr && arr[0]) {\r\n        return arr[0].label\r\n      }\r\n    },\r\n    async queryMaterialFormulaSpecDataList(id) {\r\n      let softwareFormulaSpecList = await queryMaterialFormulaSpecDataList({id});\r\n      this.softwareFormulaSpecList = softwareFormulaSpecList;\r\n    },\r\n    async queryFormulaStabilityRecordDataList(id) {\r\n      let formulaStabilityObj = await queryFormulaStabilityRecordDataList({id});\r\n      let relationStabilityDataList = formulaStabilityObj.relationStabilityDataList;\r\n      let stabilityDataList = formulaStabilityObj.stabilityDataList;\r\n      this.stabilityDataList = stabilityDataList\r\n      this.relationStabilityDataList = relationStabilityDataList\r\n    },\r\n    async copyGongyi() {\r\n      await this.$confirm('是否确认复制工艺数据,会清空已填数据!')\r\n      this.gyjsBeianDataList = JSON.parse(JSON.stringify(this.gyjsDataList));\r\n      this.zfylBeianDataList = JSON.parse(JSON.stringify(this.zfylDataList));\r\n    },\r\n    async submitUploadForm() {\r\n      this.btnLoading = true;\r\n      let formulaImage = this.form.formulaImage;\r\n      let formulaConstructionIdeas = this.form.formulaConstructionIdeas;\r\n      let id = this.form.id;\r\n      let remark = this.form.remark;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n      let params = {\r\n        id,formulaImage,formulaMaterialDatas,remark,formulaConstructionIdeas\r\n      };\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaImg(params);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitUploadFileForm() {\r\n      this.btnLoading = true;\r\n      let form = this.form;\r\n      let param = {};\r\n      param.id = form.id;\r\n      if (form.introFile) {\r\n        param.introFile = JSON.stringify(form.introFile);\r\n      } else {\r\n        param.introFile = \"\";\r\n      }\r\n      if (form.wendingxingFile) {\r\n        param.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      } else {\r\n        param.wendingxingFile = \"\";\r\n      }\r\n      if (form.gongyiFile) {\r\n        param.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      } else {\r\n        param.gongyiFile = \"\";\r\n      }\r\n      if (form.xiangrongxingFile) {\r\n        param.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      } else {\r\n        param.xiangrongxingFile = \"\";\r\n      }\r\n      if (form.weishenwuFile) {\r\n        param.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      } else {\r\n        param.weishenwuFile = \"\";\r\n      }\r\n      if (form.xiaofeizheFile) {\r\n        param.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      } else {\r\n        param.xiaofeizheFile = \"\";\r\n      }\r\n      if (form.qitaFile) {\r\n        param.qitaFile = JSON.stringify(form.qitaFile);\r\n      } else {\r\n        param.qitaFile = \"\";\r\n      }\r\n      param.wendingxingResult = form.wendingxingResult;\r\n      param.wendingxingRemark = form.wendingxingRemark;\r\n      param.xiangrongxingResult = form.xiangrongxingResult;\r\n      param.xiangrongxingRemark = form.xiangrongxingRemark;\r\n      param.weishenwuResult = form.weishenwuResult;\r\n      param.weishenwuRemark = form.weishenwuRemark;\r\n      try {\r\n        let res = await updateSoftwareDevelopingFormulaFileImg(param);\r\n        this.msgSuccess('修改成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false\r\n      }\r\n    },\r\n    async submitForm(isDraft) {\r\n      if(isDraft===0){\r\n        await this.$refs[\"form\"].validate()\r\n      }\r\n      let form = Object.assign({},this.form);\r\n      let projectNo = form.projectNo;\r\n      if(!projectNo || projectNo.length==0){\r\n        this.msgError('请选择项目');\r\n        return;\r\n      }\r\n      let categoryText = form.categoryText;\r\n      if(!categoryText || categoryText.length==0){\r\n        this.msgError('请选择配方类别');\r\n        return;\r\n      }\r\n      form.isDraft = isDraft;\r\n      if(form.categoryText && form.categoryText.length > 0 && isArray(form.categoryText)) {\r\n        form.categoryText = form.categoryText.join(',')\r\n      }else{\r\n        form.categoryText = '';\r\n      }\r\n      if(form.cirText && form.cirText.length > 0 && isArray(form.cirText)) {\r\n        form.cirText = form.cirText.join(',')\r\n      }else{\r\n        form.cirText = '';\r\n      }\r\n      if(form.duliText && form.duliText.length > 0 && isArray(form.duliText)) {\r\n        form.duliText = form.duliText.join(',')\r\n      }else{\r\n        form.duliText = '';\r\n      }\r\n      if(form.gxxc){\r\n        form.gxxc = form.gxxc.join(',');\r\n      }else{\r\n        form.gxxc = '';\r\n      }\r\n      if(form.gxxcOther){\r\n        form.gxxcOther = form.gxxcOther.join(',');\r\n      }else{\r\n        form.gxxcOther = '';\r\n      }\r\n      if(form.zybw){\r\n        form.zybw = form.zybw.join(',');\r\n      }else{\r\n        form.zybw = '';\r\n      }\r\n      if(form.syrq){\r\n        form.syrq = form.syrq.join(',');\r\n      }else{\r\n        form.syrq = '';\r\n      }\r\n      if(form.cpjx){\r\n        form.cpjx = form.cpjx.join(',');\r\n      }else{\r\n        form.cpjx = '';\r\n      }\r\n      if(form.pflx){\r\n        form.pflx = form.pflx.join(',');\r\n      }else{\r\n        form.pflx = '';\r\n      }\r\n      if(form.ranfalei){\r\n        form.ranfalei = form.ranfalei.join(',');\r\n      }else{\r\n        form.ranfalei = '';\r\n      }\r\n      if(form.cosmeticCaseFirst){\r\n        form.cosmeticCaseFirst = form.cosmeticCaseFirst.join(',');\r\n      }else{\r\n        form.cosmeticCaseFirst = '';\r\n      }\r\n      if(form.cosmeticCaseSecond){\r\n        form.cosmeticCaseSecond = form.cosmeticCaseSecond.join(',');\r\n      }else{\r\n        form.cosmeticCaseSecond = '';\r\n      }\r\n      if(form.qubanmeibailei){\r\n        form.qubanmeibailei = form.qubanmeibailei.join(',');\r\n      }else{\r\n        form.qubanmeibailei = '';\r\n      }\r\n      if(form.introFile){\r\n        form.introFile = JSON.stringify(form.introFile);\r\n      }else{\r\n        form.introFile = '';\r\n      }\r\n      if(form.wendingxingFile){\r\n        form.wendingxingFile = JSON.stringify(form.wendingxingFile);\r\n      }else{\r\n        form.wendingxingFile = '';\r\n      }\r\n      if(form.gongyiFile){\r\n        form.gongyiFile = JSON.stringify(form.gongyiFile);\r\n      }else{\r\n        form.gongyiFile = '';\r\n      }\r\n      if(form.xiangrongxingFile){\r\n        form.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);\r\n      }else{\r\n        form.xiangrongxingFile = '';\r\n      }\r\n      if(form.weishenwuFile){\r\n        form.weishenwuFile = JSON.stringify(form.weishenwuFile);\r\n      }else{\r\n        form.weishenwuFile = '';\r\n      }\r\n      if(form.xiaofeizheFile){\r\n        form.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);\r\n      }else{\r\n        form.xiaofeizheFile = '';\r\n      }\r\n      if(form.qitaFile){\r\n        form.qitaFile = JSON.stringify(form.qitaFile);\r\n      }else{\r\n        form.qitaFile = '';\r\n      }\r\n      if(form.fangshailei){\r\n        form.fangshailei = 1;\r\n      }else{\r\n        form.fangshailei = 0;\r\n      }\r\n      if(form.xingongxiao){\r\n        form.xingongxiao = 1;\r\n      }else{\r\n        form.xingongxiao = 0;\r\n      }\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n        for(let item of formulaMaterialDatas){\r\n          let designatedUse = item.designatedUse;\r\n          let isRelation = item.isRelation;\r\n          let isFx = item.isFx;\r\n          let remark = item.remark;\r\n          let relationCode = item.relationCode;\r\n          let materialCode = item.materialCode;\r\n          let appointCode = item.appointCode;\r\n          if(designatedUse==='指定' && !appointCode){\r\n            this.msgError('请选择指定原料!');\r\n            return;\r\n          }\r\n          if(isRelation==1 && !remark){\r\n            this.msgError('请输入使用代码['+materialCode+']的备注,存在推荐原料['+relationCode+']');\r\n            return;\r\n          }\r\n          if(isFx==1){\r\n            let msg = materialCode + \"为护肤风险原料，请核实!\";\r\n            await this.$confirm(msg, \"警告\", {\r\n              confirmButtonText: \"确定\",\r\n              cancelButtonText: \"取消\",\r\n              type: \"warning\"\r\n            })\r\n          }\r\n        }\r\n        form.formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);\r\n        let returnObj = this.isRepeat(formulaMaterialDatas);\r\n        let num = returnObj.num;\r\n        if(num>0){\r\n          let repeatCode = returnObj.repeatCode;\r\n          await this.$confirm('存在重复原料'+repeatCode+',是否确认添加!')\r\n        }\r\n      }else{\r\n        await this.$confirm('您还没有选择原料，确定添加配方？');\r\n        form.formulaMaterialDatas = '';\r\n      }\r\n      if(!form.pflx && isDraft===0){\r\n         this.msgError('请选择使用方法!');\r\n         return;\r\n      }\r\n      if (form.id != null) {\r\n        try {\r\n          this.btnLoading = true\r\n          await updateSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.form.currentVersion = parseFloat(this.form.currentVersion) + 1;\r\n          this.msgSuccess(\"修改成功\")\r\n          //this.close();\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      } else {\r\n        try {\r\n          this.btnLoading = true\r\n          await addSoftwareDevelopingFormula(form)\r\n          this.btnLoading = false\r\n          this.msgSuccess(\"新增成功\")\r\n          this.close();\r\n         } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    close() {\r\n      this.$store.dispatch(\"tagsView/delView\", this.$route);\r\n      let view = {\r\n        fullPath : '/rd/softwareDevelopingFormula',\r\n        name:\"SoftwareDevelopingFormula\",\r\n        path:\"/rd/softwareDevelopingFormula\",\r\n        title:\"研发配方\"\r\n      };\r\n      this.$store.dispatch('tagsView/delCachedView', view).then(() => {\r\n        const { fullPath } = view\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: '/redirect' + fullPath\r\n          })\r\n        })\r\n      })\r\n    },\r\n    //判断是否重复\r\n    isRepeat(datas){\r\n       let returnObj = {num:0,repeatCode:''};\r\n       let repeatCodesSet = new Set();\r\n      if(datas && datas.length>0){\r\n         let codes = [];\r\n         for(let item of datas){\r\n            codes.push(item.materialCode);\r\n         }\r\n         for(let code of codes){\r\n             let index = 0;\r\n             for(let item of datas){\r\n                 let materialCode = item.materialCode;\r\n                 if(code === materialCode){\r\n                   index++;\r\n                 }\r\n             }\r\n             if(index>1){\r\n               repeatCodesSet.add(code);\r\n             }\r\n         }\r\n       }\r\n       if(repeatCodesSet && repeatCodesSet.size>0){\r\n         let str = JSON.stringify(Array.from(repeatCodesSet));\r\n         returnObj = {num:1,repeatCode:str};\r\n       }\r\n       return returnObj;\r\n    },\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$confirm('是否确认删除研发配方编号为\"' + ids + '\"的数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(function() {\r\n          return delSoftwareDevelopingFormula(ids);\r\n        }).then(() => {\r\n          this.getList();\r\n          this.msgSuccess(\"删除成功\");\r\n        }).catch(() => {});\r\n    },\r\n    handleExport() {\r\n      const queryParams = this.queryParams;\r\n      this.$confirm('是否确认导出所有研发配方数据项?', \"警告\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          this.exportLoading = true;\r\n          return exportSoftwareDevelopingFormula(queryParams);\r\n        }).then(response => {\r\n          this.download(response.msg);\r\n          this.exportLoading = false;\r\n        }).catch(() => {});\r\n    },\r\n    async queryMaterialCode() {\r\n      let materialCode = this.form.materialCode;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (materialCode) {\r\n        let res = await getRawMaterialInfoByCode({materialCode});\r\n        if(res.data){\r\n          let isRelation = res.data.isRelation;\r\n           if(isRelation==1){\r\n             let tipsInfo = res.data.tipsInfo;\r\n             this.msgInfo(tipsInfo);\r\n          }\r\n          formulaMaterialDatas.unshift(res.data);\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        this.codeChange(1);\r\n      } else {\r\n        this.msgError('请输入原料代码!');\r\n      }\r\n    },\r\n    async queryFormulaCode() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaInfoByCode({formulaCode:formulaCodeParams});\r\n         if(res.data){\r\n          formulaMaterialDatas.unshift(res.data);\r\n          }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入配方编码!');\r\n      }\r\n    },\r\n    async confirmSelectGoods() {\r\n      let formulaCodeParams = this.form.formulaCodeParams;\r\n      let formulaMaterialDatas = this.formulaMaterialDatas;\r\n      if (formulaCodeParams) {\r\n        let res = await getFormulaLabNoInfoByCode({laboratoryCode:formulaCodeParams});\r\n         if(res.data){\r\n           formulaMaterialDatas.push(res.data);\r\n         }\r\n         this.formulaMaterialDatas = formulaMaterialDatas;\r\n      } else {\r\n        this.msgError('请输入实验室编码!');\r\n      }\r\n    },\r\n    async submitSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.itemArray;\r\n      if(!form.execNumberId){\r\n        this.msgError('请选择执行标准/标准名称');\r\n        return;\r\n      }\r\n      if(!(itemArray &&itemArray.length>0)){\r\n        this.msgError('请选择标准模板');\r\n        return;\r\n      }\r\n      specObj.execNumberId = form.execNumberId;\r\n      specObj.execNumber = form.execNumber;\r\n      specObj.currentTemplateId = this.form.currentTemplateId;\r\n      specObj.formulaId = form.id;\r\n      specObj.itemArray = itemArray;\r\n      specObj.isLock = form.isLock;\r\n      specObj.formulaCode = form.formulaCode;\r\n      specObj.laboratoryCode = form.laboratoryCode;\r\n      specObj.productName = form.productName;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async submitUserSpec(){\r\n      let specObj = {};\r\n      let form = this.form;\r\n      let itemArray = this.userItemArray;\r\n      if(!form.type){\r\n        this.msgError('请选择样品来源');\r\n        return;\r\n      }\r\n      specObj.formulaId = form.id;\r\n      specObj.specId = this.specId;\r\n      specObj.itemArray = itemArray;\r\n      specObj.type = form.type;\r\n      this.btnLoading = true;\r\n      try{\r\n        let res = await addSoftwareDevelopingUserFormulaSpecZxbz({specObj:JSON.stringify(specObj)});\r\n        this.msgSuccess('添加成功!');\r\n        this.btnLoading = false;\r\n        this.specOpen = false;\r\n        this.queryMaterialFormulaSpecDataList(form.id);\r\n      }catch(e){\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    async delFormulaMaterial(row){\r\n      this.formulaMaterialDatas = this.formulaMaterialDatas.filter(x => {\r\n        return x.key != row.key;\r\n      });\r\n    },\r\n    categoryChange(id){\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n      if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        let cosmeticCaseFirst = [];\r\n        if(!cosmeticCaseFirst.includes('1')){\r\n          cosmeticCaseFirst.push('1');\r\n          this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        }\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        this.codeChange(1);\r\n      }\r\n    },\r\n    categoryChangeNew(id){\r\n       let res = 1;\r\n       let form = this.form;\r\n       let ranfalei = form.ranfalei;\r\n       let qubanmeibailei = form.qubanmeibailei;\r\n       let fangshailei = form.fangshailei;\r\n       let sfa = form.sfa;\r\n       let pa = form.pa;\r\n       let yushousfa = form.yushousfa;\r\n       let xingongxiao = form.xingongxiao;\r\n       let xingongxiaocontent = form.xingongxiaocontent;\r\n       if(ranfalei.length>0\r\n      ||qubanmeibailei.length>0\r\n      ||fangshailei\r\n      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){\r\n        this.form.cosmeticClassification = '1';\r\n        this.form.cosmeticCase = '1';\r\n        res = 3;\r\n      }else{\r\n        this.form.cosmeticClassification = '';\r\n        this.form.cosmeticCase = '';\r\n        res = 2;\r\n      }\r\n      return res;\r\n    },\r\n    async codeChange(type) {\r\n      let code = []\r\n      let form = this.form\r\n      if (form.gxxc.length > 0) {\r\n        code.push(this.efficacyOptions.filter(i => form.gxxc.includes(i.id))\r\n          .sort((n1, n2) => n1.id - n2.id).map(i => i.id).join('/'))\r\n      }\r\n      if (form.zybw.length > 0) {\r\n        code.push(this.zybwOptions.filter(i => form.zybw.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.cpjx.length > 0) {\r\n        code.push(this.cpjxOptions.filter(i => form.cpjx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.syrq.length > 0) {\r\n        code.push(this.syrqOptions.filter(i => form.syrq.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      if (form.pflx.length > 0) {\r\n        code.push(this.syffOptions.filter(i => form.pflx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)\r\n          .map(i => i.id).join('/'))\r\n      }\r\n      this.form.cpfldm = code.join('~')\r\n\r\n      if (type == 1) {\r\n        let cosmeticClassification = \"\";\r\n        let cosmeticCase = \"\";\r\n        let gxxc = form.gxxc;\r\n        let gxxc1 = ['A', '3', '4', '1', '2', '5'];\r\n        let gxxc2 = ['14', '15', '19', '6', '23', '25'];\r\n\r\n        let zybw = form.zybw;\r\n        let zybw1 = ['B'];\r\n\r\n        let cpjx = form.cpjx;\r\n        let cpjx2 = ['9', '10'];\r\n\r\n        let syrq = form.syrq;\r\n        let syrq1 = ['C', '1', '2'];\r\n        let syrq2 = ['1', '2'];\r\n        let isProcess = true;\r\n        let cosmeticCaseFirst = [];\r\n        let cosmeticCaseSecond = [];\r\n        if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          if(this.arrayContainsAnother(syrq,syrq2)){\r\n            if(!cosmeticCaseFirst.includes('2')){\r\n              cosmeticCaseFirst.push('2');\r\n            }\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc2)){\r\n          if(!cosmeticCaseSecond.includes('3')){\r\n            cosmeticCaseSecond.push('3');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(cpjx, cpjx2)){\r\n          if(!cosmeticCaseSecond.includes('4')){\r\n            cosmeticCaseSecond.push('4');\r\n          }\r\n        }\r\n        if (this.arrayContainsAnother(gxxc, gxxc1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(zybw, zybw1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(syrq, syrq1)) {\r\n          cosmeticClassification = '1';\r\n        } else if (this.arrayContainsAnother(gxxc, gxxc2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else if (this.arrayContainsAnother(cpjx, cpjx2)) {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '1';\r\n        } else {\r\n          cosmeticClassification = '2';\r\n          cosmeticCase = '2';\r\n          let res = await this.categoryChangeNew(1);\r\n          if(res===1 || res ===3){\r\n            isProcess = false;\r\n          }else{\r\n            let formulaMaterialDatas = this.formulaMaterialDatas;\r\n            let isFirst = false;\r\n            let isSecond = false;\r\n            for(let item of formulaMaterialDatas){\r\n                let isNewMaterial = item.isNewMaterial;\r\n                let inicNmjyl = item.inicNmjyl;\r\n                if(isNewMaterial=='是'){\r\n                  isFirst = true;\r\n                }else if(inicNmjyl=='是'){\r\n                  isSecond = true;\r\n                }\r\n            }\r\n            if(isFirst){\r\n              cosmeticClassification = '1';\r\n              isProcess = true;\r\n            }else if(isSecond){\r\n              cosmeticClassification = '2';\r\n              cosmeticCase = '1';\r\n              isProcess = true;\r\n            }\r\n          }\r\n        }\r\n        let res1 = await this.categoryChangeNew(1);\r\n        if(res1==3){\r\n          if(!cosmeticCaseFirst.includes('1')){\r\n            cosmeticCaseFirst.push('1');\r\n          }\r\n        }\r\n        let formulaMaterialDatas = this.formulaMaterialDatas;\r\n        for(let item of formulaMaterialDatas){\r\n          let isNewMaterial = item.isNewMaterial;\r\n          let inicNmjyl = item.inicNmjyl;\r\n          let symdInfo = item.symdInfo;\r\n          if(isNewMaterial=='是'){\r\n            if(!cosmeticCaseFirst.includes('3')){\r\n              cosmeticCaseFirst.push('3');\r\n            }\r\n          }\r\n          if(inicNmjyl=='是'){\r\n            if(!cosmeticCaseSecond.includes('1')){\r\n              cosmeticCaseSecond.push('1');\r\n            }\r\n          }\r\n          if(symdInfo.indexOf('防晒剂')!=-1 ||symdInfo.indexOf('光稳定剂')!=-1 ){\r\n            if(!cosmeticCaseSecond.includes('2')){\r\n              cosmeticCaseSecond.push('2');\r\n            }\r\n          }\r\n        }\r\n        this.form.cosmeticCaseFirst = cosmeticCaseFirst;\r\n        this.form.cosmeticCaseSecond = cosmeticCaseSecond;\r\n        if(isProcess){\r\n          this.form.cosmeticClassification = cosmeticClassification;\r\n          this.form.cosmeticCase = cosmeticCase;\r\n        }\r\n\r\n      }\r\n    },\r\n    arrayContainsAnother(arr1, arr2) {\r\n      return arr1.some(item => arr2.includes(item));\r\n    },\r\n    toChoose(){\r\n       this.visible = true;\r\n    },\r\n    async selected(formulaId, laboratoryCode) {\r\n      this.form.oldFormulaCode = laboratoryCode;\r\n      this.form.copyFormulaId = formulaId;\r\n      this.visible = false;\r\n      let formulaMaterialDatas = [];\r\n      if (laboratoryCode && formulaId) {\r\n        let res = await getFormulaLabNoInfoByCode({id: formulaId});\r\n        if (res.data) {\r\n          if(res.data.dataList){\r\n            formulaMaterialDatas = res.data.dataList;\r\n          }\r\n          if(res.data.tips){\r\n            this.form.addTips = res.data.tips;\r\n          }\r\n        }\r\n        this.formulaMaterialDatas = formulaMaterialDatas;\r\n        if(formulaMaterialDatas && formulaMaterialDatas.length>0){\r\n          this.codeChange(1);\r\n        }\r\n      } else {\r\n        this.msgError('请选择配方!');\r\n      }\r\n    },\r\n    handleFormulaSpecAdd(){\r\n       this.specOpen = true;\r\n       this.userItemArray = [];\r\n       this.specId = null;\r\n       this.userItemArray = this.itemArray;\r\n    },\r\n    async handleFormulaSpecEdit(row) {\r\n      this.specOpen = true;\r\n      let dataObj = await queryMaterialFormulaSpecDataDetail({id:row.id});\r\n      let jcxmJson = dataObj.jcxmJson;\r\n      if(jcxmJson){\r\n        this.userItemArray = JSON.parse(jcxmJson);\r\n      }else{\r\n        this.userItemArray = [];\r\n      }\r\n      this.form.type = dataObj.type+'';\r\n      this.specId = dataObj.id;\r\n    },\r\n    async refreshFormulaLegalGy(type){\r\n      if(type==='1'){\r\n        await this.$confirm('是否刷新工艺数据,会清空已填数据!')\r\n      }\r\n       let id = this.form.id;\r\n       this.btnLoading = true;\r\n       let data = await queryFormulaLegalGy({id});\r\n       let gyjsData = data.data;\r\n        let gyjs = gyjsData.gyjs;\r\n        if(gyjs){\r\n          this.gyjsDataList = gyjs.map(name => ({ name }));;\r\n        }else{\r\n          this.gyjsDataList = [];\r\n        }\r\n        let zfyl = gyjsData.zfyl;\r\n        if(gyjs){\r\n          this.zfylDataList = zfyl.map(name => ({ name }));;\r\n        }else{\r\n          this.zfylDataList = [];\r\n        }\r\n       this.btnLoading = false;\r\n    },\r\n    //提交特殊原料信息\r\n    async submitTipsMaterialFormulaInfo() {\r\n      let formulaId = this.form.id;\r\n      let specMaterialDatas = this.specMaterialDatas;\r\n      this.btnLoading = true;\r\n      try {\r\n        let res = await addFormulaSpecMaterialData({id:formulaId,specMaterialDatas: JSON.stringify(specMaterialDatas)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    //提交配方使用目的\r\n    async submitSymdInfo() {\r\n      let formulaId = this.form.id;\r\n      this.btnLoading = true;\r\n      try {\r\n        let formulaSymd = [];\r\n        let compositionTableDataList = this.compositionTableDataList;\r\n        for(let item of compositionTableDataList){\r\n          formulaSymd.push({\r\n            chiName:item.chiName,\r\n            cppfSymd:item.cppfSymd,\r\n          });\r\n        }\r\n        let res = await addFormulaSymdForm({id:formulaId,formulaSymd: JSON.stringify(compositionTableDataList)});\r\n        this.msgSuccess('操作成功!');\r\n        this.btnLoading = false;\r\n      } catch (e) {\r\n        this.btnLoading = false;\r\n      }\r\n    },\r\n    getSummaries(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例(%)'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    getSummariesPFormula(param) {\r\n      const { columns, data } = param;\r\n      const sums = [];\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = '合计';\r\n          return;\r\n        }\r\n        if(!['比例'].includes(column.label)) {\r\n          sums[index] = '';\r\n          return;\r\n        }\r\n        const values = data.map(item => Number(item[column.property]));\r\n        if (!values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr);\r\n            if (!isNaN(value)) {\r\n              return this.keepDigits(prev + curr,10);\r\n            } else {\r\n              return this.keepDigits(prev,10);\r\n            }\r\n          }, 0);\r\n          sums[index] += '';\r\n        } else {\r\n          sums[index] = '';\r\n        }\r\n      });\r\n      return sums;\r\n    },\r\n    formulaMaterialBack(o) {\r\n      let status = o.row.status;\r\n      if(status){\r\n        if(status==3){\r\n          return {\r\n            background: 'orange'\r\n          }\r\n        }else if(status==4 || status==5){\r\n          return {\r\n            background: '#8cc0a8'\r\n            // background: '#9999ff'\r\n          }\r\n        }else if(status>0){\r\n          return {\r\n            background: 'red'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionTableStyle(o) {\r\n      let percert = o.row.percert;\r\n      let isColor = o.row.isColor;\r\n      if(isColor==1){\r\n        return {\r\n          color: 'red'\r\n        }\r\n      }else{\r\n        if(percert<=0.1){\r\n          return {\r\n            color: 'blue'\r\n          }\r\n        }\r\n      }\r\n    },\r\n    compositionCellTableStyle({ row, column }) {\r\n      if (column.label === '中文名称' || column.label === 'INCI 中文名') {\r\n        if (row.isTips===1) {\r\n          return \"background:#9966FF\";\r\n        }\r\n      }\r\n    },\r\n    materialDetails(row){\r\n      if(row.type==0){\r\n        this.$nextTick(async () => {\r\n          this.$refs.softwareMaterialSave.reset();\r\n          this.$refs.softwareMaterialSave.init();\r\n          let dataRow = {id:row.materialId};\r\n          this.$refs.softwareMaterialSave.handleUpdate(dataRow,'查看原料',0);\r\n          this.$refs.softwareMaterialSave.open = true;\r\n        });\r\n      }\r\n    },\r\n    selectable(row,index){\r\n      if (row.isUse == 1) {\r\n        return false\r\n      } else {\r\n        return true\r\n      }\r\n    },\r\n    async addFormulaGyjsBeianInfo(type) {\r\n      let gongyijianshuBeian = {};\r\n      gongyijianshuBeian.gyjs = this.gyjsDataList.map(item => item.name);\r\n      gongyijianshuBeian.zfyl = this.zfylDataList.map(item => item.name);\r\n      let param = {\r\n        id: this.form.id,\r\n        gongyijianshu: JSON.stringify(gongyijianshuBeian),\r\n        type\r\n      };\r\n      if(type===1){\r\n        gongyijianshuBeian.gyjs = this.gyjsBeianDataList.map(item => item.name);\r\n        gongyijianshuBeian.zfyl = this.zfylBeianDataList.map(item => item.name);\r\n        param = {\r\n          id: this.form.id,\r\n          gongyijianshuBeian: JSON.stringify(gongyijianshuBeian),\r\n          type\r\n        };\r\n      }\r\n      let res = await addFormulaGyjsBeianInfo(param);\r\n      this.msgSuccess('保存成功!');\r\n    },\r\n    async changeFun(){\r\n      this.changeTemplate(0);\r\n    },\r\n    async changeTemplate(type) {\r\n      if(this.form.currentTemplateId) {\r\n        try {\r\n          if(type===1){\r\n            await this.$confirm('是否确认带入,会清空已填数据!')\r\n          }\r\n          this.btnLoading = true\r\n          const res = await getBcpTemplate(this.form.currentTemplateId)\r\n          if (res.code === 200 && res.data && res.data.itemArray) {\r\n            let itemArray = JSON.parse(res.data.itemArray);\r\n            for(let item of itemArray){\r\n              item.standardVal = '';\r\n            }\r\n            this.itemArray = itemArray;\r\n          }\r\n          this.btnLoading = false\r\n        } catch (e) {\r\n          this.btnLoading = false\r\n        }\r\n      }\r\n    },\r\n    delItem(i) {\r\n      this.itemArray.splice(i,1)\r\n    },\r\n    selectProject() {\r\n      this.categoryOpen = true\r\n      this.xmIds = []\r\n    },\r\n    selectCategory(category) {\r\n      for (const item of category.array) {\r\n        this.selectXm(item.id)\r\n      }\r\n    },\r\n    selectXm(id) {\r\n      if(this.xmIds.includes(id)) {\r\n        this.xmIds = this.xmIds.filter(i=>i !== id)\r\n      } else {\r\n        this.xmIds.push(id)\r\n      }\r\n    },\r\n    confirmXm() {\r\n      if(this.xmIds.length) {\r\n        let arr = this.jcXmList.filter(i=> this.xmIds.includes(i.id))\r\n        const itemArray = this.itemArray\r\n        for (const a of arr) {\r\n          if(!itemArray.map(i=>i.id).includes(a.id)) {\r\n            const o = {\r\n              id: a.id,\r\n              label: a.title,\r\n              type: a.category,\r\n              serialNo: a.seq,\r\n              standard: a.standard,\r\n              frequency: a.frequency,\r\n              standardVal: a.standardVal,\r\n              yqIds: a.yqIds?a.yqIds.split(',').map(i=>Number(i)):[],\r\n            }\r\n            let methodArray = []\r\n            const dataArray = []\r\n            if(a.methodDesc) {\r\n              const methodTemplate = a.methodDesc.replace(/（/g,'(').replace(/）/g,')').replace(/\\s/g,\"\")\r\n              o.methodTemplate = methodTemplate.replace(/\\(\\{param}\\)/g,'_____')\r\n              methodArray = o.methodTemplate.split('_____')\r\n              for (let i = 0; i < methodArray.length -1; i++) {\r\n                const o = {}\r\n                o['param_ava_' + i] = ''\r\n                dataArray.push(o)\r\n              }\r\n            }\r\n            o.methodArray = methodArray\r\n            o.dataArray = dataArray\r\n            itemArray.push(o)\r\n          }\r\n        }\r\n        itemArray.sort((a,b)=>a.serialNo - b.serialNo)\r\n        this.categoryOpen = false\r\n      }\r\n    },\r\n    async handleShare(row) {\r\n      this.shareOpen = true;\r\n      let shareDeptDatas = await queryFormulaShareDeptDataList({id:row.deptId});\r\n      this.shareDeptDatas = shareDeptDatas;\r\n      let shareDeptIds = await queryFormulaShareDeptDataDetail({id:row.id});\r\n      this.shareDeptIds = shareDeptIds;\r\n      let checkRow = {\r\n        id:row.id,\r\n        deptId:row.deptId\r\n      };\r\n      this.checkRow = checkRow;\r\n     },\r\n    async submitShareFormulaInfo(){\r\n       let shareDeptIds = this.shareDeptIds;\r\n       if(shareDeptIds && shareDeptIds.length>0){\r\n          this.btnLoading = true;\r\n          let checkRow = this.checkRow;\r\n          checkRow.shareDeptIds = shareDeptIds.join(\",\");\r\n          let res = await addFormulaShareDataInfo(checkRow);\r\n          this.shareOpen = false;\r\n          this.btnLoading = false;\r\n          this.msgSuccess('操作成功');\r\n       }else {\r\n          this.msgError('请选择要分享的部门!');\r\n       }\r\n    },\r\n    handleCompositionQuery(){\r\n       let ewgColor = this.queryParams.ewgColor;\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let compositionTableDataList = this.compositionTableDataListBack;\r\n       if(ewgColor){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.dataObj.ewgColor === ewgColor);\r\n       }\r\n       if(comclusionType){\r\n         compositionTableDataList = compositionTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.compositionTableDataList = compositionTableDataList;\r\n    },\r\n    resetCompositionQuery(){\r\n      this.compositionTableDataList = this.compositionTableDataListBack;\r\n      this.queryParams.ewgColor = null;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    handleMaterialQuery(){\r\n       let comclusionType = this.queryParams.comclusionType;\r\n       let formulaTableDataList = this.formulaTableDataListBack;\r\n       if(comclusionType){\r\n         formulaTableDataList = formulaTableDataList.filter(i=>i.componentType === comclusionType);\r\n       }\r\n       this.formulaTableDataList = formulaTableDataList;\r\n    },\r\n    resetMaterialQuery(){\r\n      this.formulaTableDataList = this.formulaTableDataListBack;\r\n      this.queryParams.comclusionType = null;\r\n    },\r\n    isEditStandard(id){\r\n       let isOpr = true;\r\n       let arr = [1,2,7];\r\n       if(arr.includes(id)){\r\n         isOpr = false;\r\n       }\r\n       return isOpr;\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.select-wrapper {\r\n  .item {\r\n    height: 24px;\r\n    padding: 5px 10px;\r\n    font-size: 12px;\r\n    border: 1px solid #DCDFE6;\r\n    border-radius: 2px;\r\n    box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);\r\n    margin-top: 5px;\r\n    cursor: pointer;\r\n  }\r\n\r\n  .selected {\r\n    color: #00afff;\r\n  }\r\n}\r\n</style>\r\n"]}]}