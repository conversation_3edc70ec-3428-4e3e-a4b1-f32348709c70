<template>
  <div class="app-container">
    <!-- 添加或修改研发配方对话框 -->
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane v-for="(formula,index) in formulaTabs" :key="index" :label="formula.title"
                     :name="formula.code">
          <template v-if="formula.code==='base'">
            <fieldset>
              <legend>项目信息</legend>
              <el-row v-if="form.id">
                <el-col :span="24">
                  <el-form-item label='项目编码' prop="projectNo">
                    {{form.projectNo}}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-else>
                <el-col :span="24">
                  <el-form-item label='项目编码' prop="projectNo">
                    <el-select style="width: 500px" clearable filterable v-model="form.projectNo" @change="projectChange">
                      <el-option
                        v-for="item in projectList"
                        :key="item.projectNo"
                        :label="item.projectNo+'('+item.productName+'|'+item.customerName+')'"
                        :value="item.projectNo">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="客户名称" prop="customerName">
                    <el-input disabled="true" size="small" v-model="form.customerName" placeholder="请输入客户名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="产品名称" prop="productName">
                    <el-input :disabled="isEdit" size="small" v-model="form.productName" placeholder="请输入产品名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="品牌名称" prop="brandName">
                    <el-input :disabled="isEdit" size="small" v-model="form.brandName" placeholder="请输入品牌名称" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="系列名称" prop="seriesName">
                    <el-input :disabled="isEdit" size="small" v-model="form.seriesName" placeholder="请输入系列名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="子名称" prop="itemName">
                    <el-select clearable filterable v-model="form.itemName" @change="itemNameChange">
                      <el-option
                        v-for="item in itemNames"
                        :key="item.id"
                        :label="item.text"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="用途" prop="purpose">
                    <el-select clearable v-model="form.purpose" placeholder="请选择">
                      <el-option
                        v-for="item in purposeOptions"
                        :key="item.dictValue"
                        :label="item.dictLabel"
                        :disabled="item.isShow===0"
                        :value="item.dictValue">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
<!--              <el-row>-->
<!--                <el-col :span="8">-->
<!--                  <el-form-item label="状态" prop="formulaStatus">-->
<!--                    <el-radio-group v-model="form.formulaStatus">-->
<!--                      <el-radio :label="0">正常</el-radio>-->
<!--                      <el-radio :label="1">停用</el-radio>-->
<!--                     </el-radio-group>-->
<!--                  </el-form-item>-->
<!--                </el-col>-->
<!--              </el-row>-->
            </fieldset>
            <fieldset>
              <legend>编码信息</legend>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="配方编码" prop="formulaCode">
                    {{form.formulaCode}}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="产品分类代码" prop="cpfldm">
                    {{form.cpfldm}}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="化妆品分类" prop="cosmeticClassification">
                    <div slot="label">
                      <el-tooltip >
                        <div slot="content">
                          <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156604264.png" style="height: 500px" >
                          <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20240508/1715156703377.png" style="height: 500px" >
                        </div>
                        <i class="el-icon-question" ></i>
                      </el-tooltip>
                      化妆品分类
                    </div>
                    <el-select clearable v-model="form.cosmeticClassification">
                      <el-option
                        v-for="item in cosmeticClassificationOptions"
                        :key="item.dictValue"
                        :label="item.dictLabel"
                        :value="item.dictValue">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item v-if="form.cosmeticClassification==='1'" label="情形" prop="cosmeticCaseFirst">
                    <el-checkbox-group  v-model="form.cosmeticCaseFirst">
                      <el-checkbox
                        v-for="dict in cosmeticCaseFirstOptions"
                        :key="dict.dictValue"
                        :label="dict.dictValue">
                        {{ dict.dictLabel }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                  <el-form-item v-if="form.cosmeticClassification==='2'" label="情形" prop="cosmeticCase">
                    <el-select clearable v-model="form.cosmeticCase">
                      <el-option
                        v-for="item in caseOptions"
                        :key="item.dictValue"
                        :label="item.dictLabel"
                        :value="item.dictValue">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item v-if="form.cosmeticClassification==='2' && form.cosmeticCase==='1'" label="情形" prop="cosmeticCaseSecond">
                    <el-checkbox-group  v-model="form.cosmeticCaseSecond">
                      <el-checkbox
                        v-for="dict in cosmeticCaseSecondOptions"
                        :key="dict.dictValue"
                        :label="dict.dictValue">
                        {{ dict.dictLabel }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="实验室编码" prop="laboratoryCode">
                    <el-input v-model="form.laboratoryCode" placeholder="请输入实验室编码" />
                  </el-form-item>
                </el-col>
                <el-col v-if="form.oldFormulaCode" :span="8">
                  <el-form-item label="复制的配方编码" prop="oldFormulaCode">
                      {{form.oldFormulaCode}}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="配方类别" prop="categoryText">
                    <el-cascader
                      clearable
                      :show-all-levels="false"
                      v-model="form.categoryText"
                      :options="categoryArray"
                      :props="categoryProps"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="CIR历史用量" prop="cirText">
                    <el-cascader
                      clearable
                      :show-all-levels="false"
                      v-model="form.cirText"
                      :options="cirDataArray"
                      :props="cirDataProps"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="毒理使用量参考" prop="duliText">
                    <el-cascader
                      clearable
                      :show-all-levels="false"
                      v-model="form.duliText"
                      :options="duliDataArray"
                      :props="duliDataProps"
                    ></el-cascader>
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
            <fieldset>
              <legend>备案相关</legend>
              <el-divider content-position="left">功效宣称</el-divider>
              <el-checkbox-group v-model="form.gxxc" style="width: 1000px"  @change="codeChange(1)">
                <el-row>
                  <el-checkbox
                    style="width: 80px"
                    v-for="dict in efficacyOptions.filter(i=> i.remark == 0)"
                    :key="dict.id"
                    :label="dict.id" >
                    <i class="el-icon-s-check" v-if="dict.cssClass==='gz'" style="margin-right: 5px;color: green;"></i><i class="ali-icon ali-yiliaomeirongke" v-if="dict.cssClass==='rx'" style="margin-right: 5px;color: blue;"></i>{{dict.id}}.{{dict.title}}
                  </el-checkbox>
                </el-row>
                <el-row>
                  <el-checkbox
                    style="width: 80px"
                    v-for="dict in efficacyOptions.filter(i=> i.remark == 1)"
                    :key="dict.id"
                    :label="dict.id" >
                    <i class="el-icon-s-check" v-if="dict.cssClass==='gz'" style="margin-right: 5px;color: green;"></i><i class="ali-icon ali-yiliaomeirongke" v-if="dict.cssClass==='rx'" style="margin-right: 5px;color: blue;"></i>{{dict.id}}.{{dict.title}}
                  </el-checkbox>
                </el-row>
                <el-row>
                  <el-checkbox
                    style="width: 80px"
                    v-for="dict in efficacyOptions.filter(i=> i.remark == 3)"
                    :key="dict.id"
                    :label="dict.id" >
                    <i class="el-icon-s-check" v-if="dict.cssClass==='gz'" style="margin-right: 5px;color: green;"></i><i class="ali-icon ali-yiliaomeirongke" v-if="dict.cssClass==='rx'" style="margin-right: 5px;color: blue;"></i>{{dict.id}}.{{dict.title}}
                  </el-checkbox>
                </el-row>
              </el-checkbox-group>
              <el-divider content-position="left">其他特别宣称</el-divider>
              <el-checkbox-group v-model="form.gxxcOther">
                <el-checkbox
                  v-for="dict in otherSpecialClaimsOptions"
                  :key="dict.id"
                  :label="dict.id">
                  {{dict.id}}.{{ dict.title }}
                </el-checkbox>
              </el-checkbox-group>
              <el-divider content-position="left">申报类别(特殊化妆品填报)</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="染发类">
                    <el-checkbox-group @change="categoryChange"  v-model="form.ranfalei">
                      <el-checkbox
                        v-for="dict in rflOptions"
                        :key="dict.dictValue"
                        :label="dict.dictValue">
                        {{ dict.dictLabel }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="祛斑美白类">
                    <el-checkbox-group
                      @change="categoryChange" v-model="form.qubanmeibailei">
                      <el-checkbox
                        v-for="dict in qbmblOptions"
                        :key="dict.dictValue"
                        :label="dict.dictValue">
                        {{ dict.dictLabel }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="" prop="fangshailei">
                    <el-checkbox  @change="categoryChange" v-model="form.fangshailei">防晒类</el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="SPF值" prop="sfa">
                    <el-input  @input="categoryChange('1')" style="width: 120px" v-model="form.sfa" placeholder="SPF值"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="PA值" prop="pa">
                    <el-input  @input="categoryChange('1')" style="width: 120px" v-model="form.pa" placeholder="PA值"/>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="浴后SPF值" prop="yushousfa">
                    <el-input  @input="categoryChange('1')" style="width: 120px" v-model="form.yushousfa" placeholder="浴后SPF值"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item prop="xgx">
                    <div slot="label">
                      <el-tooltip >
                        <div slot="content">
                          1.特定宣称：宣传试用敏感皮肤，无泪配方<br/>
                          2.特定宣称：原料功效<br/>
                          3.宣称温和：无刺激<br/>
                          4.宣称量化指标（时间、统计数据等）<br/>
                          5.孕妇和哺乳期妇女适用
                        </div>
                        <i class="el-icon-question" ></i>
                      </el-tooltip>

                      <el-checkbox  @change="categoryChange" v-model="form.xingongxiao">新功效</el-checkbox>
                    </div>
                    <el-input @input="categoryChange('1')" v-model="form.xingongxiaocontent" autosize type="textarea" placeholder="请输入新功效" style="width: 800px" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider content-position="left">作用部位</el-divider>
              <el-row>
                <el-col :span="24">
                  <el-checkbox-group v-model="form.zybw" @change="codeChange(1)">
                    <el-checkbox
                      v-for="dict in zybwOptions"
                      :key="dict.id"
                      :label="dict.id">
                      {{dict.id}}.{{ dict.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
              <el-divider content-position="left">产品剂型</el-divider>
              <el-row>
                <el-col :span="24">
                  <el-checkbox-group v-model="form.cpjx" @change="codeChange(1)">
                    <el-checkbox
                      v-for="dict in cpjxOptions"
                      :key="dict.id"
                      :label="dict.id">
                      {{dict.id}}.{{ dict.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
              <el-divider content-position="left">适用人群</el-divider>
              <el-row>
                <el-col :span="24">
                  <el-checkbox-group v-model="form.syrq" @change="codeChange(1)">
                    <el-checkbox
                      v-for="dict in syrqOptions"
                      :key="dict.id"
                      :label="dict.id">
                      {{dict.id}}.{{ dict.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
              <el-divider content-position="left">使用方法</el-divider>
              <el-row>
                <el-col :span="24">
                  <el-checkbox-group v-model="form.pflx"  @change="codeChange(2)">
                    <el-checkbox
                      v-for="dict in syffOptions"
                      :key="dict.id"
                      :label="dict.id">
                      {{dict.id}}.{{ dict.title }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="8">
                  <el-form-item label="制造量" prop="weight">
                    <el-input v-model="form.weight" placeholder="请输入制造量" />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="稳定性结果" prop="stabilityresult">
                    <el-input autosize type="textarea" v-model="form.stabilityresult" placeholder="请输入稳定性结果" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="功效概述" prop="gxgs">
                    <el-input :autosize="{ minRows: 3, maxRows: 20}" type="textarea" v-model="form.gxgs" placeholder="请输入功效概述" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider  content-position="left">稳定性测试记录</el-divider>
              <el-table :data="stabilityDataList">
                <el-table-column align="center" label="稳定性编码" prop="stabilityCode"></el-table-column>
                <el-table-column align="center" label="实验室编码" prop="labNo"></el-table-column>
                <el-table-column align="center" label="稳定性状态" prop="stabilityStatus"  :formatter="stabilityStatusFormat"></el-table-column>
                <el-table-column align="center" label="结论" prop="conclusion"></el-table-column>
                <el-table-column align="center" label="样品来源" :formatter="ypFormat" prop="ypFrom"></el-table-column>
                <el-table-column align="center" label="Batch No" prop="batchNo"></el-table-column>
                <el-table-column align="center" label="配置时间" prop="ypTime"></el-table-column>
                <el-table-column align="center" label="开始时间" prop="startTime"></el-table-column>
                <el-table-column align="center" label="结束时间" prop="endTime"></el-table-column>
                <el-table-column align="center" label="创建时间" prop="createdTime"></el-table-column>
              </el-table>
              <el-divider content-position="left">关联稳定性测试记录</el-divider>
              <el-table :data="relationStabilityDataList">
                <el-table-column align="center" label="稳定性编码" prop="stabilityCode"></el-table-column>
                <el-table-column align="center" label="实验室编码" prop="labNo"></el-table-column>
                <el-table-column align="center" label="稳定性状态" prop="stabilityStatus" :formatter="stabilityStatusFormat"></el-table-column>
                <el-table-column align="center" label="结论" prop="conclusion"></el-table-column>
                <el-table-column align="center" label="样品来源" :formatter="ypFormat" prop="ypFrom"></el-table-column>
                <el-table-column align="center" label="Batch No" prop="batchNo"></el-table-column>
                <el-table-column align="center" label="配置时间" prop="ypTime"></el-table-column>
                <el-table-column align="center" label="开始时间" prop="startTime"></el-table-column>
                <el-table-column align="center" label="结束时间" prop="endTime"></el-table-column>
                <el-table-column align="center" label="创建时间" prop="createdTime"></el-table-column>
              </el-table>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注" prop="formulaRemark">
                    <el-input  autosize v-model="form.formulaRemark" type="textarea" placeholder="请输入内容" />
                  </el-form-item>
                </el-col>
              </el-row>
            </fieldset>
          </template>
          <template v-if="formula.code==='formulaMaterial'">
            <el-row v-if="!form.id">
              <el-col :span="12">
                <el-form-item label="是否复制配方">
                  <el-radio-group v-model="isCopy">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="isCopy===0">
              <el-col :span="12">
                <el-form-item label="选择原料">
                  <el-input v-model="form.materialCode"  @keyup.enter.native="queryMaterialCode" style="width:300px"  placeholder="如果需要添加原料,请输入原料编码"  />
                  &nbsp;&nbsp;<el-button type="primary" @click="queryMaterialCode">查找</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="选择配方">
                  <el-input v-model="form.formulaCodeParams"  @keyup.enter.native="queryFormulaCode" style="width:300px"  placeholder="如果需要添加配方,请输入配方编码"  />
                  &nbsp;&nbsp;<el-button type="primary" @click="queryFormulaCode">查找</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="form.id">
              <el-col :span="12">
                <el-button v-if="isGenFormula===1" type="primary" @click="genPformulaInfo">生成P配方</el-button>
              </el-col>
              <el-col :span="12" v-if="form.pFormulaCount>0">
                <el-button v-if="isBMformula===1" type="primary" @click="genNewformulaInfo">生成含B代码配方</el-button>
                <div v-if="form.formulaCodeBuff">已生成含B代码配方:<span v-html="form.formulaCodeBuff"></span></div>
              </el-col>
            </el-row>
            <el-row v-if="isCopy===1">
              <el-col :span="12">
                <el-form-item label="请输入需要复制的实验室编码">
                  <el-input v-model="form.formulaCodeParams" @focus="toChoose" style="width:350px"  placeholder="请选择要复制的实验室编码" >
                    <el-button
                      slot="append"
                      class="el-icon-zoom-in"
                      :loading="btnLoading"
                      @click="toChoose" />
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="选择原料">
                  <el-input v-model="form.materialCode"  @keyup.enter.native="queryMaterialCode" style="width:350px"  placeholder="如果需要添加原料,请输入原料编码"  />
                  &nbsp;&nbsp;<el-button type="primary" @click="queryMaterialCode">查找</el-button>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-table :data="formulaMaterialDatas" :row-style="formulaMaterialBack" show-summary :summary-method="getSummaries" @selection-change="handleFormulaMaterialSelectionChange">
                  <el-table-column v-if="form.id" align="center" type="selection" width="50" :selectable="selectable"></el-table-column>
                  <el-table-column align="center" width="60">
                    <template slot-scope="scope">
                      <i v-if="(form.isLock===1) && isLook" class="el-icon-remove-outline" @click="delFormulaMaterial(scope.row)" ></i>
                    </template>
                  </el-table-column>
                  <el-table-column label="原料代码" align="center" prop="materialCode" width="80">
                    <template slot-scope="scope">
                       <span v-if="scope.row.type==0" @click="materialDetails(scope.row)" style="color: #00afff;cursor: pointer">{{scope.row.materialCode}}</span>
                       <span v-else >{{scope.row.materialCode}}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="推荐原料" align="center" prop="relationCode" width="140"  />
                  <el-table-column label="商品名称" v-if="isShowMaterialGoodsName===1" align="center" prop="materialGoodsName" width="280"  />
                  <el-table-column label="比例(%)" width="140" align="center" prop="percentage">
                    <template slot-scope="scope">
                      <el-input type="number" v-model="scope.row.percentage" @input="limitDecimal(scope.row)"/>
                    </template>
                  </el-table-column>
                  <el-table-column label="分相" width="100" align="center"prop="subItem">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.subItem"/>
                    </template>
                  </el-table-column>
                  <el-table-column label="原料用途" width="120" align="center" prop="designatedUse">
                    <template slot-scope="scope">
                      <el-select @change="designateChange(scope.row)" v-model="scope.row.designatedUse" placeholder="请选择">
                        <el-option
                          v-for="item in useOptions"
                          :key="item.value"
                          :label="item.value"
                          :value="item.value">
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="指定代码" width="150" align="center" prop="appointCode">
                    <template slot-scope="scope">
                      <el-select clearable v-model="scope.row.appointCode" placeholder="请选择">
                        <el-option
                          v-for="item in scope.row.materialCodes"
                          :key="item.materialSubCode"
                          :label="item.materialSubCode"
                          :value="item.materialSubCode">
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="使用目的" width="200" align="center" prop="symdInfo">
                    <template slot-scope="scope">
                      <el-input style="width: 190px" v-model="scope.row.symdInfo"/>
                    </template>
                  </el-table-column>
                  <el-table-column label="周期(天)" align="center" prop="orderingCycle" />
                  <el-table-column label="原料认证" align="center" prop="certification">
                      <template slot-scope="scopoe">
                        {{selectDictLabel(certificationOptions,scopoe.row.certification)}}
                      </template>
                  </el-table-column>
                  <el-table-column label="进口国家" align="center" prop="importCountry" />
                  <el-table-column label="备注" align="center" width="300" prop="remark">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isRelation==1"><el-input v-model="scope.row.remark"/></span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="12">
                <el-form-item label="配方图片">
                  <imageUpload v-model="form.formulaImage" :limit="3"></imageUpload>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="配方搭建思路">
                  <el-input type="textarea" autosize v-model="form.formulaConstructionIdeas" placeholder="请输入配方搭建思路" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input type="textarea" autosize v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="!((form.isLock===1 || form.isLock===2) && isLook)">
              <el-col :span="24">
                 <div style="text-align: center">
                   <el-button type="primary" @click="submitUploadForm" :loading="btnLoading" >确定修改</el-button>
                 </div>
              </el-col>
            </el-row>
          </template>
          <template v-if="formula.code==='formulaFile'">
            <el-row>
              <el-col :span="24">
                <el-form-item label="检测标准" prop="introFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.introFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="稳定性测试结果" prop="wendingxingResult">
                  <el-select v-model="form.wendingxingResult" placeholder="请选择" clearable size="small">
                    <el-option v-for="item in wdxOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="稳定性测试备注" prop="wendingxingRemark">
                   <el-input type="textarea" v-model="form.wendingxingRemark"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="稳定性检测报告" prop="wendingxingFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.wendingxingFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="工艺" prop="gongyiFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.gongyiFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="相容性结果" prop="xiangrongxingResult">
                  <el-select v-model="form.xiangrongxingResult" placeholder="请选择" clearable size="small">
                    <el-option v-for="item in wdxOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="相容性备注" prop="xiangrongxingRemark">
                  <el-input type="textarea" v-model="form.xiangrongxingRemark"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="相容性测试报告" prop="xiangrongxingFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.xiangrongxingFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="防腐挑战结果" prop="weishenwuResult">
                  <div slot="label">
                    <el-tooltip>
                      <i class="el-icon-question" ></i>
                      <div slot="content">
                        <p>高风险(没有测试过相关防腐体系)</p>
                        <p>中风险(测试进行中,有一定数据量)</p>
                        <p>低风险(有相似配方的测试数据,且测试通过)</p>
                        <p>无风险(测试通过)</p>
                        <p>测试没通过(不能释放)</p>
                      </div>
                    </el-tooltip>
                    防腐挑战结果
                  </div>
                  <el-select v-model="form.weishenwuResult" placeholder="请选择" clearable size="small">
                    <el-option v-for="item in ffjtxfxpgOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="防腐挑战备注" prop="weishenwuRemark">
                  <el-input type="textarea" v-model="form.weishenwuRemark"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="防腐实验报告" prop="weishenwuFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.weishenwuFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="消费者测试报告" prop="xiaofeizheFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.xiaofeizheFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="其它" prop="qitaFile">
                  <SoftwareFileUpload :op-type="1" v-model="form.qitaFile"/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="!((form.isLock===1 || form.isLock===2) && isLook)">
              <el-col :span="24">
                <div style="text-align: center">
                  <el-button type="primary" @click="submitUploadFileForm" :loading="btnLoading" >确定修改</el-button>
                </div>
              </el-col>
            </el-row>
          </template>
          <template v-if="formula.code==='recipeChangeHistory'">
            <el-table :data="recipeChangeHistoryData">
              <el-table-column align="center" prop="modifiedTime" label="更改日期" />
              <el-table-column align="center" prop="materialCode" label="原料代码" />
              <el-table-column align="center" prop="percentageOld" label="原始比例(%)" />
              <el-table-column align="center" prop="percentageNew" label="新比例(%)" />
              <el-table-column align="center" prop="remark" label="更改内容" />
              <el-table-column align="center" prop="inciName" label="INCI中文名" />
              <el-table-column align="center" prop="operator" label="编辑人" />
            </el-table>
          </template>
          <template v-if="formula.code==='spec'">
            <el-divider content-position="left">检测标准</el-divider>
            <table class="base-table"   style="margin-top: 0 !important;">
              <tr>
                <td style="width:70px" rowspan="5">执行标准</td>
              </tr>
              <tr>
                <td>标准名称</td>
                <td>
                  <el-select clearable filterable v-model="form.execNumberId" @change="zxbzChange">
                    <el-option
                      v-for="item in zxbzList"
                      :key="item.id"
                      :label="item.zxbzh+'('+item.bzmc+')'"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </td>
                <td>执行标准号</td>
                <td id="zxbzhTd">{{zxbzDetail.zxbzh}}({{zxbzDetail.bzmc}})</td>
                <td>定义</td>
                <td id="dingyiTd">{{zxbzDetail.dingyi}}</td>
              </tr>
              <tr>
                <td>外观及pH</td>
                <td  id="waiguanjiphTd">{{zxbzDetail.waiguan}}{{zxbzDetail.ph}}</td>
                <td >耐热</td>
                <td  id="naireTd">{{zxbzDetail.naire}}</td>
                <td >耐寒</td>
                <td  id="naihanTd">{{zxbzDetail.naihan}}</td>
              </tr>
              <tr>
                <td>归属公司</td>
                <td  id="gsgsTd">
                  {{selectDictLabel(ownershopCompanyOptions,zxbzDetail.ownershopCompany)}}
                </td>
                <td >状态</td>
                <td  id="zhtTd">
                  {{selectDictLabel(statusOptions,zxbzDetail.status)}}
                </td>
                <td >标准性质</td>
                <td  id="bzxzTd">
                  {{selectDictLabel(bzxzOptions,zxbzDetail.bzxz)}}
                </td>
              </tr>
              <!--                <tr>-->
              <!--                  <td colspan="7">-->
              <!--                    <el-button @loading="btnLoading" @click="submitSpec" type="primary">提 交</el-button>-->
              <!--                  </td>-->
              <!--                </tr>-->
            </table>
            <el-divider content-position="left">检测项目</el-divider>
            <el-tabs v-model="currentTab" >
              <el-tab-pane key="base" label="常规检测" name="base" >
                <div class="cell-wrapper" >
                  <div class="label">选择模板:</div>
                  <div class="content">
                    <el-select @change="changeFun" v-model="form.currentTemplateId" filterable size="mini" >
                      <el-option
                        v-for="item in templateList"
                        :key="item.id"
                        :value="item.id"
                        :label="item.name" />
                    </el-select>
                    <el-button icon="el-icon-search" size="mini" @click="changeTemplate(1)" :loading="btnLoading" />
                  </div>
                </div>
                <div class="table-wrapper" style="text-align: center;" v-if="itemArray.length">
                  <table class="base-table small-table">
                    <tr>
                      <th style="width: 50px">
                        <i @click="selectProject" class="el-icon-circle-plus" />
                      </th>
                      <th style="width: 120px">类型</th>
                      <th style="width: 120px">检测项目</th>
                      <th style="width: 320px">检验标准</th>
                      <th style="width: 320px">检验方法</th>
                      <th style="width: 320px">标准值</th>
                      <th style="width: 120px">检验频次</th>
                    </tr>
                    <tr v-for="(item,i) in itemArray" :key="item.id" >
                      <td><i v-if="(form.isLock===1 && isLook)" class="el-icon-remove-outline" @click="delItem(i)"/></td>
                      <td>{{item.type}}</td>
                      <td>{{item.label}}</td>
                      <td>{{item.standard}}</td>
                      <td>{{item.methodTemplate}}</td>
                      <td>
                        <span v-if="isEditStandard(item.id)"><el-input v-model="item.standardVal" /> </span>
                        <span v-else>
                      <span v-if="(form.isLock===1 && isLook)"><el-input v-model="item.standardVal" /></span>
                      <span v-else>{{item.standardVal}}</span>
                    </span>
                      </td>
                      <td>{{item.frequency}}</td>
                    </tr>
                  </table>
                  <br /><br />
                  <div style="text-align: center;">
                    <el-button @loading="btnLoading" @click="submitSpec" type="primary">提 交</el-button>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane key="special" label="微生物检测" name="microbe" >
                <el-row>
                  <el-col :span="8" >
                    <el-form-item label="样品物性" prop="wxId">
                      <div style="cursor: pointer" @click="showWx" >
                        <span v-if="form.wxId" >{{wxLabel(form.wxId)}}</span>
                        <i v-else style="color: #00afff;">请选择</i>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" >
                    <el-form-item label="检验依据" prop="inspectBasis">
                      <el-input v-model="form.inspectBasis" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-tab-pane>

              <el-form-item label="备注" style="margin-top: 20px" prop="microbeRemark">
                <el-input v-model="form.microbeRemark" type="textarea" autosize />
              </el-form-item>


            </el-tabs>

            <br /><br />
            <el-divider content-position="left">检测记录</el-divider>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button
                  type="primary"
                  plain
                  icon="el-icon-plus"
                  size="mini"
                  v-if="form.isLock===1 && isLook"
                  @click="handleFormulaSpecAdd"
                >新增</el-button>
              </el-col>
            </el-row>
            <el-table v-loading="loading" :data="softwareFormulaSpecList" style="overflow: scroll">
              <el-table-column label="样品来源" align="center" prop="type">
                <template slot-scope="scope">
                  {{selectDictLabel(yplyOptions,scope.row.type)}}
                </template>
              </el-table-column>
              <el-table-column label="测试样批号" width="130" align="center" prop="ceshiyangpihao" />
              <el-table-column label="外观" align="center" prop="waiguan" />
              <el-table-column label="颜色" align="center" prop="yanse" />
              <el-table-column label="气味" align="center" prop="qiwei" />
              <el-table-column label="PH" align="center" prop="ph" />
              <el-table-column label="耐热" align="center" prop="naire" />
              <el-table-column label="耐寒" align="center" prop="naihan" />
              <el-table-column label="创建时间" align="center" prop="createdTime" width="180" />
              <el-table-column label="操作人" align="center" prop="operator" />
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template v-slot="scope">
                  <el-tooltip content="修改" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-edit"
                      v-if="form.isLock===1 && isLook"
                      @click="handleFormulaSpecEdit(scope.row)"
                    />
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template v-if="formula.code==='workmanship'">
            <div style="width: 100%">
              <div style="width: 50%;float: left">
                <div style="text-align: center;">
                  <span>工艺简述</span>
                  <el-tooltip content="更新工艺简述" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      v-if="form.isLock===1 && isLook"
                      icon="el-icon-refresh"
                      :loading="btnLoading"
                      @click="refreshFormulaLegalGy('1')"
                    />
                  </el-tooltip>
                </div>
                <el-divider content-position="left">工艺简述</el-divider>
                <table class="base-table">
                  <tr v-for="data in gyjsDataList">
                    <el-input v-model="data.name" type="textarea"/>
                  </tr>
                </table>
                <el-divider content-position="left">组分原料备注</el-divider>
                <table class="base-table">
                  <tr v-for="data in zfylDataList">
                    <el-input v-model="data.name" type="textarea"/>
                  </tr>
                </table>
                <br /><br />
                <div style="text-align:center">
                  <el-button v-if="(form.isLock===1 || form.isLock===2) && isLook" @loading="btnLoading" @click="addFormulaGyjsBeianInfo(0)" type="primary">保存更改</el-button>
                </div>
              </div>
              <div style="width: 50%;float: left">
                <div style="text-align: center;">
                  <span>备案工艺</span>
                  <el-tooltip content="复制工艺简述" placement="top">
                    <el-button
                      size="mini"
                      type="text"
                      v-if="form.isLock===1 && isLook"
                      icon="el-icon-refresh"
                      :loading="btnLoading"
                      @click="copyGongyi()"
                    >
                      复制工艺简述
                    </el-button>
                  </el-tooltip>
                </div>
                <el-divider content-position="left">工艺简述</el-divider>
                <table class="base-table">
                  <tr v-for="data in gyjsBeianDataList">
                    <el-input v-model="data.name" type="textarea"/>
                  </tr>
                </table>
                <el-divider content-position="left">组分原料备注</el-divider>
                <table class="base-table">
                  <tr v-for="data in zfylBeianDataList">
                    <el-input v-model="data.name" type="textarea"/>
                  </tr>
                </table>
                <br /><br />
                <div style="text-align:center">
                  <el-button v-if="(form.isLock===1 || form.isLock===2) && isLook" @loading="btnLoading" @click="addFormulaGyjsBeianInfo(1)" type="primary">保存更改</el-button>
                </div>
              </div>
            </div>
          </template>
          <template v-if="formula.code==='compositionTable'">
            <table class="base-table">
              <tr>
                <td align="right">客户名称:</td>
                <td style="width: 200px">{{form.customerName }}</td>
                <td align="right">品牌名称:</td>
                <td style="width: 200px">{{form.brandName }}</td>
                <td align="right">产品名称:</td>
                <td style="width: 200px">{{form.productName }}</td>
              </tr>
              <tr>
                <td align="right">实验室编码:</td>
                <td style="width: 300px">{{form.laboratoryCode }}</td>
                <td align="right">配方编码:</td>
                <td style="width: 200px">{{form.formulaCode }}</td>
                <td align="right">执行标准号:</td>
                <td style="width: 200px">{{form.execNumber }}</td>
              </tr>
              <tr>
                <td colspan="6" style="text-align: left">Material contained in 100 grams</td>
              </tr>
            </table>
            <br />
            <el-table border :data="compositionTableDataList"  :cell-style="compositionCellTableStyle" :row-style="compositionTableStyle">
              <el-table-column label="序号" type="index" align="center"  width="50" />
              <el-table-column label="INCI 中文名" prop="chiName" align="center"  width="200">
                <template slot-scope="scope">
                    <span :style="scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''">
                       <el-tooltip v-if="scope.row.isTips===1">
                          <div slot="content">
                            <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png" >
                          </div>
                          <i class="el-icon-question" ></i>
                        </el-tooltip>
                       <span v-html="scope.row.chiNameNew"></span>
                    </span>
                </template>
              </el-table-column>
              <el-table-column label="INCI NAME" prop="engName" align="center"  width="200">
                <template slot-scope="scope">
                  <span :style="scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''">{{scope.row.engName}}</span>
                </template>
              </el-table-column>
              <el-table-column label="实际成分含量" prop="percert" align="center"  width="150">
                <template slot="header">
                  实际成分含量({{totalPercent}}%)
                </template>
                <template slot-scope="scope">
                  <span :style="scope.row.isGt==1?'color:red':''">{{scope.row.percert}}</span>
                </template>
              </el-table-column>
              <el-table-column label="国家简化版安评要求" align="center">
                <el-table-column label="驻留类产品最高历史使用量" prop="zllzglssyl" align="center"  width="180" />
                <el-table-column label="淋洗类产品最高历史使用量" prop="lxlzglssyl" align="center"  width="180" />
              </el-table-column>
              <el-table-column label="法规要求" align="center">
                <el-table-column label="备案(明细)" prop="bzmx" align="center"  width="180" />
                <el-table-column label="化妆品使用时的最大允许浓度" prop="maxAllowConcentration" align="center"  width="180" />
                <el-table-column label="最高历史使用量(%)" prop="gcfZglssy" align="center"  width="180" />
                <el-table-column label="适用及(或)使用范围" prop="scopeOfApplication" align="center"  width="220"  />
                <el-table-column label="其他限制和要求" prop="otherLimit" align="center"  width="220" />
                <el-table-column label="标签上必须标印的 使用条件和注意事项" prop="labelCondition" align="center"  width="180" />
              </el-table-column>
              <el-table-column label="CIR历史使用量" align="center">
                <el-table-column label="CIR" align="center" prop="cirData"   width="110" />
                <el-table-column label="驻留型" align="center" prop="zlxData"   width="110" />
                <el-table-column label="淋洗型" align="center" prop="lxxData"   width="110" />
                <el-table-column label="婴儿产品/婴儿护理" align="center" prop="babyData"   width="110" />
                <el-table-column label="Totals" align="center" prop="totalsData"   width="110" />
              </el-table-column>
              <el-table-column label="毒理/供应商使用量参考"  align="center">
                <el-table-column label="欧标" prop="ouBiao" align="center"  width="110" />
                <el-table-column label="日标" prop="riBiao" align="center"  width="110" />
              </el-table-column>
            </el-table>
            <br />
            <table class="base-table">
              <tr>
                <td align="right">全成分标识 0.1%（w/w）以上：</td>
                <td align="left">
                   <span v-html="gtNumStr"></span>
                </td>
              </tr>
              <tr>
                <td align="right">全成分标识 0.1%（w/w）以下：</td>
                <td align="left">
                  <span v-html="ltNumStr"></span>
                </td>
              </tr>
            </table>
          </template>
          <template v-if="formula.code==='productSafetyAssessmentReport'">
            <el-row>
              <el-col :span="24">
                <el-form-item label="产品评估结论" prop="aqpgjl">
                  <el-input type="textarea" autosize v-model="form.aqpgjl" />
                </el-form-item>
              </el-col>
            </el-row>
            <div v-if="form.isLock===1  && isLook && compositionTableDataList.length>0" style="text-align:center;margin-top:10px">
              <el-button v-hasPermi="['software:softwareDevelopingFormula:editSymd']" @loading="btnLoading" @click="submitSymdInfo" type="primary">提 交</el-button>
            </div>
            <br />
            <el-table height="65vh" border :data="compositionTableDataList"  :cell-style="compositionCellTableStyle"  :row-style="compositionTableStyle">
              <el-table-column label="序号" type="index" align="center"  width="50" fixed />
              <el-table-column label="中文名称" prop="chiName" align="center"  width="200" fixed>
                <template slot-scope="scope">
                        <span :style="scope.row.isTips==1?'background-color:#9966FF':''">
                           <el-tooltip v-if="scope.row.isTips===1">
                            <div slot="content">
                              <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png" >
                            </div>
                            <i class="el-icon-question" ></i>
                          </el-tooltip>
                         <span v-html="scope.row.chiNameNew"></span>
                        </span>
                </template>
              </el-table-column>
              <el-table-column label="INCI名称/英文民称" prop="engName" align="center" fixed  width="200" />
              <el-table-column label="含量(%)" prop="percert" align="center"  width="120" fixed>
                <template slot-scope="scope">
                  <span :style="scope.row.isGt==1?';color:red':''">{{scope.row.percert}}</span>
                </template>
              </el-table-column>
              <el-table-column label="使用目的" prop="cppfSymd" align="center"  width="220" fixed>
                <template slot-scope="scope">
                  <el-input type="textarea"  :autosize="{ minRows: 3, maxRows: 6}" v-model="scope.row.cppfSymd" />
                </template>
              </el-table-column>
              <el-table-column label="在《已使用原料目录》中的序号" prop="cppfSyylxh" align="center"  width="220" />
              <el-table-column label="产品配方表备注" prop="cppfRemark" align="center"  width="200" />
              <el-table-column label="《化妆品安全技术规范》要求" prop="gcfJsgf" align="center"  width="200" />
              <el-table-column label="权威机构评估结论" prop="gcfQwjgpgjl" align="center"  width="200" />
              <el-table-column label="本企业原料历史使用量(%)" prop="gcfBqyysyl" align="center"  width="200" />
              <el-table-column label="最高历史使用量(%)" prop="gcfZglssy" align="center"  width="200" />
              <el-table-column label="评估结论" prop="gcfPgjl" align="center"  width="200">
                <template slot-scope="scope">
                  <span :style="scope.row.isColor==1?'color:red':''">{{scope.row.gcfPgjl}}</span>
                </template>
              </el-table-column>
              <el-table-column label="参考文献序号" prop="gcfCkwx" align="center"  width="200" />
              <el-table-column label="参考文献内容" prop="gcfCkwxnr" align="center"  width="200" />
              <el-table-column label="参考文献下载链接" prop="gcfCkwxlj" align="center"  width="200" />
              <el-table-column label="可能含有的风险物质" prop="aqxKnhyfxw" align="center"  width="200" />
              <el-table-column label="风险物质备注" prop="aqxRemark" align="center"  width="200" />
            </el-table>
          </template>
          <template v-if="formula.code==='conclusionOfSafetyAssessment'">
            <el-collapse v-model="activeNames">
              <el-collapse-item title="判断结果定义" name="1">
                <div class="tip">
                  <span style="color: green;font-size: 22px" class="el-icon-success" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>
                  <span style="color: blue;font-size: 22px" class="el-icon-success" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>
                  <span style="color: orange;font-size: 22px" class="el-icon-question" />:不满足以上两个条件;含有安全级别为I/Z<br/>
                  <span style="color: red;font-size: 22px" class="el-icon-error" />:含有禁用成分;含有安全级别为U的成分<br/>
                  <span style="color: red;font-size: 22px" class="el-icon-question" />:含有安全级别为UNS的成分<br/>
                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>
                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>
                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>
                  <span>I:可用数据不足以支持安全性<br/></span>
                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>
                  <span>U:该成分用于化妆品不安全<br/></span>
                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>
                  <span>无:无权威机构数据</span>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-tabs v-model="conclusionOfSafetyAssessmentName">
              <el-tab-pane label="成分纬度" name="first">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="判断结果" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.comclusionType">
                        <el-radio :label="1"><span style="color: green;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="4"><span style="color: green;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="2"><span style="color: blue;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="5"><span style="color: blue;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="3"><span style="color: orange;font-size: 22px" class="el-icon-question" /></el-radio>
                        <el-radio :label="6"><span style="color: red;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="7"><span style="color: red;font-size: 22px" class="el-icon-question" /></el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="EWG" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.ewgColor">
                        <el-radio label="green">绿色</el-radio>
                        <el-radio label="orange">橙色</el-radio>
                        <el-radio label="red">红色</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCompositionQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetCompositionQuery">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                   <span v-if="form.isCiji>0" style="color:red;">配方需测刺激性</span>
                   <span v-if="form.isZhimin>0" style="color:red;"><span v-if="form.isCiji>0">、</span><span v-else>配方需测</span>致敏性</span>
                   <span v-if="form.zmCjTips" style="color:red;">。({{form.zmCjTips}})</span>
                </el-row>
                <div v-if="form.isLock===1  && isLook && compositionTableDataList.length>0" style="text-align:center;margin-top:10px">
                  <el-button v-hasPermi="['software:softwareDevelopingFormula:editSymd']" @loading="btnLoading" @click="submitSymdInfo" type="primary">更新使用目的</el-button>
                </div>
                <br />
                <el-table  height="65vh" border :data="compositionTableDataList"   :cell-style="compositionCellTableStyle"  :row-style="compositionTableStyle">
                  <el-table-column label="序号" type="index" align="center"  width="50" fixed />
                  <el-table-column label="中文名称" prop="chiName" align="center"  width="200" fixed>
                    <template slot-scope="scope">
                        <span :style="scope.row.isTips==1?'background-color:#9966FF':''">
                           <el-tooltip v-if="scope.row.isTips===1">
                            <div slot="content">
                              <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png" >
                            </div>
                            <i class="el-icon-question" ></i>
                          </el-tooltip>
                          <span v-html="scope.row.chiNameNew"></span>
                        </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="成分含量" prop="percert" align="center"  width="100" fixed />
                  <el-table-column label="使用目的" prop="cppfSymd" align="center"  width="220" fixed>
                    <template slot-scope="scope">
                      <el-input type="textarea"  :autosize="{ minRows: 3, maxRows: 6}" v-model="scope.row.cppfSymd" />
                    </template>
                  </el-table-column>
                  <el-table-column label="新原料" align="center">
                    <el-table-column label="是否新原料" width="120" prop="isNewMaterial" align="center">
                      <template slot-scope="scope">
                        <i v-if="scope.row.dataObj.isNewMaterial  === '是'" class="ali-icon ali-weixuanzhongyuanquan" style="font-size: 20px"></i>
                      </template>
                    </el-table-column>
                    <el-table-column label="驻留类" prop="dataObj.zl" align="center" />
                    <el-table-column label="淋洗类" prop="dataObj.lx" align="center" />
                    <el-table-column label="total" prop="dataObj.newTotal" align="center" />
                    <el-table-column label="适用及(或)使用范围" :show-overflow-tooltip="true"  width="220" prop="dataObj.newRange" align="center" />
                  </el-table-column>
                  <el-table-column label="化妆品安全卫生规范" align="center">
                    <el-table-column label="符合卫生规范" width="120" prop="bzmx" align="center">
                      <template slot-scope="scope">
                        <i v-if="scope.row.dataObj.bzmx  === '是'" class="ali-icon ali-weixuanzhongyuanquan" style="font-size: 20px"></i>
                        <span v-else-if="scope.row.dataObj.bzmx  === '否'"></span>
                      </template>
                    </el-table-column>
                    <el-table-column label="备案明细" width="220" prop="bzmx" align="center">
                      <template slot-scope="scope">
                       <span v-if="scope.row.dataObj.bzmx  === '是'">
                         {{scope.row.dataObj.bzmxDetail}}
                      </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="dataObj.syjsyfw"  width="260" :show-overflow-tooltip="true"  label="适用及(或)使用范围" align="center" />
                    <el-table-column label="最大允许浓度" width="120"  prop="dataObj.zdsynd" align="center" />
                    <el-table-column prop="otherLimit"  width="260"  :show-overflow-tooltip="true" label="其他限制和要求" align="center" />
                  </el-table-column>
                  <el-table-column label="权威机构" align="center">
                    <el-table-column label="CIR驻留类" width="140" prop="zlxData" align="center" />
                    <el-table-column label="CIR淋洗类" width="140" prop="lxxData" align="center" />
                    <el-table-column label="CIR total" prop="totalsData" align="center" />
                  </el-table-column>

                  <el-table-column label="毒理(欧标)" align="center">
                    <el-table-column label="驻留类" prop="duliOuBiaoLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="duliOuBiaoRinseOff" align="center" />
                    <el-table-column label="total" prop="duliOuBiaoTotals" align="center" />
                  </el-table-column>
                  <el-table-column label="毒理(日标)" align="center">
                    <el-table-column label="驻留类" prop="duliRiBiaoLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="duliRiBiaoRinseOff" align="center" />
                    <el-table-column label="total" prop="duliRiBiaoTotals" align="center" />
                  </el-table-column>
                  <el-table-column label="药食同源" align="center">
                    <el-table-column label="类型" prop="ystyType" align="center">
                      <template slot-scope="scope">
                        {{selectDictLabel(typeOptions,scope.row.dataObj.ystyType)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="使用限制" prop="" align="center">
                      <template slot-scope="scope">
                        {{scope.row.dataObj.ystyMax}}
                      </template>
                    </el-table-column>
                  </el-table-column>

                  <el-table-column label="CIR安全级别" prop="finding" align="center">
                    <template slot-scope="scope">
                      {{scope.row.dataObj.finding}}
                    </template>
                  </el-table-column>

                  <el-table-column label="IFRA" prop="isIfra" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isEssence===1 && scope.row.isIfra===0">+</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="已上市产品原料使用信息2025/《国际化妆品安全评估数据索引》收录的部分原料使用信息" width="800" align="center">
                    <el-table-column align="center" label="驻留" width="400">
                      <template slot-scope="scope">
                        <el-divider v-if="scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0" content-position="left">已上市产品原料使用信息</el-divider>
                        <table v-if="scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='驻留').length>0" class="base-table">
                          <tr>
                            <td v-for="(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='驻留')">
                              {{zjy.parts}}
                            </td>
                          </tr>
                          <tr>
                            <td v-for="(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='驻留')">
                              {{zjy.usage}}
                            </td>
                          </tr>
                        </table>
                        <el-divider v-if="scope.row.aqpgDatas && scope.row.aqpgDatas.length>0" content-position="left">《国际化妆品安全评估数据索引》</el-divider>
                        <table v-if="scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='驻留').length>0" class="base-table">
                          <tr>
                            <td v-for="(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='驻留')">
                              {{zjy.parts}}
                            </td>
                          </tr>
                          <tr>
                            <td v-for="(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='驻留')">
                              {{zjy.usage}}
                            </td>
                          </tr>
                        </table>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="淋洗" width="400">
                      <template slot-scope="scope">
                        <el-divider v-if="scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0" content-position="left">已上市产品原料使用信息</el-divider>
                        <table v-if="scope.row.zjyDatas && scope.row.zjyDatas.filter(i=>i.method==='淋洗').length>0" class="base-table">
                          <tr>
                            <td v-for="(zjy,index) in scope.row.zjyDatas.filter(i=>i.method==='淋洗')">
                              {{zjy.parts}}
                            </td>
                          </tr>
                          <tr>
                            <td v-for="(zjy,index)  in scope.row.zjyDatas.filter(i=>i.method==='淋洗')">
                              {{zjy.usage}}
                            </td>
                          </tr>
                        </table>
                        <el-divider v-if="scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0" content-position="left">《国际化妆品安全评估数据索引》</el-divider>
                        <table v-if="scope.row.aqpgDatas && scope.row.aqpgDatas.filter(i=>i.method==='淋洗').length>0" class="base-table">
                          <tr>
                            <td v-for="(zjy,index) in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')">
                              {{zjy.parts}}
                            </td>
                          </tr>
                          <tr>
                            <td v-for="(zjy,index)  in scope.row.aqpgDatas.filter(i=>i.method==='淋洗')">
                              {{zjy.usage}}
                            </td>
                          </tr>
                        </table>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="total(中检院)" prop="maxTotals" />
                    <el-table-column align="center" label="total(国际化妆品)" prop="aqpgMaxTotals" />
                  </el-table-column>

                  <el-table-column label="公司内部" align="center">
                    <el-table-column label="驻留类" prop="companyLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="companyRinseOff" align="center" />
                    <el-table-column label="total" prop="companyTotals" align="center" />
                  </el-table-column>

                  <el-table-column label="供应商" align="center">
                    <el-table-column label="驻留类" prop="supplierLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="supplierRinseOff" align="center" />
                    <el-table-column label="total" prop="supplierTotals" align="center" />
                  </el-table-column>

                  <el-table-column label="判断结果" prop="componentType" align="center">
                    <template slot-scope="scope">
                      <span style="color: green;font-size: 22px" v-if="scope.row.componentType===1" class="el-icon-success" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===2" class="el-icon-success" />
                      <span style="color: orange;font-size: 22px" v-else-if="scope.row.componentType===3" class="el-icon-question" />
                      <span style="color: green;font-size: 22px" v-else-if="scope.row.componentType===4" class="el-icon-error" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===5" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===6" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===7" class="el-icon-question" />
                    </template>
                  </el-table-column>
                  <el-table-column label="权威评估结论" :show-overflow-tooltip="true"  width="400" prop="dataObj.conclusion" align="center" />
                  <el-table-column label="评估结论"  width="400" prop="finalConclusion" align="center" />
<!--                  <el-table-column label="已使用化妆品原料目录(2021年版)"   align="center">-->
<!--                    <el-table-column label="驻留类" prop="dataObj.zllzglssyl" align="center" />-->
<!--                    <el-table-column label="淋洗类" prop="dataObj.lxlzglssyl" align="center" />-->
<!--                  </el-table-column>-->
                  <el-table-column label="EWG" align="center">
                    <el-table-column label="EWG分值" prop="ewgScore" align="center">
                      <template slot-scope="scope">
                        <div v-if="scope.row.dataObj.isSplit==0" style="height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;" :style="{backgroundColor:scope.row.dataObj.ewgColor}">{{scope.row.dataObj.ewgScore}}</div>
                        <div v-else-if="scope.row.dataObj.isSplit==1" style="height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;" :style="{backgroundColor:scope.row.dataObj.ewgColor}">{{scope.row.dataObj.ewgScore}}</div>
                      </template>
                    </el-table-column>
                    <el-table-column label="致癌性" prop="dataObj.cancer" align="center" />
                    <el-table-column label="过敏/免疫毒性" width="160" prop="dataObj.allergies" align="center" />
                    <el-table-column label="发育/生殖毒性" width="160" prop="dataObj.developmental" align="center" />
                    <el-table-column label="使用限制" prop="dataObj.useRestrictions" align="center" />
                  </el-table-column>
                  <el-table-column label="美修参考"  align="center">
                    <el-table-column label="活性成分" prop="activity" align="center">
                      <template slot-scope="scope">
                        <img v-if="scope.row.dataObj.activity" :src="require('@/assets/images/formula/huoxing.png')" width="40" height="50" >
                      </template>
                    </el-table-column>
                    <el-table-column label="致痘风险" prop="pox" align="center">
                      <template slot-scope="scope">
                        <img v-if="scope.row.dataObj.pox==1" :src="require('@/assets/images/formula/di.png')" width="40" height="40" >
                        <img v-else-if="scope.row.dataObj.pox==2" :src="require('@/assets/images/formula/zhong.png')" width="40" height="40" >
                        <img v-else-if="scope.row.dataObj.pox==3" :src="require('@/assets/images/formula/gao.png')" width="40" height="40" >
                      </template>
                    </el-table-column>
                    <el-table-column label="孕妇慎用" prop="dataObj.yfSy" align="center" />
                    <el-table-column label="安全风险" prop="dataObj.risk" align="center" />
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="原料纬度" name="second">
                <el-row>
                  <el-col :span="16">
                    <el-form-item label="判断结果" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.comclusionType">
                        <el-radio :label="1"><span style="color: green;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="4"><span style="color: green;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="2"><span style="color: blue;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="5"><span style="color: blue;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="3"><span style="color: orange;font-size: 22px" class="el-icon-question" /></el-radio>
                        <el-radio :label="6"><span style="color: red;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="7"><span style="color: red;font-size: 22px" class="el-icon-question" /></el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleMaterialQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetMaterialQuery">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-table   height="65vh" border :data="formulaTableDataList"  :cell-style="compositionCellTableStyle"  :row-style="compositionTableStyle">
                  <el-table-column label="序号" type="index" align="center"  width="50" fixed   />
                  <el-table-column label="原料代码" prop="materialCode" align="center"  width="80" fixed />
                  <el-table-column label="比例" width="120px" prop="percentage" align="center" fixed />
                  <el-table-column label="是否新原料" align="center">
                    <el-table-column label="是否新原料" width="120" prop="isNewMaterial" align="center">
                      <template slot-scope="scope">
                        <i v-if="scope.row.isNewMaterial  === '是'" class="ali-icon ali-weixuanzhongyuanquan" style="font-size: 20px"></i>
                      </template>
                    </el-table-column>
                    <el-table-column label="驻留类" prop="cirzlx" align="center" />
                    <el-table-column label="淋洗类" prop="cirlxx" align="center" />
                    <el-table-column label="total" prop="zdsynd" align="center" />
                  </el-table-column>
                  <el-table-column label="安全技术规范" align="center">
                    <el-table-column label="规范" prop="bzmx" align="center">
                      <template slot-scope="scope">
                        <i v-if="scope.row.bzmx  === '是'" class="ali-icon ali-weixuanzhongyuanquan" style="font-size: 20px"></i>
                        <span v-else-if="scope.row.bzmx  === '否'"></span>
                        <span v-else>
                        {{scope.row.bzmx}}
                      </span>
                      </template>
                    </el-table-column>
                    <el-table-column label="最大允许浓度" width="120" prop="zdsynd" align="center" />
                  </el-table-column>

                  <el-table-column label="权威机构" align="center">
                    <el-table-column label="CIR驻留类" prop="zlxData_" align="center" />
                    <el-table-column label="CIR淋洗类" prop="lxxData_" align="center" />
                    <el-table-column label="CIR total" prop="totalsData_" align="center" />
                  </el-table-column>

                  <el-table-column label="毒理(欧标)" align="center">
                    <el-table-column label="驻留类" prop="duliOuBiaoLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="duliOuBiaoRinseOff" align="center" />
                    <el-table-column label="total" prop="duliOuBiaoTotals" align="center" />
                  </el-table-column>
                  <el-table-column label="毒理(日标)" align="center">
                    <el-table-column label="驻留类" prop="duliRiBiaoLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="duliRiBiaoRinseOff" align="center" />
                    <el-table-column label="total" prop="duliRiBiaoTotals" align="center" />
                  </el-table-column>
                  <el-table-column label="IFRA" prop="isIfra" align="center">
                    <template slot-scope="scope">
                      <span v-if="scope.row.isEssence===1 && scope.row.isIfra===0">+</span>
                    </template>
                  </el-table-column>

                  <el-table-column label="公司内部" align="center">
                    <el-table-column label="驻留类" prop="companyLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="companyRinseOff" align="center" />
                    <el-table-column label="total" prop="companyTotals" align="center" />
                  </el-table-column>

                  <el-table-column label="供应商" align="center">
                    <el-table-column label="驻留类" prop="supplierLeaveOn" align="center" />
                    <el-table-column label="淋洗类" prop="supplierRinseOff" align="center" />
                    <el-table-column label="total" prop="supplierTotals" align="center" />
                  </el-table-column>

                  <el-table-column label="判断结果" prop="componentType" align="center">
                    <template slot-scope="scope">
                      <span style="color: green;font-size: 22px" v-if="scope.row.componentType===1" class="el-icon-success" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===2" class="el-icon-success" />
                      <span style="color: orange;font-size: 22px" v-else-if="scope.row.componentType===3" class="el-icon-question" />
                      <span style="color: green;font-size: 22px" v-else-if="scope.row.componentType===4" class="el-icon-error" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===5" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===6" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===7" class="el-icon-question" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </template>
          <template v-if="formula.code==='conclusionOfSafetyAssessmentSimple'">
            <el-collapse v-model="activeNames">
              <el-collapse-item title="判断结果定义" name="1">
                <div class="tip">
                  <span style="color: green;font-size: 22px" class="el-icon-success" />:全部成分安全数据满足如下条件之一：1）国妆原备字成分  2）符合卫生规范：限用成分， 准用防晒剂、防腐剂、着色剂  3）有CIR历史用量数据  4）有CIR毒理数据  5）香精成分有IFRA数据  6）中检院发布的已上市产品原料使用信息2025;<br/>
                  <span style="color: blue;font-size: 22px" class="el-icon-success" />:其中某一或多个成分不满足绿色圆点勾，但是有供应商数据或满足公司内部3年历史数据;<br/>
                  <span style="color: orange;font-size: 22px" class="el-icon-question" />:不满足以上两个条件;含有安全级别为I/Z<br/>
                  <span style="color: red;font-size: 22px" class="el-icon-error" />:含有禁用成分;含有安全级别为U的成分<br/>
                  <span style="color: red;font-size: 22px" class="el-icon-question" />:含有安全级别为UNS的成分<br/>
                  <h4>备注:安全级别依据 Quick Reference Table Cosmetic Ingredient Review - September, 2022；</h4>
                  <span>S:在目前的使用和浓度实践中是安全的<br/></span>
                  <span>SQ:在化妆品中使用是安全的，有限制条件<br/></span>
                  <span>I:可用数据不足以支持安全性<br/></span>
                  <span>Z:可用数据不足以支持安全，但该成分也无历史使用量<br/></span>
                  <span>U:该成分用于化妆品不安全<br/></span>
                  <span>UNS:数据不足且不支持在化妆品中使用的成分<br/></span>
                  <span>无:无权威机构数据</span>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-tabs v-model="conclusionOfSafetyAssessmentName">
              <el-tab-pane label="成分纬度" name="first">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="判断结果" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.comclusionType">
                        <el-radio :label="1"><span style="color: green;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="4"><span style="color: green;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="2"><span style="color: blue;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="5"><span style="color: blue;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="3"><span style="color: orange;font-size: 22px" class="el-icon-question" /></el-radio>
                        <el-radio :label="6"><span style="color: red;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="7"><span style="color: red;font-size: 22px" class="el-icon-question" /></el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="EWG" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.ewgColor">
                        <el-radio label="green">绿色</el-radio>
                        <el-radio label="orange">橙色</el-radio>
                        <el-radio label="red">红色</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCompositionQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetCompositionQuery">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-table  height="65vh" border :data="compositionTableDataList"  :cell-style="compositionCellTableStyle"  :row-style="compositionTableStyle">
                  <el-table-column label="序号" type="index" align="center"  width="50" fixed />
                  <el-table-column label="中文名称" prop="chiName" align="center"  width="200" fixed>
                    <template slot-scope="scope">
                        <span :style="scope.row.isTips==1?'background-color:#9966FF':''">
                           <el-tooltip v-if="scope.row.isTips===1">
                            <div slot="content">
                              <img src="https://enow.oss-cn-beijing.aliyuncs.com/images/20220811/1660180976376.png" >
                            </div>
                            <i class="el-icon-question" ></i>
                          </el-tooltip>
                          <span v-html="scope.row.chiNameNew"></span>
                        </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="成分含量" prop="percert" align="center"  width="100" fixed />
                  <el-table-column label="判断结果" prop="componentType" align="center">
                    <template slot-scope="scope">
                      <span style="color: green;font-size: 22px" v-if="scope.row.componentType===1" class="el-icon-success" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===2" class="el-icon-success" />
                      <span style="color: orange;font-size: 22px" v-else-if="scope.row.componentType===3" class="el-icon-question" />
                      <span style="color: green;font-size: 22px" v-else-if="scope.row.componentType===4" class="el-icon-error" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===5" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===6" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===7" class="el-icon-question" />
                    </template>
                  </el-table-column>
                  <el-table-column label="EWG成分安全分" prop="ewgScore" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.row.dataObj.isSplit==0" style="height: 20px;width:20px;border-radius: 20px;color: white;text-align: center;" :style="{backgroundColor:scope.row.dataObj.ewgColor}">{{scope.row.dataObj.ewgScore}}</div>
                      <div v-else-if="scope.row.dataObj.isSplit==1" style="height: 20px;width:50px;border-radius: 10px;color: white;text-align: center;" :style="{backgroundColor:scope.row.dataObj.ewgColor}">{{scope.row.dataObj.ewgScore}}</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="美修参考"  align="center">
                    <el-table-column label="活性成分" prop="activity" align="center">
                      <template slot-scope="scope">
                        <img v-if="scope.row.dataObj.activity" :src="require('@/assets/images/formula/huoxing.png')" width="40" height="50" >
                      </template>
                    </el-table-column>
                    <el-table-column label="致痘风险" prop="pox" align="center">
                      <template slot-scope="scope">
                        <img v-if="scope.row.dataObj.pox==1" :src="require('@/assets/images/formula/di.png')" width="40" height="40" >
                        <img v-else-if="scope.row.dataObj.pox==2" :src="require('@/assets/images/formula/zhong.png')" width="40" height="40" >
                        <img v-else-if="scope.row.dataObj.pox==3" :src="require('@/assets/images/formula/gao.png')" width="40" height="40" >
                      </template>
                    </el-table-column>
                    <el-table-column label="孕妇慎用" prop="dataObj.yfSy" align="center" />
                    <el-table-column label="安全风险" prop="dataObj.risk" align="center" />
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="原料纬度" name="second">
                <el-row>
                  <el-col :span="16">
                    <el-form-item label="判断结果" label-width="120" prop="materialCode">
                      <el-radio-group v-model="queryParams.comclusionType">
                        <el-radio :label="1"><span style="color: green;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="4"><span style="color: green;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="2"><span style="color: blue;font-size: 22px" class="el-icon-success" /></el-radio>
                        <el-radio :label="5"><span style="color: blue;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="3"><span style="color: orange;font-size: 22px" class="el-icon-question" /></el-radio>
                        <el-radio :label="6"><span style="color: red;font-size: 22px" class="el-icon-error" /></el-radio>
                        <el-radio :label="7"><span style="color: red;font-size: 22px" class="el-icon-question" /></el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item>
                      <el-button type="primary" icon="el-icon-search" size="mini" @click="handleMaterialQuery">搜索</el-button>
                      <el-button icon="el-icon-refresh" size="mini" @click="resetMaterialQuery">重置</el-button>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-table   height="65vh" border :data="formulaTableDataList"  :cell-style="compositionCellTableStyle"  :row-style="compositionTableStyle">
                  <el-table-column label="序号" type="index" align="center"  width="50" fixed   />
                  <el-table-column label="原料代码" prop="materialCode" align="center"  width="80" fixed />
                  <el-table-column label="比例" width="120px" prop="percentage" align="center" fixed />

                  <el-table-column label="判断结果" prop="componentType" align="center">
                    <template slot-scope="scope">
                      <span style="color: green;font-size: 22px" v-if="scope.row.componentType===1" class="el-icon-success" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===2" class="el-icon-success" />
                      <span style="color: orange;font-size: 22px" v-else-if="scope.row.componentType===3" class="el-icon-question" />
                      <span style="color: green;font-size: 22px" v-else-if="scope.row.componentType===4" class="el-icon-error" />
                      <span style="color: blue;font-size: 22px" v-else-if="scope.row.componentType===5" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===6" class="el-icon-error" />
                      <span style="color: red;font-size: 22px" v-else-if="scope.row.componentType===7" class="el-icon-question" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </template>
          <template v-if="formula.code==='pFomula'">
            <el-row v-for="(item, index) in pFormulaMapData">
              <el-col :span="8">
                <el-form-item label="配方编码">
                  {{item.formulaCode}}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="实验室编码">
                  {{item.laboratoryCode}}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-button  v-hasPermi="['software:softwareDevelopingFormula:genBMaterialInfo']" type="primary" @click="generBMaterialInfo(item.id)">生成B代码</el-button>
                <span v-if="item.materialCode">该配方已生成了B代码,代码为:{{item.materialCode }}</span>
              </el-col>
              <el-table :data="item.materialDatas" show-summary :summary-method="getSummariesPFormula">
                <el-table-column label="序号" type="index" align="center"  width="50" />
                <el-table-column label="原料代码" prop="materialCode" align="center"  width="200" />
                <el-table-column v-hasPermi="['software:softwareDevelopingFormula:lookMaterialGoodsName']" label="商品名称" prop="materialChiName" align="center"  width="400" />
                <el-table-column label="比例" prop="percentage" align="center"  width="200" />
              </el-table>
              <br /><br />
            </el-row>
          </template>
          <template v-if="formula.code==='formulaTable'">
            <table class="base-table">
              <tr>
                <td align="right">客户名称:</td>
                <td style="width: 200px">{{form.customerName }}</td>
                <td align="right">品牌名称:</td>
                <td style="width: 200px">{{form.brandName }}</td>
                <td align="right">产品名称:</td>
                <td style="width: 200px">{{form.productName }}</td>
              </tr>
              <tr>
                <td align="right">实验室编码:</td>
                <td style="width: 300px">{{form.laboratoryCode }}</td>
                <td align="right">配方编码:</td>
                <td style="width: 200px">{{form.formulaCode }}</td>
                <td align="right">执行标准号:</td>
                <td style="width: 200px">{{form.execNumber }}</td>
              </tr>
              <tr>
                <td colspan="6" style="text-align: left">Material contained in 100 grams</td>
              </tr>
            </table>
            <br />
            <el-table border :data="formulaTableDataList">
              <el-table-column label="序号" type="index" align="center"  width="50" />
              <el-table-column label="分相" prop="subItem" align="center"  width="60" />
              <el-table-column label="原料代码" prop="materialCode" align="center" width="80" />
              <el-table-column label="INCI 中文名" width="350px" prop="subItem" align="center">
                <template slot-scope="scope">
                  <div  :style="scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''" v-for="(item,index) in scope.row.inicDataList">{{item.inciName}}</div>
                </template>
              </el-table-column>
              <el-table-column label="INCI NAME" width="350px" prop="subItem" align="center">
                <template slot-scope="scope">
                  <div :style="scope.row.status==1?'text-decoration:line-through;color:red':scope.row.status==2?'text-decoration:line-through;color:orange':''" v-for="(item,index) in scope.row.inicDataList">{{item.inciNameEng}}</div>
                </template>
              </el-table-column>
              <el-table-column label="比例" width="120px" prop="percentage" align="center">
                <template slot="header">
                  比例({{totalPercentVal}}%)
                </template>
              </el-table-column>
              <el-table-column label="复配百分比(%)"  width="140px" prop="subItem" align="center">
                <template slot-scope="scope">
                  <div  v-for="(item,index) in scope.row.inicDataList">{{item.proportionSingle}}</div>
                </template>
              </el-table-column>
              <el-table-column label="实际成分含量(%)"  width="150px" prop="subItem" align="center">
                <template slot="header">
                  实际成分含量({{sjTotalPercet}}%)
                </template>
                <template slot-scope="scope">
                  <div  v-for="(item,index) in scope.row.inicDataList">{{item.proportion}}</div>
                </template>
              </el-table-column>
              <el-table-column label="最低纯度(%)"  width="140px" prop="subItem" align="center">
                <template slot-scope="scope">
                  <div  v-for="(item,index) in scope.row.inicDataList">{{item.sjProportionSingle}}</div>
                </template>
              </el-table-column>
              <el-table-column label="实际成分含量"  width="140px" prop="sjProportion" align="center">
                <template slot="header" slot-scope="scope">
                  <el-tooltip content="按原料最低比例计算" >
                    <i class="el-icon-question" ></i>
                  </el-tooltip>
                  实际成分含量
                </template>
                <template slot-scope="scope">
                  <div v-for="(item,index) in scope.row.inicDataList">{{item.sjProportion}}</div>
                </template>
              </el-table-column>
              <el-table-column label="CIR历史使用量" align="center">
                <el-table-column label="CIR" align="center" prop="cirData"   width="110" />
                <el-table-column label="驻留型" align="center" prop="zlxData"   width="110" />
                <el-table-column label="淋洗型" align="center" prop="lxxData"   width="110" />
                <el-table-column label="婴儿产品/婴儿护理" align="center" prop="babyData"   width="110" />
                <el-table-column label="Totals" align="center" prop="totalsData"   width="110" />
              </el-table-column>
              <el-table-column label="毒理/供应商使用量参考"  align="center">
                <el-table-column label="欧标" prop="ouBiao" align="center"  width="110" />
                <el-table-column label="日标" prop="riBiao" align="center"  width="110" />
                <el-table-column label="供应商数据" prop="supplieData" align="center"  width="110" />
              </el-table-column>
              <el-table-column label="周期(天)" prop="orderingcycle" align="center" />
              <el-table-column label="备注"  width="220px" prop="inicRemark" align="center">
                <template slot-scope="scope">
                  <span v-html="scope.row.inicRemark"></span>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template v-if="formula.code==='specMaterial'">
            <el-table :data="specMaterialDatas">
              <el-table-column label="序号" type="index" align="center"  width="100" />
              <el-table-column label="原料代码" prop="materialCode" align="center"  width="150" />
              <el-table-column label="原始INCI 中文名" prop="inciName" align="center" />
              <el-table-column label="替换为" prop="replaceInciName" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.row.inciName==='二氧化钛' || scope.row.inciName==='CI 77891'">
                    <el-select v-model="scope.row.replaceInciName" clearable placeholder="请选择">
                      <el-option
                        v-for="item in specMaterialDatas1"
                        :key="item.value"
                        :label="item.value"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div v-else-if="scope.row.inciName==='氧化锌' || scope.row.inciName==='CI 77947'">
                    <el-select v-model="scope.row.replaceInciName" clearable placeholder="请选择">
                      <el-option
                        v-for="item in specMaterialDatas2"
                        :key="item.value"
                        :label="item.value"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div v-if="form.isLock===1  && isLook && specMaterialDatas.length>0" style="text-align:center;margin-top:10px">
              <el-button @loading="btnLoading" @click="submitTipsMaterialFormulaInfo" type="primary">提 交</el-button>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </el-form>

    <div v-if="(form.isLock===1 || form.isLock===2) && isLook && activeName!=='workmanship'" slot="footer" class="dialog-footer" style="margin-top:10px">
      <el-button @loading="btnLoading" v-if="form.isDraft===1" type="primary" @click="submitForm(1)">保存草稿</el-button>
      <el-button @loading="btnLoading" type="primary" @click="submitForm(0)">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>

    <el-dialog title="选择配方" :visible.sync="visible" width="1200px" top="5vh" append-to-body>
       <selectFormula @selected="selected"/>
    </el-dialog>

    <el-dialog title="选择项目" :visible.sync="categoryOpen" width="1200px" :close-on-click-modal="false" append-to-body>
      <template v-for="category in categoryList" >
        <el-divider content-position="left">
          {{category.category}}
          <el-tooltip content="全选/反选" >
            <i class="el-icon-circle-check" @click="selectCategory(category)" />
          </el-tooltip>
        </el-divider>

        <el-row :gutter="20" class="select-wrapper" >
          <el-col v-for="item in category.array" :key="item.id" :span="4" style="display: flex;align-items: center" >
            <div class="item" @click="selectXm(item.id)" :class="xmIds.includes(item.id) ? 'selected':''">
              {{item.title}}
            </div>
          </el-col>
        </el-row>
      </template>
      <div class="dialog-footer" style="margin-top: 10px" >
        <el-button type="primary" size="mini" @click="confirmXm" >确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="添加SPEC" :visible.sync="specOpen" width="1200px" top="5vh" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row v-if="specId">
          <el-col :span="24">
            <el-form-item label="样品来源" prop="type">
              {{selectDictLabel(yplyOptions,form.type)}}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="24">
            <el-form-item label="样品来源" prop="type">
               <el-select v-model="form.type" clearable>
                 <el-option
                   v-for="dict in yplyOptions"
                   :key="dict.dictValue"
                   :label="dict.dictLabel"
                   :value="dict.dictValue"
                 ></el-option>
               </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <table class="base-table small-table">
            <tr>
              <th style="width: 120px">类型</th>
              <th style="width: 120px">检测项目</th>
              <th style="width: 320px">检验标准</th>
              <th style="width: 320px">标准值</th>
              <th style="width: 120px">检验频次</th>
            </tr>
            <tr v-for="(item,i) in userItemArray" :key="item.id" >
              <td>{{item.type}}</td>
              <td>{{item.label}}</td>
              <td>{{item.standard}}</td>
              <td><el-input v-model="item.standardVal" /></td>
              <td>{{item.frequency}}</td>
            </tr>
          </table>
        </el-row>
         <div style="text-align: center;margin-top:10px">
          <el-button @loading="btnLoading" @click="submitUserSpec" type="primary">提 交</el-button>
         </div>
       </el-form>
    </el-dialog>

    <el-dialog title="分享配方" :visible.sync="shareOpen" width="800px" top="5vh" append-to-body>
      <el-checkbox-group v-model="shareDeptIds">
        < <el-checkbox
          v-for="dict in shareDeptDatas"
        :key="dict.id"
        :label="dict.id">
          {{ dict.name }}
      </el-checkbox>
      </el-checkbox-group>
      <div style="margin-top: 10px;text-align: center">
        <el-button  @loading="btnLoading" @click="submitShareFormulaInfo" type="primary">提 交</el-button>
      </div>
    </el-dialog>


    <el-dialog title="样品物性" :visible.sync="wxOpen" width="1200px" :close-on-click-modal="false" append-to-body>
      <el-tree
        :data="wxTree"
        @node-click="handleNodeClick"
        node-key="id"
        default-expand-all
      />
    </el-dialog>

    <SoftwareMaterialSave ref="softwareMaterialSave" />
  </div>
</template>

<script>
import {
  listSoftwareDevelopingFormula,
  getSoftwareDevelopingFormula,
  delSoftwareDevelopingFormula,
  addSoftwareDevelopingFormula,
  updateSoftwareDevelopingFormula,
  exportSoftwareDevelopingFormula,
  querySoftwareDevelopingFormulaDict,
  queryFormulaClassifyData,
  getFormulaCategoryTree,
  queryFormulaMaterialData,
  getFormulaLabNoInfoByCode,
  queryFormualMaterialRecipeChangeHistoryData,
  queryFormulaZxbzDataList,
  queryFormulaZxbzDataDetail,
  getSoftwareDevelopingFormulaDetail,
  addSoftwareDevelopingFormulaSpecZxbz,
  queryCirHistoryData,
  getCirDataTree,
  queryDuliHistoryData,
  getDuliDataTree,
  addFormulaSpecMaterialData,
  addFormulaSymdForm,
  generatePFormulaInfo,
  generateBMaterialInfo,
  generateNewformulaInfo,
  addSoftwareDevelopingUserFormulaSpecZxbz,
  queryMaterialFormulaSpecDataList,
  queryMaterialFormulaSpecDataDetail,
  addFormulaGyjsBeianInfo,
  queryFormulaLegalGy,
  queryFormulaShareDeptDataList,
  queryFormulaShareDeptDataDetail,
  addFormulaShareDataInfo,
  queryLookFormulaTabs,
  updateSoftwareDevelopingFormulaImg,
  queryFormulaStabilityRecordDataList,
  updateSoftwareDevelopingFormulaFileImg, queryFormulaAppointMaterialDataList
} from "@/api/software/softwareDevelopingFormula";
import {formualList, formualProjectDetail} from "@/api/project/project";
import {getFormulaInfoByCode, getRawMaterialInfoByCode} from "@/api/software/softwareMaterial";
import {isArray, isString} from "@/utils/validate";
import selectFormula from "@/views/software/formula/components/selectFormula";
import {allBcpTemplate, getBcpTemplate} from "@/api/qc/bcpTemplate";
import {allJcxm} from "@/api/qc/jcxm";
import {checkPermi} from "@/utils/permission";
import SoftwareMaterialSave from "@/views/software/softwareMaterial/save";
import { Base64 } from 'js-base64'
import {selectDictLabel} from "../../../utils/ruoyi";
import {allTreeData} from "@/api/system/treeData";

export default {
  name: "SoftwareDevelopingFormulaSave",
  components:{selectFormula,SoftwareMaterialSave},
  props: {
    readonly: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      wxOpen:false,
      activeName: "base",
      currentTab: 'base',
      wxOptions: [],
      conclusionOfSafetyAssessmentName: "first",
      loading: false,
      visible: false,
      btnLoading: false,
      categoryOpen: false,
      exportLoading: false,
      fullscreenFlag: false,
      shareOpen: false,
      specOpen: false,
      totalPercentVal:0,
      sjTotalPercet:0,
      isShowMaterialGoodsName:0,
      isGenFormula:0,
      isBMformula:0,
      activeNames: [],
      ids: [],
      statusOptions: [],
      shareDeptDatas: [],
      shareDeptIds: [],
      specId: null,
      ownershopCompanyOptions: [],
      bzxzOptions: [],
      projectList: [],
      typeOptions: [],
      categoryList: [],
      zxbzList: [],
      xmIds: [],
      recipeChangeHistoryData: [],
      formulaTableDataList: [],
      formulaTableDataListBack: [],
      compositionTableDataList: [],
      compositionTableDataListBack: [],
      softwareFormulaSpecList: [],
      specMaterialDatas: [],
      itemArray: [],
      userItemArray: [],
      gtNumStr: '',
      ltNumStr: '',
      itemNames: [],
      zxbzDetail:{},
      gyjsData:{},
      gyjsDataList:[],
      zfylDataList:[],
      gyjsBeianDataList:[],
      zfylBeianDataList:[],
      specObj:{},
      single: true,
      showSearch: false,
      total: 0,
      isCopy: 0,
      softwareDevelopingFormulaList: [],
      efficacyOptions: [],
      otherSpecialClaimsOptions: [],
      formulaMaterialDatas: [],
      chooseFormulaMaterialDatas: [],
      pFormulaMapData: [],
      zybwOptions: [],
      templateList: [],
      cpjxOptions: [],
      syrqOptions: [],
      syffOptions: [],
      wxTree: [],
      purposeOptions: [],
      categoryArray: [],
      jcXmList: [],
      stabilityDataList: [],
      relationStabilityDataList: [],
      jlOptions: [
        10,50,100,500,1000,
      ],
      mjOptions: [
        10,50,100,
      ],
      plOptions: [],
      checkRow: {
        id:null,
        deptId:null
      },
      categoryProps: {
        label: 'categoryName',
        value: 'categoryId'
      },
      cirDataArray: [],
      isLook:false,
      cirDataProps: {
        label: 'label',
        value: 'id'
      },
      duliDataArray: [],
      cosmeticCaseFirstOptions: [],
      cosmeticCaseSecondOptions: [],
      duliDataProps: {
        label: 'zhType',
        value: 'id'
      },
      formulaTabs:[{
        title:'基础信息',
        code:'base'
      },{
        title:'配方页面',
        code:'formulaMaterial'
      },{
        title:'附件',
        code:'formulaFile'
      }],
      title: "",
      open: false,
      isEdit: true,
      certificationOptions:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        laboratoryCode: null,
        formulaName: null,
        ewgColor: null,
        comclusionType:null
      },
      form: {},
      rules: {
        projectNo: [
          { required: true, message: '请选择项目编码'},
        ],
        customerName: [
          { required: true, message: '客户名称不允许为空'},
        ],
        productName: [
          { required: true, message: '产品名称不允许为空'},
        ],
        cirText: [
          { required: true, message: '请选择CIR历史用量'},
        ],
        duliText: [
          { required: true, message: '请选择毒理使用量参考'},
        ],
        laboratoryCode: [
          { required: true, message: '实验室编码不允许为空'},
        ],
        categoryText: [
          { required: true, message: '配方类别不允许为空'},
        ],
        pflx: [
          { required: true, message: '使用方法不允许为空'},
        ],
      },
      rflOptions:[
        {
          dictValue:'3',
          dictLabel:'染发类'
        }, {
          dictValue:'2',
          dictLabel:'防脱类'
        }, {
          dictValue:'1',
          dictLabel:'烫发类'
        }
      ],
      yplyOptions:[
        {
          dictValue:'0',
          dictLabel:'检测方法'
        }, {
          dictValue:'2',
          dictLabel:'实验室小样'
        }, {
          dictValue:'3',
          dictLabel:'中试样品'
        }, {
          dictValue:'4',
          dictLabel:'大货样品'
        }
      ],
      qiOptions:[
        {
          dictValue:'有香味',
         }, {
          dictValue:'有原料特征性气味',
         }, {
          dictValue:'无味',
         }
      ],
      qbmblOptions:[
        {
          dictValue:'1',
          dictLabel:'祛斑美白类'
        }, {
          dictValue:'2',
          dictLabel:'祛斑美白类(仅具物理遮盖作用)'
        }
      ],
      cosmeticClassificationOptions:[
        {
          dictValue:'1',
          dictLabel:'第一类化妆品'
        }, {
          dictValue:'2',
          dictLabel:'第二类化妆品'
        }
      ],
      caseOptions:[
        {
          dictValue:'1',
          dictLabel:'情形一'
        }, {
          dictValue:'2',
          dictLabel:'情形二'
        }
      ],
      useOptions:[
        {
          value:'/',
        },  {
          value:'指定',
        }
      ],
      specMaterialDatas1:[
        {
          value:'二氧化钛'
        },
        {
          value:'CI 77891'
        }
      ],
      specMaterialDatas2:[
        {
          value:'氧化锌'
        },
        {
          value:'CI 77947'
        }
      ],
      ffjtxfxpgOptions: [{
        dictValue:'0',
        dictLabel:'高风险'
      },{
        dictValue:'1',
        dictLabel:'中风险'
      },{
        dictValue:'2',
        dictLabel:'低风险'
      },{
        dictValue:'3',
        dictLabel:'无风险'
      },{
        dictValue:'4',
        dictLabel:'测试没通过'
      }],
      wdxOptions: [{
        dictValue:'0',
        dictLabel:'进行中'
      },{
        dictValue:'1',
        dictLabel:'测试通过'
      },{
        dictValue:'2',
        dictLabel:'测试失败'
      },{
        dictValue:'3',
        dictLabel:'条件接受'
      }],
      ypFromOptions: [
        {label: '实验室',value: 0},
        {label: '中试',value: 1},
        {label: '生产',value: 2},
        {label: '生技复样',value: 3},
      ],
      stabilityStatusOptions: [
        {label: '进行中',value: 0},
        {label: '测试通过',value: 1},
        {label: '测试失败',value: 2},
        {label: '条件接受',value: 3},
      ],
    };
  },
  async created() {
    this.wxTree = this.toTree(this.wxOptions, 0)

    //使用目的
    let certificationRes = await this.getDicts("SOFTWARE_CERTIFICATION")
    this.certificationOptions = certificationRes.data

    this.getDicts("qc_jypl").then(response => {
      this.plOptions = response.data
    })
  },
  watch: {
    "$route.query.params": {
      immediate: true,
      handler() {
        let params = this.$route.query.params;
        if(params) {
          let query = Base64.decode(Base64.decode(params));
          if(query){
            query = JSON.parse(query);
            this.reset();
            this.init(1);
            this.handleUpdate(query.id,query.shareType===1?true:false);
            this.btnLoading = true;
            this.title = query.shareType===1?'修改配方':'查看配方';
          }
        }else{
          this.reset();
          this.init(2);
          this.queryProjectList();
          this.handleAdd();
          this.isLook = true;
        }
      },
    }
  },
  methods: {
    async showWx() {
      this.wxOpen = true
    },
    selectDictLabel,
    async designateChange(row){
       let designatedUse = row.designatedUse;
       if('指定'===designatedUse){  //指定原料
          let materialId = row.materialId;
          let res = await queryFormulaAppointMaterialDataList({id:materialId});
          row.materialCodes = res;
       }else{
          row.appointCode = '';
          row.materialCodes = [];
       }
    },
    async queryProjectList(){
      let projectList  = await formualList();
      this.projectList = projectList;
    },
    async init(type){
      if(checkPermi(['software:softwareDevelopingFormula:lookMaterialGoodsName'])) {
        this.isShowMaterialGoodsName = 1;
      }else{
        this.isShowMaterialGoodsName = 0;
      }
      if(checkPermi(['software:softwareDevelopingFormula:genPformulaInfo'])) {
        this.isGenFormula = 1;
      }else{
        this.isGenFormula = 0;
      }
      if(checkPermi(['software:softwareDevelopingFormula:genBMFormulaInfo'])) {
        this.isBMformula = 1;
      }else{
        this.isBMformula = 0;
      }
      this.getDicts("ZXBZ_STATUS").then(response => {
        this.statusOptions = response.data;
      })
      this.getDicts("OWNERSHOP_COMPANY").then(response => {
        this.ownershopCompanyOptions = response.data;
      })
      this.getDicts("BZXZ").then(response => {
        this.bzxzOptions = response.data;
      })
      this.getDicts("rd_ysty_type").then(response => {
        this.typeOptions = response.data;
      })
      this.getDicts("SOFTWARE_FORMULA_CASE1").then(response => {
        this.cosmeticCaseFirstOptions = response.data;
      })
      this.getDicts("SOFTWARE_FORMULA_CASE2").then(response => {
        this.cosmeticCaseSecondOptions = response.data;
      })
      const categorySet = new Set()
      const jcXmList = await allJcxm({type:1})
      this.jcXmList = jcXmList
      for (const item of jcXmList) {
        categorySet.add(item.category)
      }
      const categoryList = []
      for (const category of categorySet) {
        categoryList.push({
          category,
          array: jcXmList.filter(i=>i.category === category),
        })
      }
      this.categoryList = categoryList.filter(i=>i.category !== '微生物')
      this.templateList = await allBcpTemplate();
      //配方使用用途
      let purposeRes =  await this.getDicts("SOFTWARE_FORMULA_PURPOSE")
      this.purposeOptions = purposeRes.data
      //获取字典数据
      let dictObj = await querySoftwareDevelopingFormulaDict();
      this.efficacyOptions = dictObj.GXXC_DATA_LIST;
      this.otherSpecialClaimsOptions = dictObj.GXXC_DATA_LIST_OTHER;
      this.zybwOptions = dictObj.ZYBW_DATA_LIST;
      this.cpjxOptions = dictObj.CPJX_DATA_LIST;
      this.syrqOptions = dictObj.SYRQ_DATA_LIST;
      this.syffOptions = dictObj.PFLX_DATA_LIST;
      let categoryAllArray = await queryFormulaClassifyData()
      if(type==2){
        let datas = [22,23,24,25,26,27,43,50,61];
        categoryAllArray = categoryAllArray.filter(i=>!datas.includes(i.categoryId));
      }
      this.categoryArray = await getFormulaCategoryTree(categoryAllArray)
      let zxbzList = await queryFormulaZxbzDataList();
      this.zxbzList = zxbzList;
      //获取cir历史使用量
      let cirDataAllArray = await queryCirHistoryData();
      this.cirDataArray = await getCirDataTree(cirDataAllArray);
      //获取毒理/供应商使用量参考
      let duliDataAllArray = await queryDuliHistoryData();
      this.duliDataArray = await getDuliDataTree(duliDataAllArray);
    },
    async itemNameChange(itemName){
      let itemNames = this.itemNames;
      let arr = itemNames.filter(i=> i.id === itemName)
      let itemNameText = '';
      if(arr && arr[0]) {
        itemNameText = arr[0].text;
      }
       this.form.itemNameText = itemNameText;
     },
    async projectChange(projectNo) {
      //获取项目详情
      let projectDetail = await formualProjectDetail({projectNo});
      let isEdit = true;
      if(projectNo.indexOf("P")!=-1 || projectNo=='210002089' ||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){
         isEdit = false;
      }
      this.isEdit = isEdit;
      this.form.itemName = '';
      this.form.itemNameText = '';
      this.itemNames = [];
      if(projectDetail!=null && projectDetail.id){
        this.form.customerName = projectDetail.customerName;
        this.form.productName = projectDetail.productName;
        this.form.brandName = projectDetail.brandName;
        this.form.seriesName = projectDetail.seriesName;
        let itemNames = projectDetail.itemNames;
        if(itemNames){
          itemNames = JSON.parse(itemNames);
          this.itemNames = itemNames;
        }
       }else{
        this.form.customerName = '';
        this.form.productName = '';
        this.form.brandName = '';
        this.form.seriesName = '';
       }
    },
    async zxbzChange(id){
      let zxbzDetail = await queryFormulaZxbzDataDetail({id});
      this.form.execNumber = zxbzDetail.zxbzh;
      this.zxbzDetail = zxbzDetail;
    },
    async getList() {
      let params = Object.assign({},this.queryParams)
      this.loading = true
      let res = await listSoftwareDevelopingFormula(params)
      this.loading = false
      this.softwareDevelopingFormulaList = res.rows
      this.total = res.total
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    async reset() {
      this.currentTab = 'base';
      this.stabilityDataList = [];
      this.relationStabilityDataList = [];
      this.activeNames = [];
      this.isCopy = 0;
      this.isLook = false;
      this.activeName = 'base';
      this.itemNames = [];
      this.formulaMaterialDatas = [];
      this.chooseFormulaMaterialDatas = [];
      this.pFormulaMapData = [];
      this.recipeChangeHistoryData = [];
      this.gyjsData = {};
      this.gyjsDataList = [];
      this.zfylDataList = [];
      this.gyjsBeianDataList = [];
      this.zfylBeianDataList = [];
      this.softwareFormulaSpecList = [];
      this.formulaTableDataList = [];
      this.formulaTableDataListBack = [];
      this.compositionTableDataList = [];
      this.compositionTableDataListBack = [];
      this.specMaterialDatas = [];
      this.itemArray = [];
      this.userItemArray = [];
      this.zxbzDetail = {};
      this.gtNumStr = '';
      this.ltNumStr = '';
      this.specObj = {};
      this.totalPercentVal = 0;
      this.specId = null;
      this.sjTotalPercet = 0;
      this.form = {
        id: null,
        currentTemplateId: null,
        relationMaterialCode: null,
        formulaName: null,
        englishName: null,
        materialCode: null,
        formulaCodeParams: null,
        price: null,
        weight: 100,
        isLock: 1,
        isMateral: null,
        status: "0",
        remark: null,
        operator: null,
        createdTime: null,
        isDel: null,
        lastModifiedTime: null,
        note: null,
        formulaCode: null,
        duliId: null,
        cirId: null,
        cirText: null,
        duliText: null,
        laboratoryCode: null,
        productName: null,
        brandId: null,
        customerCode: null,
        customerName: null,
        seriesName: null,
        appearance: null,
        colour: null,
        ph: null,
        viscosity: null,
        stabilityresult: null,
        gxgs: null,
        category: null,
        brandName: null,
        standard: null,
        introFile: [],
        organizationId: null,
        oldFormulaCode: null,
        copyFormulaId: null,
        addTips: null,
        wendingxingFile: [],
        gongyiFile: [],
        xiangrongxingFile: [],
        weishenwuFile: [],
        xiaofeizheFile: [],
        qitaFile: [],
        execNumber: null,
        isDraft: 1,
        gxxc: [],
        gxxcOther: [],
        zybw: [],
        syrq: [],
        cpjx: [],
        pflx: [],
        cpfldm: null,
        cosmeticClassification: null,
        cosmeticCase: null,
        execNumberId: null,
        aqpgjl: null,
        gongyijianshu: null,
        gongyijianshuBeian: null,
        ranfalei: [],
        cosmeticCaseFirst: [],
        cosmeticCaseSecond: [],
        qubanmeibailei: [],
        fangshailei: false,
        sfa: null,
        pa: null,
        yushousfa: null,
        xingongxiao: false,
        xingongxiaocontent: null,
        ftlTime: null,
        filCode: null,
        baCode: null,
        baTime: null,
        filCodeNote: null,
        baCodeNote: null,
        waxcName: null,
        waxcOthername: null,
        waxcStatus: null,
        baStatus: null,
        formulaPid: null,
        bpNote: null,
        zsTime: null,
        zsCode: null,
        gongyijianshuZs: null,
        yfFile: null,
        zsFile: null,
        isLove: null,
        upRate: null,
        oriPrice: null,
        levelNum: null,
        purpose: '普通',
        formulaStatus: 0,
        formulaRemark: null,
        projectNo: null,
        itemName: null,
        itemNameText: null,
        formulaImage: null,
        formulaConstructionIdeas: null,
        isRealse: null,
        materialStatusInfo: null,
        importCountryInfo: null,
        operatorName: null,
        stabilityStatus: null,
        isResult: null,
        isGt: null,
        type: null,
        weishenwuResult: null,
        weishenwuRemark: null,
        xiangrongxingResult: null,
        xiangrongxingRemark: null,
        wendingxingResult: null,
        wendingxingRemark: null,
        materialCycle: null
      };
      this.resetForm("form");

      if (!this.wxOptions.length) {
        this.wxOptions = await allTreeData({type: 9})
      }
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
    },
    handleFormulaMaterialSelectionChange(selection) {
      this.chooseFormulaMaterialDatas = selection;
    },
    async genPformulaInfo() {
      let chooseFormulaMaterialDatas = this.chooseFormulaMaterialDatas;
      if (chooseFormulaMaterialDatas && chooseFormulaMaterialDatas.length > 0) {
        for (let item of chooseFormulaMaterialDatas) {
          let type = item.type;
          if (type == 1) {
            this.msgError('生成错误,请选择原料编码信息');
            return;
          }
        }
        let id = this.form.id;
        let data = await generatePFormulaInfo({id,formulaMaterialDatas:JSON.stringify(chooseFormulaMaterialDatas)});
        this.msgSuccess("操作成功");
      } else {
        this.msgError('请选择原料信息!');
      }
    },
    limitDecimal(row){
       const inputValue = row.percentage;
      // 正则表达式匹配最多6位小数的数字
      const regex = /^\d*\.?\d{0,6}$/;
      if (regex.test(inputValue)) {
        this.lastValue = inputValue;
      } else {
        // 如果不符合条件，回退到上一个有效值
        row.percentage = this.lastValue;
      }
    },
    async generBMaterialInfo(id) {
       this.$confirm('您确定要生成B代码吗?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(async function () {
         let data = await generateBMaterialInfo({id});
       }).then(() => {
         this.msgSuccess("操作成功");
      }).catch(() => {});
    },
    async genNewformulaInfo() {
      let id = this.form.id;
      let res = await generateNewformulaInfo({id});
      this.msgSuccess("操作成功");
    },
    handleAdd() {
      this.reset();
      this.formulaTabs = [{
        title: '基础信息',
        code: 'base'
      }, {
        title: '配方页面',
        code: 'formulaMaterial'
      }, {
        title: '附件',
        code: 'formulaFile'
      }];
      this.open = true;
      this.title = "创建配方";
    },
    async handleUpdate(id,isLook) {
      this.reset();
      let formulaTabs = await queryLookFormulaTabs();
      this.formulaTabs = formulaTabs;
      this.btnLoading = true;
      getSoftwareDevelopingFormulaDetail(id).then(async response => {
        let form = response.data;
        let isEdit = true;
        let projectNo = form.projectNo;
        if(projectNo.indexOf("P")!=-1 ||projectNo=='210002089'||projectNo=='240000365'||projectNo=='240001042' || projectNo=='210002088' || projectNo=='220005457' || projectNo=='240000365'){
          isEdit = false;
        }
        if(form.isLock===2){
          isEdit = false;
        }
        this.isEdit = isEdit;
        if(form.fangshailei){
          form.fangshailei = form.fangshailei==1?true:false;
        }else{
          form.fangshailei = false;
        }
        if(form.xingongxiao){
          form.xingongxiao =form.xingongxiao==1?true:false;
        }else{
          form.xingongxiao = false;
        }
        let specObj = form.specObj;
        if(specObj){
          this.specObj = JSON.parse(specObj);
        }else{
          this.specObj = {};
        }
        let formulaObj = form.formulaObj;
        if(formulaObj){
          formulaObj = JSON.parse(formulaObj);
          this.totalPercentVal = formulaObj.totalPercentVal;
          this.sjTotalPercet = formulaObj.sjTotalPercet;
          if(formulaObj.dataList){
            this.formulaTableDataList = formulaObj.dataList;
            this.formulaTableDataListBack = formulaObj.dataList;
          }else{
            this.formulaTableDataList = [];
            this.formulaTableDataListBack = [];
          }
          if(formulaObj.allList){
            this.compositionTableDataList = formulaObj.allList;
            this.compositionTableDataListBack = formulaObj.allList;
          }else{
            this.compositionTableDataList = [];
            this.compositionTableDataListBack = [];
          }
          if(formulaObj.specMaterialData){
            this.specMaterialDatas = formulaObj.specMaterialData;
          }else{
            this.specMaterialDatas = [];
          }
          if(formulaObj.ltNumStr){
            this.ltNumStr = formulaObj.ltNumStr;
          }else{
            this.ltNumStr = '';
          }
          if(formulaObj.gtNumStr){
            this.gtNumStr = formulaObj.gtNumStr;
          }else{
            this.gtNumStr = '';
          }
        }
        //获取配方原料信息
        let formulaMaterialDatas = form.formulaMaterialDatas;
        if(formulaMaterialDatas){
           this.formulaMaterialDatas = JSON.parse(formulaMaterialDatas);
        }else{
          this.formulaMaterialDatas = [];
        }
        if (form.categoryText) {
          let categoryTextList = form.categoryText.split(',');
          let categoryIds = []
          for (let t of categoryTextList) {
            categoryIds.push(parseInt(t))
          }
          form.categoryText = categoryIds
        } else {
          form.categoryText = [];
        }
        if (form.cirText) {
          let cirTextList = form.cirText.split(',');
          let cirId = []
          for (let t of cirTextList) {
            cirId.push(parseInt(t))
          }
          form.cirText = cirId
        } else {
          form.cirText = [];
        }
        if (form.duliText) {
          let duliTextList = form.duliText.split(',');
          let duliId = []
          for (let t of duliTextList) {
            duliId.push(parseInt(t))
          }
          form.duliText = duliId
        } else {
          form.duliText = [];
        }
        if (form.gxxc) {
          form.gxxc = form.gxxc.split(",");
        } else {
          form.gxxc = [];
        }
        if (form.gxxcOther) {
          form.gxxcOther = form.gxxcOther.split(",");
        } else {
          form.gxxcOther = [];
        }
        if (form.zybw) {
          form.zybw = form.zybw.split(",");
        } else {
          form.zybw = [];
        }
        if (form.syrq) {
          form.syrq = form.syrq.split(",");
        } else {
          form.syrq = [];
        }
        if (form.cpjx) {
          form.cpjx = form.cpjx.split(",");
        } else {
          form.cpjx = [];
        }
        if (form.pflx) {
          form.pflx = form.pflx.split(",");
        } else {
          form.pflx = [];
        }
        if (form.ranfalei) {
          form.ranfalei = form.ranfalei.split(",");
        } else {
          form.ranfalei = [];
        }
        if (form.cosmeticCaseFirst) {
          form.cosmeticCaseFirst = form.cosmeticCaseFirst.split(",");
        } else {
          form.cosmeticCaseFirst = [];
        }
        if (form.cosmeticCaseSecond) {
          form.cosmeticCaseSecond = form.cosmeticCaseSecond.split(",");
        } else {
          form.cosmeticCaseSecond = [];
        }
        if (form.qubanmeibailei) {
          form.qubanmeibailei = form.qubanmeibailei.split(",");
        } else {
          form.qubanmeibailei = [];
        }
        if (form.introFile) {
          form.introFile = JSON.parse(form.introFile);
        } else {
          form.introFile = [];
        }
        if (form.wendingxingFile) {
          form.wendingxingFile = JSON.parse(form.wendingxingFile);
        } else {
          form.wendingxingFile = [];
        }
        if (form.gongyiFile) {
          form.gongyiFile = JSON.parse(form.gongyiFile);
        } else {
          form.gongyiFile = [];
        }
        if (form.xiangrongxingFile) {
          form.xiangrongxingFile = JSON.parse(form.xiangrongxingFile);
        } else {
          form.xiangrongxingFile = [];
        }
        if (form.weishenwuFile) {
          form.weishenwuFile = JSON.parse(form.weishenwuFile);
        } else {
          form.weishenwuFile = [];
        }
        if (form.xiaofeizheFile) {
          form.xiaofeizheFile = JSON.parse(form.xiaofeizheFile);
        } else {
          form.xiaofeizheFile = [];
        }
        if (form.qitaFile) {
          form.qitaFile = JSON.parse(form.qitaFile);
        } else {
          form.qitaFile = [];
        }
        let itemNames = [];
        if(form.itemArr){
          itemNames = form.itemArr;
        }
        this.itemNames = itemNames;

        let pFormulaMapData = [];
        if(form.pFormulaMapData){
          pFormulaMapData = form.pFormulaMapData;
        }
        this.pFormulaMapData = pFormulaMapData;
        //获取信息数据
        let execNumberId = form.execNumberId;
        if(execNumberId){
          this.zxbzChange(execNumberId);
        }else{
          this.zxbzDetail = {};
        }
        let jcxmJson = form.jcxmJson;
        if(jcxmJson){
           this.itemArray = JSON.parse(jcxmJson);
        }else{
          this.itemArray = [];
        }
        this.form = form;
        let gongyijianshu = form.gongyijianshu;
        if(gongyijianshu){
          let gyjsData = JSON.parse(gongyijianshu);
          let gyjs = gyjsData.gyjs;
          if(gyjs){
            this.gyjsDataList = gyjs.map(name => ({ name }));;
          }else{
            this.refreshFormulaLegalGy('0');
            this.gyjsDataList = [];
          }
          let zfyl = gyjsData.zfyl;
          if(gyjs){
            this.zfylDataList = zfyl.map(name => ({ name }));;
          }else{
            this.zfylDataList = [];
          }
        }else{
          this.gyjsDataList = [];
          this.zfylDataList = [];
          this.refreshFormulaLegalGy('0');
        }
        let gongyijianshuBeian = form.gongyijianshuBeian;
        if(gongyijianshuBeian){
          let gyjsData = JSON.parse(gongyijianshuBeian);
          let gyjs = gyjsData.gyjs;
          if(gyjs){
            this.gyjsBeianDataList = gyjs.map(name => ({ name }));;
          }else{
            this.gyjsBeianDataList = [];
          }
          let zfyl = gyjsData.zfyl;
          if(gyjs){
            this.zfylBeianDataList = zfyl.map(name => ({ name }));;
          }else{
            this.zfylBeianDataList = [];
          }
        }else{
          this.gyjsBeianDataList = [];
          this.zfylBeianDataList = [];
        }
        this.open = true;
        if(isLook){
          this.title = "修改配方";
        }else{
          this.title = "查看配方";
        }
        this.isLook = isLook;
        this.btnLoading = false;
      });
      let recipeChangeHistoryData = await queryFormualMaterialRecipeChangeHistoryData({id});
      this.recipeChangeHistoryData = recipeChangeHistoryData;
      //获取spec内容
      this.queryMaterialFormulaSpecDataList(id);
      //获取关联稳定性记录内容
      this.queryFormulaStabilityRecordDataList(id);
    },
    stabilityStatusFormat(row) {
      const arr = this.stabilityStatusOptions.filter(i=> i.value === row.stabilityStatus)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    ypFormat(row) {
      const arr = this.ypFromOptions.filter(i=> i.value === row.ypFrom)
      if(arr && arr[0]) {
        return arr[0].label
      }
    },
    async queryMaterialFormulaSpecDataList(id) {
      let softwareFormulaSpecList = await queryMaterialFormulaSpecDataList({id});
      this.softwareFormulaSpecList = softwareFormulaSpecList;
    },
    async queryFormulaStabilityRecordDataList(id) {
      let formulaStabilityObj = await queryFormulaStabilityRecordDataList({id});
      let relationStabilityDataList = formulaStabilityObj.relationStabilityDataList;
      let stabilityDataList = formulaStabilityObj.stabilityDataList;
      this.stabilityDataList = stabilityDataList
      this.relationStabilityDataList = relationStabilityDataList
    },
    async copyGongyi() {
      await this.$confirm('是否确认复制工艺数据,会清空已填数据!')
      this.gyjsBeianDataList = JSON.parse(JSON.stringify(this.gyjsDataList));
      this.zfylBeianDataList = JSON.parse(JSON.stringify(this.zfylDataList));
    },
    async submitUploadForm() {
      this.btnLoading = true;
      let formulaImage = this.form.formulaImage;
      let formulaConstructionIdeas = this.form.formulaConstructionIdeas;
      let id = this.form.id;
      let remark = this.form.remark;
      let formulaMaterialDatas = this.formulaMaterialDatas;
      formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);
      let params = {
        id,formulaImage,formulaMaterialDatas,remark,formulaConstructionIdeas
      };
      try {
        let res = await updateSoftwareDevelopingFormulaImg(params);
        this.msgSuccess('修改成功!');
        this.btnLoading = false;
      } catch (e) {
        this.btnLoading = false
      }
    },
    async submitUploadFileForm() {
      this.btnLoading = true;
      let form = this.form;
      let param = {};
      param.id = form.id;
      if (form.introFile) {
        param.introFile = JSON.stringify(form.introFile);
      } else {
        param.introFile = "";
      }
      if (form.wendingxingFile) {
        param.wendingxingFile = JSON.stringify(form.wendingxingFile);
      } else {
        param.wendingxingFile = "";
      }
      if (form.gongyiFile) {
        param.gongyiFile = JSON.stringify(form.gongyiFile);
      } else {
        param.gongyiFile = "";
      }
      if (form.xiangrongxingFile) {
        param.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);
      } else {
        param.xiangrongxingFile = "";
      }
      if (form.weishenwuFile) {
        param.weishenwuFile = JSON.stringify(form.weishenwuFile);
      } else {
        param.weishenwuFile = "";
      }
      if (form.xiaofeizheFile) {
        param.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);
      } else {
        param.xiaofeizheFile = "";
      }
      if (form.qitaFile) {
        param.qitaFile = JSON.stringify(form.qitaFile);
      } else {
        param.qitaFile = "";
      }
      param.wendingxingResult = form.wendingxingResult;
      param.wendingxingRemark = form.wendingxingRemark;
      param.xiangrongxingResult = form.xiangrongxingResult;
      param.xiangrongxingRemark = form.xiangrongxingRemark;
      param.weishenwuResult = form.weishenwuResult;
      param.weishenwuRemark = form.weishenwuRemark;
      try {
        let res = await updateSoftwareDevelopingFormulaFileImg(param);
        this.msgSuccess('修改成功!');
        this.btnLoading = false;
      } catch (e) {
        this.btnLoading = false
      }
    },
    async submitForm(isDraft) {
      if(isDraft===0){
        await this.$refs["form"].validate()
      }
      let form = Object.assign({},this.form);
      let projectNo = form.projectNo;
      if(!projectNo || projectNo.length==0){
        this.msgError('请选择项目');
        return;
      }
      let categoryText = form.categoryText;
      if(!categoryText || categoryText.length==0){
        this.msgError('请选择配方类别');
        return;
      }
      form.isDraft = isDraft;
      if(form.categoryText && form.categoryText.length > 0 && isArray(form.categoryText)) {
        form.categoryText = form.categoryText.join(',')
      }else{
        form.categoryText = '';
      }
      if(form.cirText && form.cirText.length > 0 && isArray(form.cirText)) {
        form.cirText = form.cirText.join(',')
      }else{
        form.cirText = '';
      }
      if(form.duliText && form.duliText.length > 0 && isArray(form.duliText)) {
        form.duliText = form.duliText.join(',')
      }else{
        form.duliText = '';
      }
      if(form.gxxc){
        form.gxxc = form.gxxc.join(',');
      }else{
        form.gxxc = '';
      }
      if(form.gxxcOther){
        form.gxxcOther = form.gxxcOther.join(',');
      }else{
        form.gxxcOther = '';
      }
      if(form.zybw){
        form.zybw = form.zybw.join(',');
      }else{
        form.zybw = '';
      }
      if(form.syrq){
        form.syrq = form.syrq.join(',');
      }else{
        form.syrq = '';
      }
      if(form.cpjx){
        form.cpjx = form.cpjx.join(',');
      }else{
        form.cpjx = '';
      }
      if(form.pflx){
        form.pflx = form.pflx.join(',');
      }else{
        form.pflx = '';
      }
      if(form.ranfalei){
        form.ranfalei = form.ranfalei.join(',');
      }else{
        form.ranfalei = '';
      }
      if(form.cosmeticCaseFirst){
        form.cosmeticCaseFirst = form.cosmeticCaseFirst.join(',');
      }else{
        form.cosmeticCaseFirst = '';
      }
      if(form.cosmeticCaseSecond){
        form.cosmeticCaseSecond = form.cosmeticCaseSecond.join(',');
      }else{
        form.cosmeticCaseSecond = '';
      }
      if(form.qubanmeibailei){
        form.qubanmeibailei = form.qubanmeibailei.join(',');
      }else{
        form.qubanmeibailei = '';
      }
      if(form.introFile){
        form.introFile = JSON.stringify(form.introFile);
      }else{
        form.introFile = '';
      }
      if(form.wendingxingFile){
        form.wendingxingFile = JSON.stringify(form.wendingxingFile);
      }else{
        form.wendingxingFile = '';
      }
      if(form.gongyiFile){
        form.gongyiFile = JSON.stringify(form.gongyiFile);
      }else{
        form.gongyiFile = '';
      }
      if(form.xiangrongxingFile){
        form.xiangrongxingFile = JSON.stringify(form.xiangrongxingFile);
      }else{
        form.xiangrongxingFile = '';
      }
      if(form.weishenwuFile){
        form.weishenwuFile = JSON.stringify(form.weishenwuFile);
      }else{
        form.weishenwuFile = '';
      }
      if(form.xiaofeizheFile){
        form.xiaofeizheFile = JSON.stringify(form.xiaofeizheFile);
      }else{
        form.xiaofeizheFile = '';
      }
      if(form.qitaFile){
        form.qitaFile = JSON.stringify(form.qitaFile);
      }else{
        form.qitaFile = '';
      }
      if(form.fangshailei){
        form.fangshailei = 1;
      }else{
        form.fangshailei = 0;
      }
      if(form.xingongxiao){
        form.xingongxiao = 1;
      }else{
        form.xingongxiao = 0;
      }
      let formulaMaterialDatas = this.formulaMaterialDatas;
      if(formulaMaterialDatas && formulaMaterialDatas.length>0){
        for(let item of formulaMaterialDatas){
          let designatedUse = item.designatedUse;
          let isRelation = item.isRelation;
          let isFx = item.isFx;
          let remark = item.remark;
          let relationCode = item.relationCode;
          let materialCode = item.materialCode;
          let appointCode = item.appointCode;
          if(designatedUse==='指定' && !appointCode){
            this.msgError('请选择指定原料!');
            return;
          }
          if(isRelation==1 && !remark){
            this.msgError('请输入使用代码['+materialCode+']的备注,存在推荐原料['+relationCode+']');
            return;
          }
          if(isFx==1){
            let msg = materialCode + "为护肤风险原料，请核实!";
            await this.$confirm(msg, "警告", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
          }
        }
        form.formulaMaterialDatas = JSON.stringify(formulaMaterialDatas);
        let returnObj = this.isRepeat(formulaMaterialDatas);
        let num = returnObj.num;
        if(num>0){
          let repeatCode = returnObj.repeatCode;
          await this.$confirm('存在重复原料'+repeatCode+',是否确认添加!')
        }
      }else{
        await this.$confirm('您还没有选择原料，确定添加配方？');
        form.formulaMaterialDatas = '';
      }
      if(!form.pflx && isDraft===0){
         this.msgError('请选择使用方法!');
         return;
      }
      if (form.id != null) {
        try {
          this.btnLoading = true
          await updateSoftwareDevelopingFormula(form)
          this.btnLoading = false
          this.form.currentVersion = parseFloat(this.form.currentVersion) + 1;
          this.msgSuccess("修改成功")
          //this.close();
        } catch (e) {
          this.btnLoading = false
        }
      } else {
        try {
          this.btnLoading = true
          await addSoftwareDevelopingFormula(form)
          this.btnLoading = false
          this.msgSuccess("新增成功")
          this.close();
         } catch (e) {
          this.btnLoading = false
        }
      }
    },
    close() {
      this.$store.dispatch("tagsView/delView", this.$route);
      let view = {
        fullPath : '/rd/softwareDevelopingFormula',
        name:"SoftwareDevelopingFormula",
        path:"/rd/softwareDevelopingFormula",
        title:"研发配方"
      };
      this.$store.dispatch('tagsView/delCachedView', view).then(() => {
        const { fullPath } = view
        this.$nextTick(() => {
          this.$router.replace({
            path: '/redirect' + fullPath
          })
        })
      })
    },
    //判断是否重复
    isRepeat(datas){
       let returnObj = {num:0,repeatCode:''};
       let repeatCodesSet = new Set();
      if(datas && datas.length>0){
         let codes = [];
         for(let item of datas){
            codes.push(item.materialCode);
         }
         for(let code of codes){
             let index = 0;
             for(let item of datas){
                 let materialCode = item.materialCode;
                 if(code === materialCode){
                   index++;
                 }
             }
             if(index>1){
               repeatCodesSet.add(code);
             }
         }
       }
       if(repeatCodesSet && repeatCodesSet.size>0){
         let str = JSON.stringify(Array.from(repeatCodesSet));
         returnObj = {num:1,repeatCode:str};
       }
       return returnObj;
    },
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除研发配方编号为"' + ids + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delSoftwareDevelopingFormula(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(() => {});
    },
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有研发配方数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.exportLoading = true;
          return exportSoftwareDevelopingFormula(queryParams);
        }).then(response => {
          this.download(response.msg);
          this.exportLoading = false;
        }).catch(() => {});
    },
    async queryMaterialCode() {
      let materialCode = this.form.materialCode;
      let formulaMaterialDatas = this.formulaMaterialDatas;
      if (materialCode) {
        let res = await getRawMaterialInfoByCode({materialCode});
        if(res.data){
          let isRelation = res.data.isRelation;
           if(isRelation==1){
             let tipsInfo = res.data.tipsInfo;
             this.msgInfo(tipsInfo);
          }
          formulaMaterialDatas.unshift(res.data);
        }
        this.formulaMaterialDatas = formulaMaterialDatas;
        this.codeChange(1);
      } else {
        this.msgError('请输入原料代码!');
      }
    },
    async queryFormulaCode() {
      let formulaCodeParams = this.form.formulaCodeParams;
      let formulaMaterialDatas = this.formulaMaterialDatas;
      if (formulaCodeParams) {
        let res = await getFormulaInfoByCode({formulaCode:formulaCodeParams});
         if(res.data){
          formulaMaterialDatas.unshift(res.data);
          }
         this.formulaMaterialDatas = formulaMaterialDatas;
      } else {
        this.msgError('请输入配方编码!');
      }
    },
    async confirmSelectGoods() {
      let formulaCodeParams = this.form.formulaCodeParams;
      let formulaMaterialDatas = this.formulaMaterialDatas;
      if (formulaCodeParams) {
        let res = await getFormulaLabNoInfoByCode({laboratoryCode:formulaCodeParams});
         if(res.data){
           formulaMaterialDatas.push(res.data);
         }
         this.formulaMaterialDatas = formulaMaterialDatas;
      } else {
        this.msgError('请输入实验室编码!');
      }
    },
    async submitSpec(){
      let specObj = {};
      let form = this.form;
      let itemArray = this.itemArray;
      if(!form.execNumberId){
        this.msgError('请选择执行标准/标准名称');
        return;
      }
      if(!(itemArray &&itemArray.length>0)){
        this.msgError('请选择标准模板');
        return;
      }
      specObj.execNumberId = form.execNumberId;
      specObj.execNumber = form.execNumber;
      specObj.currentTemplateId = this.form.currentTemplateId;
      specObj.formulaId = form.id;
      specObj.itemArray = itemArray;
      specObj.isLock = form.isLock;
      specObj.formulaCode = form.formulaCode;
      specObj.laboratoryCode = form.laboratoryCode;
      specObj.productName = form.productName;
      this.btnLoading = true;
      try{
        let res = await addSoftwareDevelopingFormulaSpecZxbz({specObj:JSON.stringify(specObj)});
        this.msgSuccess('添加成功!');
        this.btnLoading = false;
      }catch(e){
        this.btnLoading = false;
      }
    },
    async submitUserSpec(){
      let specObj = {};
      let form = this.form;
      let itemArray = this.userItemArray;
      if(!form.type){
        this.msgError('请选择样品来源');
        return;
      }
      specObj.formulaId = form.id;
      specObj.specId = this.specId;
      specObj.itemArray = itemArray;
      specObj.type = form.type;
      this.btnLoading = true;
      try{
        let res = await addSoftwareDevelopingUserFormulaSpecZxbz({specObj:JSON.stringify(specObj)});
        this.msgSuccess('添加成功!');
        this.btnLoading = false;
        this.specOpen = false;
        this.queryMaterialFormulaSpecDataList(form.id);
      }catch(e){
        this.btnLoading = false;
      }
    },
    async delFormulaMaterial(row){
      this.formulaMaterialDatas = this.formulaMaterialDatas.filter(x => {
        return x.key != row.key;
      });
    },
    categoryChange(id){
       let form = this.form;
       let ranfalei = form.ranfalei;
       let qubanmeibailei = form.qubanmeibailei;
       let fangshailei = form.fangshailei;
       let sfa = form.sfa;
       let pa = form.pa;
       let yushousfa = form.yushousfa;
       let xingongxiao = form.xingongxiao;
       let xingongxiaocontent = form.xingongxiaocontent;
      if(ranfalei.length>0
      ||qubanmeibailei.length>0
      ||fangshailei
      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){
        this.form.cosmeticClassification = '1';
        this.form.cosmeticCase = '1';
        let cosmeticCaseFirst = [];
        if(!cosmeticCaseFirst.includes('1')){
          cosmeticCaseFirst.push('1');
          this.form.cosmeticCaseFirst = cosmeticCaseFirst;
        }
      }else{
        this.form.cosmeticClassification = '';
        this.form.cosmeticCase = '';
        this.codeChange(1);
      }
    },
    categoryChangeNew(id){
       let res = 1;
       let form = this.form;
       let ranfalei = form.ranfalei;
       let qubanmeibailei = form.qubanmeibailei;
       let fangshailei = form.fangshailei;
       let sfa = form.sfa;
       let pa = form.pa;
       let yushousfa = form.yushousfa;
       let xingongxiao = form.xingongxiao;
       let xingongxiaocontent = form.xingongxiaocontent;
       if(ranfalei.length>0
      ||qubanmeibailei.length>0
      ||fangshailei
      ||sfa || pa || yushousfa || xingongxiao ||xingongxiaocontent){
        this.form.cosmeticClassification = '1';
        this.form.cosmeticCase = '1';
        res = 3;
      }else{
        this.form.cosmeticClassification = '';
        this.form.cosmeticCase = '';
        res = 2;
      }
      return res;
    },
    async codeChange(type) {
      let code = []
      let form = this.form
      if (form.gxxc.length > 0) {
        code.push(this.efficacyOptions.filter(i => form.gxxc.includes(i.id))
          .sort((n1, n2) => n1.id - n2.id).map(i => i.id).join('/'))
      }
      if (form.zybw.length > 0) {
        code.push(this.zybwOptions.filter(i => form.zybw.includes(i.id)).sort((n1, n2) => n1.id - n2.id)
          .map(i => i.id).join('/'))
      }
      if (form.cpjx.length > 0) {
        code.push(this.cpjxOptions.filter(i => form.cpjx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)
          .map(i => i.id).join('/'))
      }
      if (form.syrq.length > 0) {
        code.push(this.syrqOptions.filter(i => form.syrq.includes(i.id)).sort((n1, n2) => n1.id - n2.id)
          .map(i => i.id).join('/'))
      }
      if (form.pflx.length > 0) {
        code.push(this.syffOptions.filter(i => form.pflx.includes(i.id)).sort((n1, n2) => n1.id - n2.id)
          .map(i => i.id).join('/'))
      }
      this.form.cpfldm = code.join('~')

      if (type == 1) {
        let cosmeticClassification = "";
        let cosmeticCase = "";
        let gxxc = form.gxxc;
        let gxxc1 = ['A', '3', '4', '1', '2', '5'];
        let gxxc2 = ['14', '15', '19', '6', '23', '25'];

        let zybw = form.zybw;
        let zybw1 = ['B'];

        let cpjx = form.cpjx;
        let cpjx2 = ['9', '10'];

        let syrq = form.syrq;
        let syrq1 = ['C', '1', '2'];
        let syrq2 = ['1', '2'];
        let isProcess = true;
        let cosmeticCaseFirst = [];
        let cosmeticCaseSecond = [];
        if (this.arrayContainsAnother(syrq, syrq1)) {
          if(this.arrayContainsAnother(syrq,syrq2)){
            if(!cosmeticCaseFirst.includes('2')){
              cosmeticCaseFirst.push('2');
            }
          }
        }
        if (this.arrayContainsAnother(gxxc, gxxc2)){
          if(!cosmeticCaseSecond.includes('3')){
            cosmeticCaseSecond.push('3');
          }
        }
        if (this.arrayContainsAnother(cpjx, cpjx2)){
          if(!cosmeticCaseSecond.includes('4')){
            cosmeticCaseSecond.push('4');
          }
        }
        if (this.arrayContainsAnother(gxxc, gxxc1)) {
          cosmeticClassification = '1';
        } else if (this.arrayContainsAnother(zybw, zybw1)) {
          cosmeticClassification = '1';
        } else if (this.arrayContainsAnother(syrq, syrq1)) {
          cosmeticClassification = '1';
        } else if (this.arrayContainsAnother(gxxc, gxxc2)) {
          cosmeticClassification = '2';
          cosmeticCase = '1';
        } else if (this.arrayContainsAnother(cpjx, cpjx2)) {
          cosmeticClassification = '2';
          cosmeticCase = '1';
        } else {
          cosmeticClassification = '2';
          cosmeticCase = '2';
          let res = await this.categoryChangeNew(1);
          if(res===1 || res ===3){
            isProcess = false;
          }else{
            let formulaMaterialDatas = this.formulaMaterialDatas;
            let isFirst = false;
            let isSecond = false;
            for(let item of formulaMaterialDatas){
                let isNewMaterial = item.isNewMaterial;
                let inicNmjyl = item.inicNmjyl;
                if(isNewMaterial=='是'){
                  isFirst = true;
                }else if(inicNmjyl=='是'){
                  isSecond = true;
                }
            }
            if(isFirst){
              cosmeticClassification = '1';
              isProcess = true;
            }else if(isSecond){
              cosmeticClassification = '2';
              cosmeticCase = '1';
              isProcess = true;
            }
          }
        }
        let res1 = await this.categoryChangeNew(1);
        if(res1==3){
          if(!cosmeticCaseFirst.includes('1')){
            cosmeticCaseFirst.push('1');
          }
        }
        let formulaMaterialDatas = this.formulaMaterialDatas;
        for(let item of formulaMaterialDatas){
          let isNewMaterial = item.isNewMaterial;
          let inicNmjyl = item.inicNmjyl;
          let symdInfo = item.symdInfo;
          if(isNewMaterial=='是'){
            if(!cosmeticCaseFirst.includes('3')){
              cosmeticCaseFirst.push('3');
            }
          }
          if(inicNmjyl=='是'){
            if(!cosmeticCaseSecond.includes('1')){
              cosmeticCaseSecond.push('1');
            }
          }
          if(symdInfo.indexOf('防晒剂')!=-1 ||symdInfo.indexOf('光稳定剂')!=-1 ){
            if(!cosmeticCaseSecond.includes('2')){
              cosmeticCaseSecond.push('2');
            }
          }
        }
        this.form.cosmeticCaseFirst = cosmeticCaseFirst;
        this.form.cosmeticCaseSecond = cosmeticCaseSecond;
        if(isProcess){
          this.form.cosmeticClassification = cosmeticClassification;
          this.form.cosmeticCase = cosmeticCase;
        }

      }
    },
    arrayContainsAnother(arr1, arr2) {
      return arr1.some(item => arr2.includes(item));
    },
    toChoose(){
       this.visible = true;
    },
    async selected(formulaId, laboratoryCode) {
      this.form.oldFormulaCode = laboratoryCode;
      this.form.copyFormulaId = formulaId;
      this.visible = false;
      let formulaMaterialDatas = [];
      if (laboratoryCode && formulaId) {
        let res = await getFormulaLabNoInfoByCode({id: formulaId});
        if (res.data) {
          if(res.data.dataList){
            formulaMaterialDatas = res.data.dataList;
          }
          if(res.data.tips){
            this.form.addTips = res.data.tips;
          }
        }
        this.formulaMaterialDatas = formulaMaterialDatas;
        if(formulaMaterialDatas && formulaMaterialDatas.length>0){
          this.codeChange(1);
        }
      } else {
        this.msgError('请选择配方!');
      }
    },
    handleFormulaSpecAdd(){
       this.specOpen = true;
       this.userItemArray = [];
       this.specId = null;
       this.userItemArray = this.itemArray;
    },
    async handleFormulaSpecEdit(row) {
      this.specOpen = true;
      let dataObj = await queryMaterialFormulaSpecDataDetail({id:row.id});
      let jcxmJson = dataObj.jcxmJson;
      if(jcxmJson){
        this.userItemArray = JSON.parse(jcxmJson);
      }else{
        this.userItemArray = [];
      }
      this.form.type = dataObj.type+'';
      this.specId = dataObj.id;
    },
    async refreshFormulaLegalGy(type){
      if(type==='1'){
        await this.$confirm('是否刷新工艺数据,会清空已填数据!')
      }
       let id = this.form.id;
       this.btnLoading = true;
       let data = await queryFormulaLegalGy({id});
       let gyjsData = data.data;
        let gyjs = gyjsData.gyjs;
        if(gyjs){
          this.gyjsDataList = gyjs.map(name => ({ name }));;
        }else{
          this.gyjsDataList = [];
        }
        let zfyl = gyjsData.zfyl;
        if(gyjs){
          this.zfylDataList = zfyl.map(name => ({ name }));;
        }else{
          this.zfylDataList = [];
        }
       this.btnLoading = false;
    },
    //提交特殊原料信息
    async submitTipsMaterialFormulaInfo() {
      let formulaId = this.form.id;
      let specMaterialDatas = this.specMaterialDatas;
      this.btnLoading = true;
      try {
        let res = await addFormulaSpecMaterialData({id:formulaId,specMaterialDatas: JSON.stringify(specMaterialDatas)});
        this.msgSuccess('操作成功!');
        this.btnLoading = false;
      } catch (e) {
        this.btnLoading = false;
      }
    },
    //提交配方使用目的
    async submitSymdInfo() {
      let formulaId = this.form.id;
      this.btnLoading = true;
      try {
        let formulaSymd = [];
        let compositionTableDataList = this.compositionTableDataList;
        for(let item of compositionTableDataList){
          formulaSymd.push({
            chiName:item.chiName,
            cppfSymd:item.cppfSymd,
          });
        }
        let res = await addFormulaSymdForm({id:formulaId,formulaSymd: JSON.stringify(compositionTableDataList)});
        this.msgSuccess('操作成功!');
        this.btnLoading = false;
      } catch (e) {
        this.btnLoading = false;
      }
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if(!['比例(%)'].includes(column.label)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return this.keepDigits(prev + curr,10);
            } else {
              return this.keepDigits(prev,10);
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    getSummariesPFormula(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if(!['比例'].includes(column.label)) {
          sums[index] = '';
          return;
        }
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return this.keepDigits(prev + curr,10);
            } else {
              return this.keepDigits(prev,10);
            }
          }, 0);
          sums[index] += '';
        } else {
          sums[index] = '';
        }
      });
      return sums;
    },
    formulaMaterialBack(o) {
      let status = o.row.status;
      if(status){
        if(status==3){
          return {
            background: 'orange'
          }
        }else if(status==4 || status==5){
          return {
            background: '#8cc0a8'
            // background: '#9999ff'
          }
        }else if(status>0){
          return {
            background: 'red'
          }
        }
      }
    },
    compositionTableStyle(o) {
      let percert = o.row.percert;
      let isColor = o.row.isColor;
      if(isColor==1){
        return {
          color: 'red'
        }
      }else{
        if(percert<=0.1){
          return {
            color: 'blue'
          }
        }
      }
    },
    compositionCellTableStyle({ row, column }) {
      if (column.label === '中文名称' || column.label === 'INCI 中文名') {
        if (row.isTips===1) {
          return "background:#9966FF";
        }
      }
    },
    materialDetails(row){
      if(row.type==0){
        this.$nextTick(async () => {
          this.$refs.softwareMaterialSave.reset();
          this.$refs.softwareMaterialSave.init();
          let dataRow = {id:row.materialId};
          this.$refs.softwareMaterialSave.handleUpdate(dataRow,'查看原料',0);
          this.$refs.softwareMaterialSave.open = true;
        });
      }
    },
    selectable(row,index){
      if (row.isUse == 1) {
        return false
      } else {
        return true
      }
    },
    async addFormulaGyjsBeianInfo(type) {
      let gongyijianshuBeian = {};
      gongyijianshuBeian.gyjs = this.gyjsDataList.map(item => item.name);
      gongyijianshuBeian.zfyl = this.zfylDataList.map(item => item.name);
      let param = {
        id: this.form.id,
        gongyijianshu: JSON.stringify(gongyijianshuBeian),
        type
      };
      if(type===1){
        gongyijianshuBeian.gyjs = this.gyjsBeianDataList.map(item => item.name);
        gongyijianshuBeian.zfyl = this.zfylBeianDataList.map(item => item.name);
        param = {
          id: this.form.id,
          gongyijianshuBeian: JSON.stringify(gongyijianshuBeian),
          type
        };
      }
      let res = await addFormulaGyjsBeianInfo(param);
      this.msgSuccess('保存成功!');
    },
    async changeFun(){
      this.changeTemplate(0);
    },
    async changeTemplate(type) {
      if(this.form.currentTemplateId) {
        try {
          if(type===1){
            await this.$confirm('是否确认带入,会清空已填数据!')
          }
          this.btnLoading = true
          const res = await getBcpTemplate(this.form.currentTemplateId)
          if (res.code === 200 && res.data && res.data.itemArray) {
            let itemArray = JSON.parse(res.data.itemArray);
            for(let item of itemArray){
              item.standardVal = '';
            }
            this.itemArray = itemArray;
          }
          this.btnLoading = false
        } catch (e) {
          this.btnLoading = false
        }
      }
    },
    delItem(i) {
      this.itemArray.splice(i,1)
    },
    selectProject() {
      this.categoryOpen = true
      this.xmIds = []
    },
    selectCategory(category) {
      for (const item of category.array) {
        this.selectXm(item.id)
      }
    },
    selectXm(id) {
      if(this.xmIds.includes(id)) {
        this.xmIds = this.xmIds.filter(i=>i !== id)
      } else {
        this.xmIds.push(id)
      }
    },
    confirmXm() {
      if(this.xmIds.length) {
        let arr = this.jcXmList.filter(i=> this.xmIds.includes(i.id))
        const itemArray = this.itemArray
        for (const a of arr) {
          if(!itemArray.map(i=>i.id).includes(a.id)) {
            const o = {
              id: a.id,
              label: a.title,
              type: a.category,
              serialNo: a.seq,
              standard: a.standard,
              frequency: a.frequency,
              standardVal: a.standardVal,
              yqIds: a.yqIds?a.yqIds.split(',').map(i=>Number(i)):[],
            }
            let methodArray = []
            const dataArray = []
            if(a.methodDesc) {
              const methodTemplate = a.methodDesc.replace(/（/g,'(').replace(/）/g,')').replace(/\s/g,"")
              o.methodTemplate = methodTemplate.replace(/\(\{param}\)/g,'_____')
              methodArray = o.methodTemplate.split('_____')
              for (let i = 0; i < methodArray.length -1; i++) {
                const o = {}
                o['param_ava_' + i] = ''
                dataArray.push(o)
              }
            }
            o.methodArray = methodArray
            o.dataArray = dataArray
            itemArray.push(o)
          }
        }
        itemArray.sort((a,b)=>a.serialNo - b.serialNo)
        this.categoryOpen = false
      }
    },
    async handleShare(row) {
      this.shareOpen = true;
      let shareDeptDatas = await queryFormulaShareDeptDataList({id:row.deptId});
      this.shareDeptDatas = shareDeptDatas;
      let shareDeptIds = await queryFormulaShareDeptDataDetail({id:row.id});
      this.shareDeptIds = shareDeptIds;
      let checkRow = {
        id:row.id,
        deptId:row.deptId
      };
      this.checkRow = checkRow;
     },
    async submitShareFormulaInfo(){
       let shareDeptIds = this.shareDeptIds;
       if(shareDeptIds && shareDeptIds.length>0){
          this.btnLoading = true;
          let checkRow = this.checkRow;
          checkRow.shareDeptIds = shareDeptIds.join(",");
          let res = await addFormulaShareDataInfo(checkRow);
          this.shareOpen = false;
          this.btnLoading = false;
          this.msgSuccess('操作成功');
       }else {
          this.msgError('请选择要分享的部门!');
       }
    },
    handleCompositionQuery(){
       let ewgColor = this.queryParams.ewgColor;
       let comclusionType = this.queryParams.comclusionType;
       let compositionTableDataList = this.compositionTableDataListBack;
       if(ewgColor){
         compositionTableDataList = compositionTableDataList.filter(i=>i.dataObj.ewgColor === ewgColor);
       }
       if(comclusionType){
         compositionTableDataList = compositionTableDataList.filter(i=>i.componentType === comclusionType);
       }
       this.compositionTableDataList = compositionTableDataList;
    },
    resetCompositionQuery(){
      this.compositionTableDataList = this.compositionTableDataListBack;
      this.queryParams.ewgColor = null;
      this.queryParams.comclusionType = null;
    },
    handleMaterialQuery(){
       let comclusionType = this.queryParams.comclusionType;
       let formulaTableDataList = this.formulaTableDataListBack;
       if(comclusionType){
         formulaTableDataList = formulaTableDataList.filter(i=>i.componentType === comclusionType);
       }
       this.formulaTableDataList = formulaTableDataList;
    },
    resetMaterialQuery(){
      this.formulaTableDataList = this.formulaTableDataListBack;
      this.queryParams.comclusionType = null;
    },
    isEditStandard(id){
       let isOpr = true;
       let arr = [1,2,7];
       if(arr.includes(id)){
         isOpr = false;
       }
       return isOpr;
    }
  }
};
</script>
<style scoped lang="scss">
.select-wrapper {
  .item {
    height: 24px;
    padding: 5px 10px;
    font-size: 12px;
    border: 1px solid #DCDFE6;
    border-radius: 2px;
    box-shadow: 0 0 35px 0 rgb(154 161 171 / 15%);
    margin-top: 5px;
    cursor: pointer;
  }

  .selected {
    color: #00afff;
  }
}
</style>
